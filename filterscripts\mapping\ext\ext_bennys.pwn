CreateBennysExt()
{
    new STREAMER_TAG_OBJECT: bennyxadaws;
    //handover
    bennyxadaws = CreateDynamicObject(19463, 222.737762, -251.926757, 2.135602, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 3922, "bistro", "sw_wallbrick_01", 0x00000000);
    bennyxadaws = CreateDynamicObject(19463, 214.887908, -224.126556, 2.985602, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 9515, "bigboxtemp1", "ws_garagedoor3_white", 0x00000000);
    bennyxadaws = CreateDynamicObject(1897, 207.940429, -226.127426, 0.820544, 89.999992, 1145.247680, -15.747509, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 2772, "airp_prop", "CJ_red_COUNTER", 0x00000000);
    bennyxadaws = CreateDynamicObject(1897, 207.145629, -225.488754, 2.000555, 0.000025, 720.000000, 24.499994, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 2772, "airp_prop", "CJ_red_COUNTER", 0x00000000);
    bennyxadaws = CreateDynamicObject(1897, 207.100585, -226.511032, 0.820544, 89.999992, 1095.246826, -15.746891, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 2772, "airp_prop", "CJ_red_COUNTER", 0x00000000);
    bennyxadaws = CreateDynamicObject(1897, 207.315063, -225.800216, 1.444401, 29.999952, 900.000000, 24.500005, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 2772, "airp_prop", "CJ_red_COUNTER", 0x00000000);
    bennyxadaws = CreateDynamicObject(1897, 207.552978, -226.380661, 2.980570, 89.999992, 1840.246337, -15.746650, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 2772, "airp_prop", "CJ_red_COUNTER", 0x00000000);
    bennyxadaws = CreateDynamicObject(16332, 207.966674, -227.366317, 2.528761, 0.000032, 0.000032, -65.500236, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, -1, "none", "none", 0xFFFFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 2, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    bennyxadaws = CreateDynamicObject(19948, 207.092163, -225.350387, 0.769244, 0.000032, 30.200048, -65.500236, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(bennyxadaws, 1, 7103, "vgnplantgen", "metalwheel5_128", 0xFFFFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 2, 19480, "signsurf", "sign", 0x00000000);
    bennyxadaws = CreateDynamicObject(16332, 207.771362, -226.937973, 3.158767, 0.000032, 90.000083, -65.500236, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 19480, "signsurf", "sign", 0xFFFFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 2, 19480, "signsurf", "sign", 0x00000000);
    bennyxadaws = CreateDynamicObject(16332, 207.460327, -226.254806, 3.158767, 0.000032, 90.000083, -65.500236, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 19480, "signsurf", "sign", 0xFFFFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 2, 19480, "signsurf", "sign", 0x00000000);
    bennyxadaws = CreateDynamicObject(16332, 207.153076, -225.580978, 3.158767, 0.000032, 90.000083, -65.500236, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 19480, "signsurf", "sign", 0xFFFFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 2, 19480, "signsurf", "sign", 0x00000000);
    bennyxadaws = CreateDynamicObject(19873, 207.110839, -225.383590, 3.095903, 0.000032, 89.999946, 24.499963, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 14548, "ab_cargo_int", "metal_frame", 0xFFFFFFFF);
    bennyxadaws = CreateDynamicObject(16332, 207.053710, -225.363204, 1.400032, 0.000032, 180.000000, -65.500236, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 19480, "signsurf", "sign", 0xFFFFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 2, 19480, "signsurf", "sign", 0x00000000);
    bennyxadaws = CreateDynamicObject(16332, 207.053710, -225.363204, 2.150763, 0.000032, 180.000000, -65.500236, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 19480, "signsurf", "sign", 0xFFFFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 2, 19480, "signsurf", "sign", 0x00000000);
    bennyxadaws = CreateDynamicObject(16332, 207.053710, -225.363204, 2.891485, 0.000032, 180.000000, -65.500236, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 19480, "signsurf", "sign", 0xFFFFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 2, 19480, "signsurf", "sign", 0x00000000);
    bennyxadaws = CreateDynamicObject(19873, 207.106811, -225.374496, 0.925902, 0.000032, 89.999946, 24.499963, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 14548, "ab_cargo_int", "metal_frame", 0xFFFFFFFF);
    bennyxadaws = CreateDynamicObject(19873, 207.497314, -226.231124, 2.435896, 0.000032, 89.999946, 24.499963, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 1560, "7_11_door", "cj_sheetmetal2", 0xFFFFFFFF);
    bennyxadaws = CreateDynamicObject(1974, 207.632202, -226.529708, 3.025474, 0.000032, 28.000036, -65.500236, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 1560, "7_11_door", "cj_sheetmetal2", 0xFFFFFFFF);
    bennyxadaws = CreateDynamicObject(19087, 203.235351, -231.038391, 3.962414, 0.000058, 89.999992, 89.499572, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    bennyxadaws = CreateDynamicObject(19087, 202.724914, -231.034118, 3.722411, 0.000058, 89.999992, 89.499572, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    bennyxadaws = CreateDynamicObject(19087, 210.813415, -226.106307, 3.962414, 0.000057, 89.999992, -0.500465, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    bennyxadaws = CreateDynamicObject(19087, 210.817687, -225.595870, 3.722411, 0.000057, 89.999992, -0.500465, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    bennyxadaws = CreateDynamicObject(19087, 202.442306, -227.511428, 5.591921, 0.000047, -0.000058, -123.400344, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 16640, "a51", "concreteyellow256 copy", 0x00000000);
    bennyxadaws = CreateDynamicObject(1952, 202.441101, -227.513137, 3.127857, -0.000047, 990.000000, 56.599987, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(bennyxadaws, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(bennyxadaws, 2, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    bennyxadaws = CreateDynamicObject(1974, 202.448303, -227.504409, 3.147830, 0.000047, -0.000066, -123.400344, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    bennyxadaws = CreateDynamicObject(19940, 209.515594, -234.885513, 1.168476, 0.000007, 0.000007, 89.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19793, 210.252227, -235.051422, 0.918048, 0.000007, 124.200004, 89.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 1, 14652, "ab_trukstpa", "wood01", 0x00000000);
    bennyxadaws = CreateDynamicObject(19793, 210.252227, -234.732818, 0.917443, 0.000007, 57.199981, 89.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 1, 14652, "ab_trukstpa", "wood01", 0x00000000);
    bennyxadaws = CreateDynamicObject(19793, 208.042205, -235.051422, 0.918048, 0.000007, 124.200012, 89.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 1, 14652, "ab_trukstpa", "wood01", 0x00000000);
    bennyxadaws = CreateDynamicObject(19793, 208.042205, -234.732818, 0.917443, 0.000007, 57.199989, 89.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 1, 14652, "ab_trukstpa", "wood01", 0x00000000);
    bennyxadaws = CreateDynamicObject(19940, 208.735473, -234.875503, 1.178476, 0.000007, 0.000007, 89.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19940, 214.475631, -234.885513, 1.168476, 0.000015, 0.000007, 89.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19793, 215.212265, -235.051422, 0.918048, 0.000015, 124.200004, 89.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 1, 14652, "ab_trukstpa", "wood01", 0x00000000);
    bennyxadaws = CreateDynamicObject(19793, 215.212265, -234.732818, 0.917443, 0.000015, 57.199981, 89.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 1, 14652, "ab_trukstpa", "wood01", 0x00000000);
    bennyxadaws = CreateDynamicObject(19793, 213.002243, -235.051422, 0.918048, 0.000015, 124.200012, 89.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 1, 14652, "ab_trukstpa", "wood01", 0x00000000);
    bennyxadaws = CreateDynamicObject(19793, 213.002243, -234.732818, 0.917443, 0.000015, 57.199989, 89.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 1, 14652, "ab_trukstpa", "wood01", 0x00000000);
    bennyxadaws = CreateDynamicObject(19940, 213.695510, -234.875503, 1.178476, 0.000015, 0.000007, 89.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    bennyxadaws = CreateDynamicObject(2267, 197.957107, -230.059616, 3.518617, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 1, 14530, "estate2", "Auto_monstera", 0x00000000);
    bennyxadaws = CreateDynamicObject(19479, 221.340545, -263.188476, 12.950587, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(bennyxadaws, 0, "Handover\nMotorworks", 130, "Monotype Corsiva", 65, 1, 0xFFFFFFFF, 0xFF000000, 1);
    bennyxadaws = CreateDynamicObject(19479, 221.870269, -263.188476, 12.950587, 0.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(bennyxadaws, 0, "Handover\nMotorworks", 130, "Monotype Corsiva", 65, 1, 0xFFFFFFFF, 0xFF000000, 1);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(19463, 192.877929, -241.096633, -0.264397, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19463, 192.897918, -267.056518, -0.264397, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(4100, 192.898544, -264.654693, 3.008409, 0.000023, 0.000019, 50.200016, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19463, 192.887924, -260.356658, -0.254397, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19463, 197.767837, -271.786773, -0.254397, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19463, 207.387878, -271.786773, -0.254397, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19463, 217.008087, -271.786773, -0.254397, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(4100, 192.898544, -242.944656, 3.008409, 0.000000, 0.000000, 50.200035, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19463, 192.877929, -250.736785, -0.264397, 0.000000, 0.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(4100, 192.898544, -256.674682, 3.008409, 0.000017, 0.000014, 50.200016, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(4100, 199.488235, -271.794647, 3.008409, 0.000039, 0.000012, 140.199996, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(4100, 213.218505, -271.794647, 3.008409, 0.000043, 0.000006, 140.199996, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19872, 204.619445, -227.705642, -1.101003, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19872, 217.879653, -230.845153, -1.101003, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 202.095336, -233.451599, 3.862399, -89.999992, -493.586608, 135.913101, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 202.116943, -230.981506, 3.862399, -89.999992, -493.586608, 135.913101, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1081, 202.706787, -233.202209, 4.202379, -0.000057, 0.000032, -90.500053, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1081, 202.710205, -232.812072, 4.202379, -0.000057, 0.000032, -90.500053, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1081, 202.713806, -232.411926, 4.202379, -0.000057, 0.000032, -90.500053, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1081, 202.717407, -232.001647, 4.202379, -0.000057, 0.000032, -90.500053, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1081, 202.720581, -231.611511, 4.202379, -0.000057, 0.000032, -90.500053, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1081, 202.723937, -231.231262, 4.202379, -0.000057, 0.000032, -90.500053, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 208.400207, -224.966293, 3.862399, -89.999992, -735.252624, 164.247100, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 210.870300, -224.987899, 3.862399, -89.999992, -735.252624, 164.247100, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1081, 208.649597, -225.577743, 4.202379, -0.000057, 0.000033, 179.499816, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1081, 209.039733, -225.581161, 4.202379, -0.000057, 0.000033, 179.499816, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1081, 209.439880, -225.584762, 4.202379, -0.000057, 0.000033, 179.499816, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1081, 209.850158, -225.588363, 4.202379, -0.000057, 0.000033, 179.499816, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1081, 210.240295, -225.591537, 4.202379, -0.000057, 0.000033, 179.499816, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1081, 210.620544, -225.594894, 4.202379, -0.000057, 0.000033, 179.499816, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19903, 221.848831, -228.665054, 0.748618, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19899, 221.598831, -233.778549, 0.713560, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19900, 216.909286, -226.109512, 0.746206, 0.000000, 0.000000, -41.299999, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19815, 219.668502, -224.412002, 3.412024, 0.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19815, 209.948562, -224.412033, 2.922024, 0.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19899, 221.908950, -226.618438, 3.113558, 0.000000, 540.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19921, 211.573867, -225.104598, 1.990185, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19921, 219.093948, -225.104598, 1.990185, 0.000000, 0.000000, 23.000001, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1805, 212.676620, -225.928634, 0.979212, 0.000000, 0.000000, -32.599998, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3761, 197.164031, -225.222030, 2.123430, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2008, 195.351440, -232.233520, 0.750694, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1714, 193.998062, -232.657180, 0.744403, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1721, 196.997451, -233.361770, 0.778618, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1721, 196.987457, -232.171768, 0.778618, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11730, 199.254714, -230.242950, 0.778618, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11729, 198.590652, -230.253738, 0.778618, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11729, 197.920730, -230.253738, 0.778618, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2121, 200.455535, -230.454711, 1.278618, 0.000000, 0.000000, -31.400012, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11745, 200.389297, -230.565826, 1.458618, 0.000000, 0.000000, 72.199981, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2121, 200.670944, -233.341674, 1.258618, 0.000000, 0.000000, -87.300003, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1808, 197.909179, -235.072906, 0.778618, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2121, 201.216033, -225.322021, 1.268618, 0.000000, 0.000000, 47.599979, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19921, 221.314498, -234.110397, 2.058617, 0.000000, 0.000000, -70.899986, 0, 0, -1, 200.00, 200.00);

    //automax
    bennyxadaws = CreateDynamicObject(19088, -1852.416870, 153.462448, 16.637781, 0.000058, 180.000000, 179.499618, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 16640, "a51", "concreteyellow256 copy", 0x00000000);
    bennyxadaws = CreateDynamicObject(2267, -1839.705566, 138.317108, 16.337194, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 1, 14530, "estate2", "Auto_Slamvan2", 0x00000000);
    bennyxadaws = CreateDynamicObject(19087, -1852.415771, 153.461227, 17.801858, 0.000058, -0.000031, 179.499618, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 16640, "a51", "concreteyellow256 copy", 0x00000000);
    bennyxadaws = CreateDynamicObject(1952, -1852.417846, 153.461288, 15.337794, -0.000058, 990.000000, -0.500016, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(bennyxadaws, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(bennyxadaws, 2, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    bennyxadaws = CreateDynamicObject(1974, -1852.406616, 153.460006, 15.357767, 0.000058, -0.000039, 179.499618, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    bennyxadaws = CreateDynamicObject(19088, -1855.788818, 141.332458, 16.877786, 0.000058, 180.000000, 179.499526, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 16640, "a51", "concreteyellow256 copy", 0x00000000);
    bennyxadaws = CreateDynamicObject(19087, -1855.787719, 141.331237, 18.041864, 0.000058, -0.000047, 179.499526, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 16640, "a51", "concreteyellow256 copy", 0x00000000);
    bennyxadaws = CreateDynamicObject(1952, -1855.789794, 141.331298, 15.577798, -0.000058, 990.000000, -0.500016, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(bennyxadaws, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(bennyxadaws, 2, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    bennyxadaws = CreateDynamicObject(1974, -1855.778564, 141.330017, 15.597771, 0.000058, -0.000055, 179.499526, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    bennyxadaws = CreateDynamicObject(19087, -1841.988037, 159.454086, 18.292203, 0.000049, 89.999992, -0.500373, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    bennyxadaws = CreateDynamicObject(19087, -1841.983886, 159.964584, 18.052200, 0.000049, 89.999992, -0.500373, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    bennyxadaws = CreateDynamicObject(1897, -1855.242553, 154.351898, 14.183720, 89.999992, 1038.584472, -156.984313, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 2772, "airp_prop", "CJ_red_COUNTER", 0x00000000);
    bennyxadaws = CreateDynamicObject(1897, -1855.535278, 153.375213, 15.363732, 0.000035, 720.000000, 136.599899, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 2772, "airp_prop", "CJ_red_COUNTER", 0x00000000);
    bennyxadaws = CreateDynamicObject(1897, -1854.571166, 153.718078, 14.183720, 89.999992, 988.583557, -156.983596, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 2772, "airp_prop", "CJ_red_COUNTER", 0x00000000);
    bennyxadaws = CreateDynamicObject(1897, -1855.310424, 153.649383, 14.807578, 29.999969, 900.000000, 136.599914, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 2772, "airp_prop", "CJ_red_COUNTER", 0x00000000);
    bennyxadaws = CreateDynamicObject(1897, -1854.862182, 154.088195, 16.343748, 89.999992, 1733.583007, -156.983322, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 2772, "airp_prop", "CJ_red_COUNTER", 0x00000000);
    bennyxadaws = CreateDynamicObject(16332, -1854.104492, 154.842330, 15.891939, 0.000045, 0.000041, 46.599639, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, -1, "none", "none", 0xFFFFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 2, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    bennyxadaws = CreateDynamicObject(19948, -1855.643432, 153.273620, 14.132420, 0.000045, 30.200059, 46.599639, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(bennyxadaws, 1, 7103, "vgnplantgen", "metalwheel5_128", 0xFFFFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 2, 19480, "signsurf", "sign", 0x00000000);
    bennyxadaws = CreateDynamicObject(16332, -1854.427978, 154.500198, 16.521944, 0.000045, 90.000099, 46.599639, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 19480, "signsurf", "sign", 0xFFFFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 2, 19480, "signsurf", "sign", 0x00000000);
    bennyxadaws = CreateDynamicObject(16332, -1854.943969, 153.955001, 16.521944, 0.000045, 90.000099, 46.599639, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 19480, "signsurf", "sign", 0xFFFFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 2, 19480, "signsurf", "sign", 0x00000000);
    bennyxadaws = CreateDynamicObject(16332, -1855.452636, 153.416809, 16.521944, 0.000045, 90.000099, 46.599639, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 19480, "signsurf", "sign", 0xFFFFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 2, 19480, "signsurf", "sign", 0x00000000);
    bennyxadaws = CreateDynamicObject(19873, -1855.619628, 153.303421, 16.459081, 0.000041, 89.999938, 136.599899, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 14548, "ab_cargo_int", "metal_frame", 0xFFFFFFFF);
    bennyxadaws = CreateDynamicObject(16332, -1855.616943, 153.242813, 14.763208, 0.000045, 180.000000, 46.599639, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 19480, "signsurf", "sign", 0xFFFFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 2, 19480, "signsurf", "sign", 0x00000000);
    bennyxadaws = CreateDynamicObject(16332, -1855.616943, 153.242813, 15.513939, 0.000045, 180.000000, 46.599639, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 19480, "signsurf", "sign", 0xFFFFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 2, 19480, "signsurf", "sign", 0x00000000);
    bennyxadaws = CreateDynamicObject(16332, -1855.616943, 153.242813, 16.254663, 0.000045, 180.000000, 46.599639, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 19480, "signsurf", "sign", 0xFFFFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 2, 19480, "signsurf", "sign", 0x00000000);
    bennyxadaws = CreateDynamicObject(19873, -1855.626586, 153.296264, 14.289078, 0.000041, 89.999938, 136.599899, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 14548, "ab_cargo_int", "metal_frame", 0xFFFFFFFF);
    bennyxadaws = CreateDynamicObject(19873, -1854.979736, 153.980361, 15.799074, 0.000041, 89.999938, 136.599899, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 1560, "7_11_door", "cj_sheetmetal2", 0xFFFFFFFF);
    bennyxadaws = CreateDynamicObject(1974, -1854.753784, 154.217666, 16.388650, 0.000045, 28.000047, 46.599639, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 1560, "7_11_door", "cj_sheetmetal2", 0xFFFFFFFF);
    bennyxadaws = CreateDynamicObject(19087, -1856.136230, 160.710403, 17.287218, 0.000058, 89.999992, 89.499572, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    bennyxadaws = CreateDynamicObject(19087, -1856.646728, 160.714553, 17.047214, 0.000058, 89.999992, 89.499572, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    bennyxadaws = CreateDynamicObject(19940, -1839.966064, 149.635574, 14.477693, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19793, -1840.131958, 148.898941, 14.227266, 0.000000, 124.200012, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 1, 14652, "ab_trukstpa", "wood01", 0x00000000);
    bennyxadaws = CreateDynamicObject(19793, -1839.813354, 148.898941, 14.226661, 0.000000, 57.199989, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 1, 14652, "ab_trukstpa", "wood01", 0x00000000);
    bennyxadaws = CreateDynamicObject(19793, -1840.131958, 151.108963, 14.227266, 0.000000, 124.200019, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 1, 14652, "ab_trukstpa", "wood01", 0x00000000);
    bennyxadaws = CreateDynamicObject(19793, -1839.813354, 151.108963, 14.226661, 0.000000, 57.199996, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 1, 14652, "ab_trukstpa", "wood01", 0x00000000);
    bennyxadaws = CreateDynamicObject(19940, -1839.956054, 150.415695, 14.487693, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19940, -1839.966064, 143.245574, 14.477693, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19793, -1840.131958, 142.508941, 14.227266, 0.000000, 124.200019, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 1, 14652, "ab_trukstpa", "wood01", 0x00000000);
    bennyxadaws = CreateDynamicObject(19793, -1839.813354, 142.508941, 14.226661, 0.000000, 57.199996, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 1, 14652, "ab_trukstpa", "wood01", 0x00000000);
    bennyxadaws = CreateDynamicObject(19793, -1840.131958, 144.718963, 14.227266, 0.000000, 124.200027, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 1, 14652, "ab_trukstpa", "wood01", 0x00000000);
    bennyxadaws = CreateDynamicObject(19793, -1839.813354, 144.718963, 14.226661, 0.000000, 57.200004, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 1, 14652, "ab_trukstpa", "wood01", 0x00000000);
    bennyxadaws = CreateDynamicObject(19940, -1839.956054, 144.025695, 14.487693, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    bennyxadaws = CreateDynamicObject(4735, -1857.335083, 163.384292, 28.465311, 0.100001, 0.699992, 167.499832, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterialText(bennyxadaws, 0, "WORKSHOP", 120, "Arial", 55, 1, 0xFF000000, 0x00000000, 1);
    bennyxadaws = CreateDynamicObject(19376, -1852.075073, 156.623092, 19.713451, 180.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, -1, "none", "none", 0x80FFFFFF);
    bennyxadaws = CreateDynamicObject(19376, -1852.075073, 146.993041, 19.713451, 180.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, -1, "none", "none", 0x80FFFFFF);
    bennyxadaws = CreateDynamicObject(19376, -1852.075073, 137.363037, 19.713451, 180.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, -1, "none", "none", 0x80FFFFFF);
    bennyxadaws = CreateDynamicObject(19376, -1841.923828, 134.892974, 17.563446, 180.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, -1, "none", "none", 0x80FFFFFF);
    bennyxadaws = CreateDynamicObject(19376, -1843.944458, 156.623092, 19.703451, 0.000000, 270.000000, -179.999984, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, -1, "none", "none", 0x80FFFFFF);
    bennyxadaws = CreateDynamicObject(19376, -1843.944458, 146.993041, 19.703451, 0.000000, 270.000000, -179.999984, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, -1, "none", "none", 0x80FFFFFF);
    bennyxadaws = CreateDynamicObject(19376, -1843.944458, 137.363037, 19.703451, 0.000000, 270.000000, -179.999984, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, -1, "none", "none", 0x80FFFFFF);
    bennyxadaws = CreateDynamicObject(19376, -1843.934448, 134.222976, 19.693450, 0.000000, 270.000000, -179.999984, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, -1, "none", "none", 0x80FFFFFF);
    bennyxadaws = CreateDynamicObject(19376, -1852.065063, 133.883117, 19.703451, 180.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, -1, "none", "none", 0x80FFFFFF);
    bennyxadaws = CreateDynamicObject(4735, -1858.122070, 160.045883, 30.494319, 0.000001, 0.299992, 167.599838, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterialText(bennyxadaws, 0, "AUTOMAX", 120, "Arial", 55, 1, 0xFFFFFF00, 0x00000000, 1);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(11388, -1848.351684, 145.446685, 20.785829, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11390, -1848.351684, 145.446685, 18.496828, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11394, -1848.331665, 147.040695, 18.004631, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11389, -1848.291870, 145.446685, 17.246831, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11360, -1839.041748, 149.047698, 16.207731, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11359, -1852.801635, 129.196685, 16.207731, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11387, -1838.921752, 129.438690, 17.465530, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11392, -1847.931762, 146.868698, 14.153030, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19900, -1852.214233, 153.953964, 14.098915, 0.000000, 0.000000, 12.900007, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19903, -1855.166137, 151.811950, 14.117354, 0.000000, 0.000000, 315.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19899, -1856.782958, 143.358184, 14.082406, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19899, -1856.782958, 134.038284, 14.082406, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19815, -1857.266357, 140.007339, 15.555787, 0.000000, 0.000000, 450.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19899, -1856.812255, 139.618072, 14.952404, 0.000000, 180.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1327, -1856.655273, 146.216476, 14.984676, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19900, -1854.962280, 146.820693, 14.098915, 0.000000, 0.000000, 12.900007, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(935, -1839.888793, 152.336975, 14.670655, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(935, -1840.289184, 140.737045, 14.670655, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(935, -1841.599243, 140.427047, 14.670655, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(935, -1840.868774, 140.257080, 14.670655, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(935, -1840.478393, 140.407073, 15.820656, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(935, -1844.559448, 140.087066, 14.670655, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(935, -1845.280029, 140.217071, 14.670655, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(935, -1846.050170, 140.247055, 14.670655, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1778, -1843.906738, 139.821594, 14.098579, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1428, -1843.574584, 140.287078, 15.603816, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1428, -1847.584594, 129.887084, 15.603816, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2103, -1845.233398, 140.181869, 15.270067, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2690, -1847.351440, 139.028137, 15.234239, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19899, -1845.401733, 130.608230, 14.082406, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19872, -1853.712646, 157.328491, 12.245966, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19872, -1853.712646, 148.928527, 12.245963, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2567, -1843.178222, 160.650482, 15.987407, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, -1844.401367, 160.594223, 18.192188, -89.999992, -403.586608, 135.913101, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, -1841.931152, 160.572494, 18.192188, -89.999992, -403.586608, 135.913101, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1081, -1844.151855, 159.982650, 18.532169, -0.000049, 0.000033, 179.499862, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1081, -1843.761718, 159.979232, 18.532169, -0.000049, 0.000033, 179.499862, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1081, -1843.361572, 159.975692, 18.532169, -0.000049, 0.000033, 179.499862, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1081, -1842.951416, 159.972152, 18.532169, -0.000049, 0.000033, 179.499862, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1081, -1842.561279, 159.968978, 18.532169, -0.000049, 0.000033, 179.499862, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1081, -1842.180908, 159.965560, 18.532169, -0.000049, 0.000033, 179.499862, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, -1857.276367, 158.297073, 17.187202, -89.999992, -493.586608, 135.913101, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, -1857.254638, 160.767288, 17.187202, -89.999992, -493.586608, 135.913101, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1081, -1856.664794, 158.546585, 17.527183, -0.000057, 0.000031, -90.500053, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1081, -1856.661376, 158.936721, 17.527183, -0.000057, 0.000031, -90.500053, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1081, -1856.657836, 159.336868, 17.527183, -0.000057, 0.000031, -90.500053, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1081, -1856.654296, 159.747024, 17.527183, -0.000057, 0.000031, -90.500053, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1081, -1856.651123, 160.137161, 17.527183, -0.000057, 0.000031, -90.500053, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1081, -1856.647705, 160.517532, 17.527183, -0.000057, 0.000031, -90.500053, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19815, -1849.255859, 161.447265, 16.495779, 0.000000, 0.000000, 720.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19899, -1853.802124, 160.968002, 14.952404, 0.000000, 180.000000, 450.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3761, -1839.631591, 130.927703, 15.497184, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2593, -1843.212646, 130.890487, 14.947183, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1998, -1840.466918, 136.956985, 14.107186, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1744, -1839.518066, 136.651763, 16.337198, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1671, -1840.038452, 137.924713, 14.534618, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2121, -1846.482055, 136.310089, 14.527187, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1806, -1843.043945, 138.033416, 14.087185, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1806, -1843.043945, 136.993438, 14.087185, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11729, -1839.923583, 132.026885, 14.087184, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11730, -1839.913940, 132.691833, 14.087185, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3465, -1826.725463, 135.414840, 15.581459, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 

    //bennys
    bennyxadaws = CreateDynamicObject(18981, 1324.715332, 692.449768, 9.387483, 180.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    bennyxadaws = CreateDynamicObject(18981, 1299.745361, 692.449768, 9.387483, 180.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    bennyxadaws = CreateDynamicObject(18981, 1274.815063, 692.449768, 9.387483, 180.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    bennyxadaws = CreateDynamicObject(18981, 1324.715332, 722.259033, 9.427486, 0.000000, 270.000000, -179.999984, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    bennyxadaws = CreateDynamicObject(18981, 1303.815429, 722.068786, 9.437481, 0.000000, 270.000000, -179.999984, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    bennyxadaws = CreateDynamicObject(18981, 1274.815063, 717.439025, 9.387483, 0.000000, 270.000000, -179.999984, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    bennyxadaws = CreateDynamicObject(18981, 1324.715332, 742.438537, 9.377483, 0.000000, 270.000000, -179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    bennyxadaws = CreateDynamicObject(18981, 1303.915283, 747.048278, 9.427482, 0.000000, 270.000000, -179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    bennyxadaws = CreateDynamicObject(18981, 1274.815063, 742.438537, 9.387483, 0.000000, 270.000000, -179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    bennyxadaws = CreateDynamicObject(18981, 1324.715332, 767.437866, 9.387483, 0.000000, 270.000000, -179.999893, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    bennyxadaws = CreateDynamicObject(18981, 1303.934692, 767.437866, 9.467486, 0.000000, 270.000000, -179.999893, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    bennyxadaws = CreateDynamicObject(18981, 1274.815063, 767.437866, 9.387483, 0.000000, 270.000000, -179.999893, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1334.698486, 679.468872, 14.808131, 0.000000, 270.000000, -179.999893, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(19912, 1332.279418, 679.475830, 13.076971, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "black64", 0x90FFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 1, 10765, "airportgnd_sfse", "white", 0x90FFFFFF);
    bennyxadaws = CreateDynamicObject(18766, 1319.159301, 679.468872, 14.808131, 0.000000, 270.000000, -179.999847, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1327.198852, 679.468872, 7.808105, 0.000000, 360.000000, -179.999984, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1323.998779, 679.478881, 7.818106, 0.000000, 360.000000, -179.999984, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(19912, 1316.739379, 679.475830, 13.076971, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "black64", 0x90FFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 1, 10765, "airportgnd_sfse", "white", 0x90FFFFFF);
    bennyxadaws = CreateDynamicObject(18766, 1303.619262, 679.468872, 14.808131, 0.000000, 270.000000, -179.999801, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1311.658813, 679.468872, 7.808105, 0.000000, 360.000000, -179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1308.458740, 679.478881, 7.818106, 0.000000, 360.000000, -179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(19912, 1301.220336, 679.475830, 13.076971, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "black64", 0x90FFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 1, 10765, "airportgnd_sfse", "white", 0x90FFFFFF);
    bennyxadaws = CreateDynamicObject(18766, 1288.100219, 679.468872, 14.808131, 0.000000, 270.000000, -179.999801, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1296.139770, 679.468872, 7.808105, 0.000000, 360.000000, -179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1292.939697, 679.478881, 7.818106, 0.000000, 360.000000, -179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(19912, 1285.680297, 679.475830, 13.076971, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "black64", 0x90FFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 1, 10765, "airportgnd_sfse", "white", 0x90FFFFFF);
    bennyxadaws = CreateDynamicObject(18766, 1272.560180, 679.468872, 14.808131, 0.000000, 270.000000, -179.999755, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1280.599731, 679.468872, 7.808105, 0.000000, 360.000000, -179.999893, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1277.399658, 679.478881, 7.818106, 0.000000, 360.000000, -179.999893, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18981, 1261.824707, 692.449768, 7.307480, 0.000000, 360.000000, -179.999984, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1267.611083, 679.468872, 14.808131, 0.000000, 270.000000, -179.999755, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1263.820556, 679.458862, 14.798130, 0.000000, 270.000000, -179.999755, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1261.823852, 769.582275, 14.808131, -0.000037, 270.000000, -89.999809, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(19912, 1261.816894, 767.163208, 13.076971, 0.000029, 0.000014, 89.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "black64", 0x90FFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 1, 10765, "airportgnd_sfse", "white", 0x90FFFFFF);
    bennyxadaws = CreateDynamicObject(18766, 1261.823852, 754.043090, 14.808131, -0.000037, 270.000000, -89.999763, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1261.823852, 762.082641, 7.808105, -0.000029, 360.000000, -89.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1261.813842, 758.882568, 7.818106, -0.000029, 360.000000, -89.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(19912, 1261.816894, 751.623168, 13.076971, 0.000029, 0.000022, 89.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "black64", 0x90FFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 1, 10765, "airportgnd_sfse", "white", 0x90FFFFFF);
    bennyxadaws = CreateDynamicObject(18766, 1261.823852, 738.503051, 14.808131, -0.000037, 270.000000, -89.999717, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1261.823852, 746.542602, 7.808105, -0.000029, 360.000000, -89.999832, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1261.813842, 743.342529, 7.818106, -0.000029, 360.000000, -89.999832, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(19912, 1261.816894, 736.104125, 13.076971, 0.000029, 0.000022, 89.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "black64", 0x90FFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 1, 10765, "airportgnd_sfse", "white", 0x90FFFFFF);
    bennyxadaws = CreateDynamicObject(18766, 1261.823852, 722.984008, 14.808131, -0.000037, 270.000000, -89.999717, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1261.823852, 731.023559, 7.808105, -0.000029, 360.000000, -89.999832, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1261.813842, 727.823486, 7.818106, -0.000029, 360.000000, -89.999832, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(19912, 1261.816894, 720.564086, 13.076971, 0.000029, 0.000029, 89.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "black64", 0x90FFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 1, 10765, "airportgnd_sfse", "white", 0x90FFFFFF);
    bennyxadaws = CreateDynamicObject(18766, 1261.823852, 707.443969, 14.808131, -0.000037, 270.000000, -89.999671, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1261.823852, 715.483520, 7.808105, -0.000029, 360.000000, -89.999786, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1261.813842, 712.283447, 7.818106, -0.000029, 360.000000, -89.999786, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1334.698486, 709.269592, 14.808131, 0.000000, 270.000000, -179.999847, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18981, 1336.734008, 697.270385, 7.297482, 0.000000, 360.000000, -179.999984, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1319.159301, 709.269592, 14.808131, 0.000000, 270.000000, -179.999801, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1327.198852, 709.269592, 7.418103, 0.000000, 360.000000, -179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1323.998779, 709.279602, 7.428103, 0.000000, 360.000000, -179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(19912, 1316.739379, 709.276550, 13.076971, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "black64", 0x90FFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 1, 10765, "airportgnd_sfse", "white", 0x90FFFFFF);
    bennyxadaws = CreateDynamicObject(18766, 1303.619262, 709.269592, 14.808131, 0.000000, 270.000000, -179.999755, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1311.658813, 709.269592, 7.808105, 0.000000, 360.000000, -179.999893, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1308.458740, 709.279602, 7.818106, 0.000000, 360.000000, -179.999893, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(19912, 1302.070556, 709.276550, 13.076971, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "black64", 0x90FFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 1, 10765, "airportgnd_sfse", "white", 0x90FFFFFF);
    bennyxadaws = CreateDynamicObject(18766, 1336.728149, 682.348937, 14.818131, -0.000007, 270.000000, -89.999916, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1296.139770, 709.269592, 7.808105, 0.000000, 360.000000, -179.999893, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1295.439819, 709.279602, 7.818107, 0.000000, 360.000000, -179.999893, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1290.915039, 773.383850, 14.798130, -0.000051, 270.000000, -89.999717, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(19912, 1290.928466, 755.473693, 13.076971, 0.000045, 0.000022, 89.999832, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "black64", 0x90FFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 1, 10765, "airportgnd_sfse", "white", 0x90FFFFFF);
    bennyxadaws = CreateDynamicObject(18766, 1290.935424, 742.353576, 14.808131, -0.000051, 270.000000, -89.999671, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1290.945434, 750.393127, 7.808104, -0.000045, 360.000000, -89.999786, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1290.935424, 747.193054, 7.818106, -0.000045, 360.000000, -89.999786, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(19912, 1290.928466, 739.933654, 13.076971, 0.000045, 0.000029, 89.999832, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "black64", 0x90FFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 1, 10765, "airportgnd_sfse", "white", 0x90FFFFFF);
    bennyxadaws = CreateDynamicObject(18766, 1290.935424, 726.813537, 14.808131, -0.000051, 270.000000, -89.999626, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1290.945434, 734.853088, 7.808104, -0.000045, 360.000000, -89.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1290.935424, 731.653015, 7.818106, -0.000045, 360.000000, -89.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(19912, 1290.928466, 724.414611, 13.076971, 0.000045, 0.000029, 89.999832, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "black64", 0x90FFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 1, 10765, "airportgnd_sfse", "white", 0x90FFFFFF);
    bennyxadaws = CreateDynamicObject(18766, 1290.935424, 711.294494, 14.808131, -0.000051, 270.000000, -89.999626, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1290.945434, 719.334045, 7.808104, -0.000045, 360.000000, -89.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1290.935424, 716.133972, 7.818106, -0.000045, 360.000000, -89.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18981, 1274.815185, 780.420227, 7.307487, 0.000000, 360.000000, -90.000007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1261.823852, 774.542358, 14.808131, -0.000036, 270.000000, -89.999809, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1261.833862, 778.422180, 14.818131, -0.000037, 270.000000, -89.999809, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1290.945068, 777.593811, 14.808131, -0.000051, 270.000000, -89.999717, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1288.896118, 780.394042, 14.798130, -0.000044, 270.000000, 0.000257, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1290.934082, 757.894592, 14.808131, -0.000022, 270.000000, -89.999763, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1290.934082, 765.934143, 7.418103, -0.000014, 360.000000, -89.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1290.924072, 762.734069, 7.428103, -0.000014, 360.000000, -89.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(19376, 1332.004638, 685.227539, 4.650266, 0.000000, 0.000029, -0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19353, 1330.375122, 680.512084, 8.145769, 0.000022, 0.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19353, 1327.175537, 680.512084, 8.145769, 0.000022, 0.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19376, 1325.654785, 685.382812, 4.650266, 0.000007, -0.000014, 179.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19353, 1327.284301, 690.098144, 8.145769, -0.000022, -0.000007, -89.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19353, 1330.483886, 690.098144, 8.145769, -0.000022, -0.000007, -89.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(18667, 1328.894287, 685.282714, 9.920925, 0.000000, 90.000000, -89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(bennyxadaws, 0, "1", 50, "Arial", 100, 1, 0xFFFFFF00, 0x00000000, 1);
    bennyxadaws = CreateDynamicObject(19376, 1322.394653, 685.227539, 4.650266, 0.000000, 0.000037, -0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19353, 1320.765136, 680.512084, 8.145769, 0.000029, 0.000000, 89.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19353, 1317.565551, 680.512084, 8.145769, 0.000029, 0.000000, 89.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19376, 1316.044799, 685.382812, 4.650266, 0.000007, -0.000022, 179.999725, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19353, 1317.674316, 690.098144, 8.145769, -0.000029, -0.000007, -89.999916, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19353, 1320.873901, 690.098144, 8.145769, -0.000029, -0.000007, -89.999916, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(18667, 1319.284301, 685.282714, 9.920925, -0.000007, 90.000000, -89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(bennyxadaws, 0, "2", 50, "Arial", 100, 1, 0xFFFFFF00, 0x00000000, 1);
    bennyxadaws = CreateDynamicObject(19376, 1312.604125, 685.227539, 4.650266, 0.000000, 0.000045, -0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19353, 1310.974609, 680.512084, 8.145769, 0.000037, 0.000000, 89.999885, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19353, 1307.775024, 680.512084, 8.145769, 0.000037, 0.000000, 89.999885, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19376, 1306.254272, 685.382812, 4.650266, 0.000007, -0.000029, 179.999679, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19353, 1307.883789, 690.098144, 8.145769, -0.000037, -0.000007, -89.999893, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19353, 1311.083374, 690.098144, 8.145769, -0.000037, -0.000007, -89.999893, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(18667, 1309.493774, 685.282714, 9.920925, -0.000014, 90.000000, -89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(bennyxadaws, 0, "3", 50, "Arial", 100, 1, 0xFFFFFF00, 0x00000000, 1);
    bennyxadaws = CreateDynamicObject(19376, 1302.601806, 685.227539, 4.650266, 0.000000, 0.000037, -0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19353, 1300.972290, 680.512084, 8.145769, 0.000029, 0.000000, 89.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19353, 1297.772705, 680.512084, 8.145769, 0.000029, 0.000000, 89.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19376, 1296.251953, 685.382812, 4.650266, 0.000007, -0.000022, 179.999725, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19353, 1297.881469, 690.098144, 8.145769, -0.000029, -0.000007, -89.999916, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19353, 1301.081054, 690.098144, 8.145769, -0.000029, -0.000007, -89.999916, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(18667, 1299.491455, 685.282714, 9.920925, -0.000007, 90.000000, -89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(bennyxadaws, 0, "4", 50, "Arial", 100, 1, 0xFFFFFF00, 0x00000000, 1);
    bennyxadaws = CreateDynamicObject(19376, 1292.991821, 685.227539, 4.650266, 0.000000, 0.000045, -0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19353, 1291.362304, 680.512084, 8.145769, 0.000037, 0.000000, 89.999885, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19353, 1288.162719, 680.512084, 8.145769, 0.000037, 0.000000, 89.999885, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19376, 1286.641967, 685.382812, 4.650266, 0.000007, -0.000029, 179.999679, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19353, 1288.271484, 690.098144, 8.145769, -0.000037, -0.000007, -89.999893, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19353, 1291.471069, 690.098144, 8.145769, -0.000037, -0.000007, -89.999893, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(18667, 1289.881469, 685.282714, 9.920925, -0.000014, 90.000000, -89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(bennyxadaws, 0, "5", 60, "Arial", 140, 1, 0xFFFFFF00, 0x00000000, 1);
    bennyxadaws = CreateDynamicObject(19376, 1283.201293, 685.227539, 4.650266, 0.000000, 0.000052, -0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19353, 1281.571777, 680.512084, 8.145769, 0.000045, 0.000000, 89.999862, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19353, 1278.372192, 680.512084, 8.145769, 0.000045, 0.000000, 89.999862, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19376, 1276.851440, 685.382812, 4.650266, 0.000007, -0.000037, 179.999633, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19353, 1278.480957, 690.098144, 8.145769, -0.000045, -0.000007, -89.999870, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19353, 1281.680541, 690.098144, 8.145769, -0.000045, -0.000007, -89.999870, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(18667, 1280.090942, 685.282714, 9.920925, -0.000022, 90.000000, -89.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(bennyxadaws, 0, "6", 50, "Arial", 100, 1, 0xFFFFFF00, 0x00000000, 1);
    bennyxadaws = CreateDynamicObject(19376, 1267.547363, 710.299926, 4.650266, -0.000007, 0.000037, -90.000045, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19353, 1262.832031, 711.929565, 8.145769, 0.000029, 0.000007, -0.000128, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19353, 1262.832031, 715.129028, 8.145769, 0.000029, 0.000007, -0.000128, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19376, 1267.702636, 716.649780, 4.650266, 0.000014, -0.000022, 89.999656, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19353, 1272.417968, 715.020385, 8.145769, -0.000029, -0.000014, -179.999832, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19353, 1272.417968, 711.820678, 8.145769, -0.000029, -0.000014, -179.999832, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(18667, 1267.602539, 713.410278, 9.920925, -0.000007, 89.999992, 179.999862, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(bennyxadaws, 0, "7", 50, "Arial", 100, 1, 0xFFFFFF00, 0x00000000, 1);
    bennyxadaws = CreateDynamicObject(19376, 1267.547363, 719.910034, 4.650266, -0.000007, 0.000045, -90.000045, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19353, 1262.832031, 721.539428, 8.145769, 0.000037, 0.000007, -0.000159, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19353, 1262.832031, 724.739135, 8.145769, 0.000037, 0.000007, -0.000159, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19376, 1267.702636, 726.259887, 4.650266, 0.000014, -0.000029, 89.999610, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19353, 1272.417968, 724.630249, 8.145769, -0.000037, -0.000014, -179.999801, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19353, 1272.417968, 721.430786, 8.145769, -0.000037, -0.000014, -179.999801, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(18667, 1267.602539, 723.020385, 9.920925, -0.000014, 89.999992, -179.999847, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(bennyxadaws, 0, "8", 50, "Arial", 100, 1, 0xFFFFFF00, 0x00000000, 1);
    bennyxadaws = CreateDynamicObject(19376, 1267.547363, 729.700561, 4.650266, -0.000007, 0.000052, -90.000045, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19353, 1262.832031, 731.329956, 8.145769, 0.000045, 0.000007, -0.000174, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19353, 1262.832031, 734.529663, 8.145769, 0.000045, 0.000007, -0.000174, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19376, 1267.702636, 736.050415, 4.650266, 0.000014, -0.000037, 89.999565, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19353, 1272.417968, 734.420776, 8.145769, -0.000045, -0.000014, -179.999786, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19353, 1272.417968, 731.221313, 8.145769, -0.000045, -0.000014, -179.999786, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(18667, 1267.602539, 732.810913, 9.920925, -0.000022, 89.999992, -179.999816, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(bennyxadaws, 0, "9", 50, "Arial", 100, 1, 0xFFFFFF00, 0x00000000, 1);
    bennyxadaws = CreateDynamicObject(19376, 1267.547363, 739.380004, 4.650266, -0.000014, 0.000037, -90.000022, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19353, 1262.832031, 741.009643, 8.145769, 0.000029, 0.000014, -0.000128, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19353, 1262.832031, 744.209106, 8.145769, 0.000029, 0.000014, -0.000128, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19376, 1267.702636, 745.729858, 4.650266, 0.000022, -0.000022, 89.999633, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19353, 1272.417968, 744.100463, 8.145769, -0.000029, -0.000022, -179.999786, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19353, 1272.417968, 740.900756, 8.145769, -0.000029, -0.000022, -179.999786, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(18667, 1267.602539, 742.490356, 9.920925, -0.000007, 89.999984, 179.999816, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(bennyxadaws, 0, "10", 50, "Arial", 100, 1, 0xFFFFFF00, 0x00000000, 1);
    bennyxadaws = CreateDynamicObject(19376, 1267.547363, 748.990112, 4.650266, -0.000014, 0.000045, -90.000022, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19353, 1262.832031, 750.619506, 8.145769, 0.000037, 0.000014, -0.000159, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19353, 1262.832031, 753.819213, 8.145769, 0.000037, 0.000014, -0.000159, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19376, 1267.702636, 755.339965, 4.650266, 0.000022, -0.000029, 89.999588, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19353, 1272.417968, 753.710327, 8.145769, -0.000037, -0.000022, -179.999755, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19353, 1272.417968, 750.510864, 8.145769, -0.000037, -0.000022, -179.999755, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(18667, 1267.602539, 752.100463, 9.920925, -0.000014, 89.999984, -179.999801, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(bennyxadaws, 0, "11", 50, "Arial", 100, 1, 0xFFFFFF00, 0x00000000, 1);
    bennyxadaws = CreateDynamicObject(19376, 1267.547363, 758.780639, 4.650266, -0.000014, 0.000052, -90.000022, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19353, 1262.832031, 760.410034, 8.145769, 0.000045, 0.000014, -0.000174, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19353, 1262.832031, 763.609741, 8.145769, 0.000045, 0.000014, -0.000174, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19376, 1267.702636, 765.130493, 4.650266, 0.000022, -0.000037, 89.999542, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19353, 1272.417968, 763.500854, 8.145769, -0.000045, -0.000022, -179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(19353, 1272.417968, 760.301391, 8.145769, -0.000045, -0.000022, -179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    bennyxadaws = CreateDynamicObject(18667, 1267.602539, 761.890991, 9.920925, -0.000022, 89.999984, -179.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(bennyxadaws, 0, "12", 50, "Arial", 100, 1, 0xFFFFFF00, 0x00000000, 1);
    bennyxadaws = CreateDynamicObject(18981, 1324.715332, 702.648864, 9.397480, 180.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5154, "dkcargoshp_las2", "green_64", 0x00000000);
    bennyxadaws = CreateDynamicObject(18981, 1299.736206, 702.648864, 9.397480, 180.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5154, "dkcargoshp_las2", "green_64", 0x00000000);
    bennyxadaws = CreateDynamicObject(18981, 1285.006225, 702.648864, 9.407482, 180.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5154, "dkcargoshp_las2", "green_64", 0x00000000);
    bennyxadaws = CreateDynamicObject(18981, 1285.006225, 727.618591, 9.407482, 180.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5154, "dkcargoshp_las2", "green_64", 0x00000000);
    bennyxadaws = CreateDynamicObject(18981, 1285.006225, 752.618164, 9.407482, 180.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5154, "dkcargoshp_las2", "green_64", 0x00000000);
    bennyxadaws = CreateDynamicObject(18981, 1285.006225, 767.448486, 9.397480, 180.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5154, "dkcargoshp_las2", "green_64", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1272.581542, 685.448608, 14.758129, 0.000000, 270.000000, -179.999710, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1267.591186, 685.448608, 14.758129, 0.000000, 270.000000, -179.999710, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1268.831909, 692.708679, 13.848109, 89.999992, 360.000183, -89.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18882, "hugebowls", "woodpanel1", 0x00000000);
    bennyxadaws = CreateDynamicObject(19383, 1269.639404, 697.603576, 11.602705, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18882, "hugebowls", "woodpanel1", 0x00000000);
    bennyxadaws = CreateDynamicObject(19912, 1271.257934, 694.833984, 15.347002, 0.000051, 270.000030, 89.999809, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "black64", 0x90FFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 1, 10765, "airportgnd_sfse", "white", 0x90FFFFFF);
    bennyxadaws = CreateDynamicObject(19912, 1271.267944, 690.534240, 15.347006, 0.000051, 270.000030, 89.999809, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "black64", 0x90FFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 1, 10765, "airportgnd_sfse", "white", 0x90FFFFFF);
    bennyxadaws = CreateDynamicObject(18766, 1266.364013, 688.249023, 11.808120, 0.000000, 720.000000, -179.999786, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18882, "hugebowls", "woodpanel1", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1263.869750, 685.428588, 14.748126, 0.000000, 270.000000, -179.999710, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1263.931518, 692.728698, 13.838109, 89.999992, 360.000183, -89.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18882, "hugebowls", "woodpanel1", 0x00000000);
    bennyxadaws = CreateDynamicObject(19383, 1269.639404, 697.603576, 16.062709, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18882, "hugebowls", "woodpanel1", 0x00000000);
    bennyxadaws = CreateDynamicObject(19445, 1266.937866, 697.605957, 14.398815, 90.000000, 360.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18882, "hugebowls", "woodpanel1", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1266.364013, 703.678710, 11.808120, 0.000000, 720.000000, -179.999786, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18882, "hugebowls", "woodpanel1", 0x00000000);
    bennyxadaws = CreateDynamicObject(2357, 1266.652954, 693.064758, 10.258810, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 16093, "a51_ext", "ws_whitewall2_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(2357, 1265.343627, 693.064758, 10.258810, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 16093, "a51_ext", "ws_whitewall2_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(2608, 1268.160156, 688.948120, 10.478144, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 16093, "a51_ext", "ws_whitewall2_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18981, 1336.703979, 697.260375, -0.612515, 180.000000, 180.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 1426, "break_scaffold", "CJ_BLUE_WOOD", 0x00000000);
    bennyxadaws = CreateDynamicObject(18981, 1336.693969, 692.409973, -0.612515, 180.000000, 180.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 1426, "break_scaffold", "CJ_BLUE_WOOD", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1334.638427, 679.498901, 6.878129, 0.000000, 270.000000, -179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 1426, "break_scaffold", "CJ_BLUE_WOOD", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1319.178588, 679.498901, 6.878129, 0.000000, 270.000000, -179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 1426, "break_scaffold", "CJ_BLUE_WOOD", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1303.608154, 679.498901, 6.878129, 0.000000, 270.000000, -179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 1426, "break_scaffold", "CJ_BLUE_WOOD", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1288.047241, 679.498901, 6.878129, 0.000000, 270.000000, -179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 1426, "break_scaffold", "CJ_BLUE_WOOD", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1272.631835, 679.488891, 6.878128, 0.000000, 270.000000, -179.999664, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 1426, "break_scaffold", "CJ_BLUE_WOOD", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1267.642700, 679.488891, 6.878128, 0.000000, 270.000000, -179.999664, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 1426, "break_scaffold", "CJ_BLUE_WOOD", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1263.852172, 679.478881, 6.868124, 0.000000, 270.000000, -179.999664, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 1426, "break_scaffold", "CJ_BLUE_WOOD", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1272.613159, 685.428588, 6.868125, 0.000000, 270.000000, -179.999572, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 1426, "break_scaffold", "CJ_BLUE_WOOD", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1267.622802, 685.428588, 6.868125, 0.000000, 270.000000, -179.999572, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 1426, "break_scaffold", "CJ_BLUE_WOOD", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1263.901367, 685.408569, 6.858126, 0.000000, 270.000000, -179.999572, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 1426, "break_scaffold", "CJ_BLUE_WOOD", 0x00000000);
    bennyxadaws = CreateDynamicObject(18981, 1261.854736, 692.449768, -0.602518, 180.000000, 180.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 1426, "break_scaffold", "CJ_BLUE_WOOD", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1261.853881, 707.433959, 6.898120, -0.000029, 270.000000, -89.999694, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 1426, "break_scaffold", "CJ_BLUE_WOOD", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1261.853881, 722.954406, 6.898120, -0.000029, 270.000000, -89.999694, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 1426, "break_scaffold", "CJ_BLUE_WOOD", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1261.853881, 738.524780, 6.898120, -0.000029, 270.000000, -89.999694, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 1426, "break_scaffold", "CJ_BLUE_WOOD", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1261.853881, 754.055053, 6.898120, -0.000029, 270.000000, -89.999694, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 1426, "break_scaffold", "CJ_BLUE_WOOD", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1261.843872, 769.562927, 6.918120, -0.000050, 270.000000, -89.999763, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 1426, "break_scaffold", "CJ_BLUE_WOOD", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1261.843872, 774.542968, 6.918120, -0.000050, 270.000000, -89.999763, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 1426, "break_scaffold", "CJ_BLUE_WOOD", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1261.853881, 778.402832, 6.928122, -0.000050, 270.000000, -89.999763, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 1426, "break_scaffold", "CJ_BLUE_WOOD", 0x00000000);
    bennyxadaws = CreateDynamicObject(18981, 1274.815185, 780.400207, -0.542510, 180.000000, 180.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 1426, "break_scaffold", "CJ_BLUE_WOOD", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1288.886352, 780.364013, 6.968122, -0.000045, 270.000000, 0.000257, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 1426, "break_scaffold", "CJ_BLUE_WOOD", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1290.885009, 773.413879, 6.978114, -0.000050, 270.000000, -89.999717, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 1426, "break_scaffold", "CJ_BLUE_WOOD", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1290.905029, 777.413818, 6.988113, -0.000050, 270.000000, -89.999717, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 1426, "break_scaffold", "CJ_BLUE_WOOD", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1290.905029, 757.873718, 6.988113, -0.000050, 270.000000, -89.999717, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 1426, "break_scaffold", "CJ_BLUE_WOOD", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1290.915039, 742.323242, 6.988113, -0.000050, 270.000000, -89.999717, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 1426, "break_scaffold", "CJ_BLUE_WOOD", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1290.915039, 726.793273, 6.988113, -0.000050, 270.000000, -89.999717, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 1426, "break_scaffold", "CJ_BLUE_WOOD", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1290.915039, 711.252929, 6.988113, -0.000050, 270.000000, -89.999717, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 1426, "break_scaffold", "CJ_BLUE_WOOD", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1303.635009, 709.242187, 6.988114, -0.000051, 270.000000, 0.000281, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 1426, "break_scaffold", "CJ_BLUE_WOOD", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1319.144775, 709.242187, 6.988114, -0.000051, 270.000000, 0.000281, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 1426, "break_scaffold", "CJ_BLUE_WOOD", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1334.714965, 709.242187, 6.888113, -0.000050, 270.000000, 0.000280, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 1426, "break_scaffold", "CJ_BLUE_WOOD", 0x00000000);
    bennyxadaws = CreateDynamicObject(1897, 1314.047973, 687.567626, 9.988677, 89.999992, 1039.042358, -89.542068, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 2772, "airp_prop", "CJ_red_COUNTER", 0x00000000);
    bennyxadaws = CreateDynamicObject(1897, 1314.842773, 686.928955, 11.168689, 0.000031, 720.000000, -155.499893, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 2772, "airp_prop", "CJ_red_COUNTER", 0x00000000);
    bennyxadaws = CreateDynamicObject(1897, 1314.887817, 687.951232, 9.988677, 89.999992, 989.041748, -89.541709, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 2772, "airp_prop", "CJ_red_COUNTER", 0x00000000);
    bennyxadaws = CreateDynamicObject(1897, 1314.673339, 687.240417, 10.612533, 29.999965, 900.000000, -155.499877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 2772, "airp_prop", "CJ_red_COUNTER", 0x00000000);
    bennyxadaws = CreateDynamicObject(1897, 1314.435424, 687.820861, 12.148704, 89.999992, 1734.041381, -89.541580, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 2772, "airp_prop", "CJ_red_COUNTER", 0x00000000);
    bennyxadaws = CreateDynamicObject(16332, 1314.021728, 688.806518, 11.696895, 0.000051, 0.000038, 114.499679, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, -1, "none", "none", 0xFFFFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 2, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    bennyxadaws = CreateDynamicObject(19948, 1314.896240, 686.790588, 9.937377, 0.000051, 30.200056, 114.499679, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(bennyxadaws, 1, 7103, "vgnplantgen", "metalwheel5_128", 0xFFFFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 2, 19480, "signsurf", "sign", 0x00000000);
    bennyxadaws = CreateDynamicObject(16332, 1314.217041, 688.378173, 12.326901, 0.000051, 90.000099, 114.499679, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 19480, "signsurf", "sign", 0xFFFFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 2, 19480, "signsurf", "sign", 0x00000000);
    bennyxadaws = CreateDynamicObject(16332, 1314.528076, 687.695007, 12.326901, 0.000051, 90.000099, 114.499679, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 19480, "signsurf", "sign", 0xFFFFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 2, 19480, "signsurf", "sign", 0x00000000);
    bennyxadaws = CreateDynamicObject(16332, 1314.835327, 687.021179, 12.326901, 0.000051, 90.000099, 114.499679, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 19480, "signsurf", "sign", 0xFFFFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 2, 19480, "signsurf", "sign", 0x00000000);
    bennyxadaws = CreateDynamicObject(19873, 1314.877563, 686.823791, 12.264039, 0.000038, 89.999931, -155.499923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 14548, "ab_cargo_int", "metal_frame", 0xFFFFFFFF);
    bennyxadaws = CreateDynamicObject(16332, 1314.934692, 686.803405, 10.568164, 0.000051, 180.000000, 114.499679, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 19480, "signsurf", "sign", 0xFFFFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 2, 19480, "signsurf", "sign", 0x00000000);
    bennyxadaws = CreateDynamicObject(16332, 1314.934692, 686.803405, 11.318898, 0.000051, 180.000000, 114.499679, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 19480, "signsurf", "sign", 0xFFFFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 2, 19480, "signsurf", "sign", 0x00000000);
    bennyxadaws = CreateDynamicObject(16332, 1314.934692, 686.803405, 12.059619, 0.000051, 180.000000, 114.499679, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 19480, "signsurf", "sign", 0xFFFFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 2, 19480, "signsurf", "sign", 0x00000000);
    bennyxadaws = CreateDynamicObject(19873, 1314.881591, 686.814697, 10.094037, 0.000038, 89.999931, -155.499923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 14548, "ab_cargo_int", "metal_frame", 0xFFFFFFFF);
    bennyxadaws = CreateDynamicObject(19873, 1314.491088, 687.671325, 11.604030, 0.000038, 89.999931, -155.499923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 1560, "7_11_door", "cj_sheetmetal2", 0xFFFFFFFF);
    bennyxadaws = CreateDynamicObject(1974, 1314.356201, 687.969909, 12.193608, 0.000051, 28.000043, 114.499679, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 1560, "7_11_door", "cj_sheetmetal2", 0xFFFFFFFF);
    bennyxadaws = CreateDynamicObject(1897, 1294.347656, 682.597778, 9.988677, 89.999992, 1039.271362, -89.771011, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 2772, "airp_prop", "CJ_red_COUNTER", 0x00000000);
    bennyxadaws = CreateDynamicObject(1897, 1295.142456, 681.959106, 11.168689, 0.000029, 720.000000, -155.499847, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 2772, "airp_prop", "CJ_red_COUNTER", 0x00000000);
    bennyxadaws = CreateDynamicObject(1897, 1295.187500, 682.981384, 9.988677, 89.999992, 989.270874, -89.770828, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 2772, "airp_prop", "CJ_red_COUNTER", 0x00000000);
    bennyxadaws = CreateDynamicObject(1897, 1294.973022, 682.270568, 10.612533, 29.999959, 900.000000, -155.499832, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 2772, "airp_prop", "CJ_red_COUNTER", 0x00000000);
    bennyxadaws = CreateDynamicObject(1897, 1294.735107, 682.851013, 12.148704, 89.999992, 1734.270629, -89.770767, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 2772, "airp_prop", "CJ_red_COUNTER", 0x00000000);
    bennyxadaws = CreateDynamicObject(16332, 1294.321411, 683.836669, 11.696895, 0.000059, 0.000036, 114.499656, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, -1, "none", "none", 0xFFFFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 2, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    bennyxadaws = CreateDynamicObject(19948, 1295.195922, 681.820739, 9.937377, 0.000059, 30.200052, 114.499656, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(bennyxadaws, 1, 7103, "vgnplantgen", "metalwheel5_128", 0xFFFFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 2, 19480, "signsurf", "sign", 0x00000000);
    bennyxadaws = CreateDynamicObject(16332, 1294.516723, 683.408325, 12.326901, 0.000059, 90.000099, 114.499656, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 19480, "signsurf", "sign", 0xFFFFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 2, 19480, "signsurf", "sign", 0x00000000);
    bennyxadaws = CreateDynamicObject(16332, 1294.827758, 682.725158, 12.326901, 0.000059, 90.000099, 114.499656, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 19480, "signsurf", "sign", 0xFFFFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 2, 19480, "signsurf", "sign", 0x00000000);
    bennyxadaws = CreateDynamicObject(16332, 1295.135009, 682.051330, 12.326901, 0.000059, 90.000099, 114.499656, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 19480, "signsurf", "sign", 0xFFFFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 2, 19480, "signsurf", "sign", 0x00000000);
    bennyxadaws = CreateDynamicObject(19873, 1295.177246, 681.853942, 12.264039, 0.000036, 89.999923, -155.499877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 14548, "ab_cargo_int", "metal_frame", 0xFFFFFFFF);
    bennyxadaws = CreateDynamicObject(16332, 1295.234375, 681.833557, 10.568164, 0.000059, 180.000000, 114.499656, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 19480, "signsurf", "sign", 0xFFFFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 2, 19480, "signsurf", "sign", 0x00000000);
    bennyxadaws = CreateDynamicObject(16332, 1295.234375, 681.833557, 11.318898, 0.000059, 180.000000, 114.499656, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 19480, "signsurf", "sign", 0xFFFFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 2, 19480, "signsurf", "sign", 0x00000000);
    bennyxadaws = CreateDynamicObject(16332, 1295.234375, 681.833557, 12.059619, 0.000059, 180.000000, 114.499656, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 19480, "signsurf", "sign", 0xFFFFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 2, 19480, "signsurf", "sign", 0x00000000);
    bennyxadaws = CreateDynamicObject(19873, 1295.181274, 681.844848, 10.094037, 0.000036, 89.999923, -155.499877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 14548, "ab_cargo_int", "metal_frame", 0xFFFFFFFF);
    bennyxadaws = CreateDynamicObject(19873, 1294.790771, 682.701477, 11.604030, 0.000036, 89.999923, -155.499877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 1560, "7_11_door", "cj_sheetmetal2", 0xFFFFFFFF);
    bennyxadaws = CreateDynamicObject(1974, 1294.655883, 683.000061, 12.193608, 0.000059, 28.000040, 114.499656, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 1560, "7_11_door", "cj_sheetmetal2", 0xFFFFFFFF);
    bennyxadaws = CreateDynamicObject(1897, 1268.567382, 727.498596, 9.988677, 89.999992, 1039.385864, -89.885482, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 2772, "airp_prop", "CJ_red_COUNTER", 0x00000000);
    bennyxadaws = CreateDynamicObject(1897, 1269.362182, 726.859924, 11.168689, 0.000025, 720.000000, -155.499801, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 2772, "airp_prop", "CJ_red_COUNTER", 0x00000000);
    bennyxadaws = CreateDynamicObject(1897, 1269.407226, 727.882202, 9.988677, 89.999992, 989.385437, -89.885391, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 2772, "airp_prop", "CJ_red_COUNTER", 0x00000000);
    bennyxadaws = CreateDynamicObject(1897, 1269.192749, 727.171386, 10.612533, 29.999954, 900.000000, -155.499786, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 2772, "airp_prop", "CJ_red_COUNTER", 0x00000000);
    bennyxadaws = CreateDynamicObject(1897, 1268.954833, 727.751831, 12.148704, 89.999992, 1734.385253, -89.885360, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 2772, "airp_prop", "CJ_red_COUNTER", 0x00000000);
    bennyxadaws = CreateDynamicObject(16332, 1268.541137, 728.737487, 11.696895, 0.000066, 0.000033, 114.499633, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, -1, "none", "none", 0xFFFFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 2, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    bennyxadaws = CreateDynamicObject(19948, 1269.415649, 726.721557, 9.937377, 0.000066, 30.200048, 114.499633, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(bennyxadaws, 1, 7103, "vgnplantgen", "metalwheel5_128", 0xFFFFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 2, 19480, "signsurf", "sign", 0x00000000);
    bennyxadaws = CreateDynamicObject(16332, 1268.736450, 728.309143, 12.326901, 0.000066, 90.000099, 114.499633, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 19480, "signsurf", "sign", 0xFFFFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 2, 19480, "signsurf", "sign", 0x00000000);
    bennyxadaws = CreateDynamicObject(16332, 1269.047485, 727.625976, 12.326901, 0.000066, 90.000099, 114.499633, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 19480, "signsurf", "sign", 0xFFFFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 2, 19480, "signsurf", "sign", 0x00000000);
    bennyxadaws = CreateDynamicObject(16332, 1269.354736, 726.952148, 12.326901, 0.000066, 90.000099, 114.499633, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 19480, "signsurf", "sign", 0xFFFFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 2, 19480, "signsurf", "sign", 0x00000000);
    bennyxadaws = CreateDynamicObject(19873, 1269.396972, 726.754760, 12.264039, 0.000033, 89.999916, -155.499832, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 14548, "ab_cargo_int", "metal_frame", 0xFFFFFFFF);
    bennyxadaws = CreateDynamicObject(16332, 1269.454101, 726.734375, 10.568164, 0.000066, 180.000000, 114.499633, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 19480, "signsurf", "sign", 0xFFFFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 2, 19480, "signsurf", "sign", 0x00000000);
    bennyxadaws = CreateDynamicObject(16332, 1269.454101, 726.734375, 11.318898, 0.000066, 180.000000, 114.499633, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 19480, "signsurf", "sign", 0xFFFFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 2, 19480, "signsurf", "sign", 0x00000000);
    bennyxadaws = CreateDynamicObject(16332, 1269.454101, 726.734375, 12.059619, 0.000066, 180.000000, 114.499633, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 19480, "signsurf", "sign", 0xFFFFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 2, 19480, "signsurf", "sign", 0x00000000);
    bennyxadaws = CreateDynamicObject(19873, 1269.401000, 726.745666, 10.094037, 0.000033, 89.999916, -155.499832, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 14548, "ab_cargo_int", "metal_frame", 0xFFFFFFFF);
    bennyxadaws = CreateDynamicObject(19873, 1269.010498, 727.602294, 11.604030, 0.000033, 89.999916, -155.499832, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 1560, "7_11_door", "cj_sheetmetal2", 0xFFFFFFFF);
    bennyxadaws = CreateDynamicObject(1974, 1268.875610, 727.900878, 12.193608, 0.000066, 28.000036, 114.499633, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 1560, "7_11_door", "cj_sheetmetal2", 0xFFFFFFFF);
    bennyxadaws = CreateDynamicObject(1897, 1267.947631, 747.700744, 9.988677, 89.999992, 1174.471801, -44.971385, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 2772, "airp_prop", "CJ_red_COUNTER", 0x00000000);
    bennyxadaws = CreateDynamicObject(1897, 1267.152954, 748.339416, 11.168689, 0.000015, 720.000000, 24.500225, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 2772, "airp_prop", "CJ_red_COUNTER", 0x00000000);
    bennyxadaws = CreateDynamicObject(1897, 1267.107788, 747.317077, 9.988677, 89.999992, 1124.471435, -44.971359, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 2772, "airp_prop", "CJ_red_COUNTER", 0x00000000);
    bennyxadaws = CreateDynamicObject(1897, 1267.322387, 748.027893, 10.612533, 29.999935, 900.000000, 24.500234, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 2772, "airp_prop", "CJ_red_COUNTER", 0x00000000);
    bennyxadaws = CreateDynamicObject(1897, 1267.560180, 747.447448, 12.148704, 89.999992, 1869.471313, -44.971355, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 2772, "airp_prop", "CJ_red_COUNTER", 0x00000000);
    bennyxadaws = CreateDynamicObject(16332, 1267.973999, 746.461730, 11.696895, 0.000070, 0.000023, -65.500335, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, -1, "none", "none", 0xFFFFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 2, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    bennyxadaws = CreateDynamicObject(19948, 1267.099487, 748.477722, 9.937377, 0.000070, 30.200037, -65.500335, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(bennyxadaws, 1, 7103, "vgnplantgen", "metalwheel5_128", 0xFFFFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 2, 19480, "signsurf", "sign", 0x00000000);
    bennyxadaws = CreateDynamicObject(16332, 1267.778686, 746.890197, 12.326901, 0.000070, 90.000091, -65.500335, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 19480, "signsurf", "sign", 0xFFFFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 2, 19480, "signsurf", "sign", 0x00000000);
    bennyxadaws = CreateDynamicObject(16332, 1267.467651, 747.573303, 12.326901, 0.000070, 90.000091, -65.500335, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 19480, "signsurf", "sign", 0xFFFFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 2, 19480, "signsurf", "sign", 0x00000000);
    bennyxadaws = CreateDynamicObject(16332, 1267.160278, 748.247131, 12.326901, 0.000070, 90.000091, -65.500335, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 19480, "signsurf", "sign", 0xFFFFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 2, 19480, "signsurf", "sign", 0x00000000);
    bennyxadaws = CreateDynamicObject(19873, 1267.118041, 748.444519, 12.264039, 0.000023, 89.999908, 24.500194, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 14548, "ab_cargo_int", "metal_frame", 0xFFFFFFFF);
    bennyxadaws = CreateDynamicObject(16332, 1267.060913, 748.464904, 10.568164, 0.000070, 180.000000, -65.500335, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 19480, "signsurf", "sign", 0xFFFFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 2, 19480, "signsurf", "sign", 0x00000000);
    bennyxadaws = CreateDynamicObject(16332, 1267.060913, 748.464904, 11.318898, 0.000070, 180.000000, -65.500335, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 19480, "signsurf", "sign", 0xFFFFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 2, 19480, "signsurf", "sign", 0x00000000);
    bennyxadaws = CreateDynamicObject(16332, 1267.060913, 748.464904, 12.059619, 0.000070, 180.000000, -65.500335, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 19480, "signsurf", "sign", 0xFFFFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 2, 19480, "signsurf", "sign", 0x00000000);
    bennyxadaws = CreateDynamicObject(19873, 1267.114135, 748.453674, 10.094037, 0.000023, 89.999908, 24.500194, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 14548, "ab_cargo_int", "metal_frame", 0xFFFFFFFF);
    bennyxadaws = CreateDynamicObject(19873, 1267.504516, 747.596984, 11.604030, 0.000023, 89.999908, 24.500194, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 1560, "7_11_door", "cj_sheetmetal2", 0xFFFFFFFF);
    bennyxadaws = CreateDynamicObject(1974, 1267.639526, 747.298400, 12.193608, 0.000070, 28.000024, -65.500335, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 1560, "7_11_door", "cj_sheetmetal2", 0xFFFFFFFF);
    bennyxadaws = CreateDynamicObject(2960, 1333.999877, 690.040832, 18.044343, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18641, "flashlight1", "metalblack1-2", 0x00000000);
    bennyxadaws = CreateDynamicObject(2960, 1329.400146, 690.040832, 18.044343, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18641, "flashlight1", "metalblack1-2", 0x00000000);
    bennyxadaws = CreateDynamicObject(2960, 1324.850341, 690.040832, 18.044343, 0.000000, 0.000037, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18641, "flashlight1", "metalblack1-2", 0x00000000);
    bennyxadaws = CreateDynamicObject(2960, 1320.250610, 690.040832, 18.044343, 0.000000, 0.000037, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18641, "flashlight1", "metalblack1-2", 0x00000000);
    bennyxadaws = CreateDynamicObject(2960, 1315.640014, 690.040832, 18.044343, 0.000000, 0.000037, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18641, "flashlight1", "metalblack1-2", 0x00000000);
    bennyxadaws = CreateDynamicObject(2960, 1311.040283, 690.040832, 18.044343, 0.000000, 0.000037, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18641, "flashlight1", "metalblack1-2", 0x00000000);
    bennyxadaws = CreateDynamicObject(2960, 1306.490478, 690.040832, 18.044343, 0.000000, 0.000045, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18641, "flashlight1", "metalblack1-2", 0x00000000);
    bennyxadaws = CreateDynamicObject(2960, 1301.890747, 690.040832, 18.044343, 0.000000, 0.000045, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18641, "flashlight1", "metalblack1-2", 0x00000000);
    bennyxadaws = CreateDynamicObject(2960, 1297.290649, 690.040832, 18.044343, 0.000000, 0.000037, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18641, "flashlight1", "metalblack1-2", 0x00000000);
    bennyxadaws = CreateDynamicObject(2960, 1292.690917, 690.040832, 18.044343, 0.000000, 0.000037, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18641, "flashlight1", "metalblack1-2", 0x00000000);
    bennyxadaws = CreateDynamicObject(2960, 1288.141113, 690.040832, 18.044343, 0.000000, 0.000045, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18641, "flashlight1", "metalblack1-2", 0x00000000);
    bennyxadaws = CreateDynamicObject(2960, 1283.541381, 690.040832, 18.044343, 0.000000, 0.000045, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18641, "flashlight1", "metalblack1-2", 0x00000000);
    bennyxadaws = CreateDynamicObject(2960, 1278.930786, 690.040832, 18.044343, 0.000000, 0.000045, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18641, "flashlight1", "metalblack1-2", 0x00000000);
    bennyxadaws = CreateDynamicObject(2960, 1274.811035, 688.210205, 18.044343, 0.000029, 0.000014, 89.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18641, "flashlight1", "metalblack1-2", 0x00000000);
    bennyxadaws = CreateDynamicObject(2960, 1277.251220, 690.060852, 18.054342, 0.000000, 0.000045, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18641, "flashlight1", "metalblack1-2", 0x00000000);
    bennyxadaws = CreateDynamicObject(2960, 1274.811035, 692.820068, 18.044342, 0.000029, 0.000014, 89.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18641, "flashlight1", "metalblack1-2", 0x00000000);
    bennyxadaws = CreateDynamicObject(2960, 1274.811035, 697.430175, 18.044340, 0.000029, 0.000014, 89.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18641, "flashlight1", "metalblack1-2", 0x00000000);
    bennyxadaws = CreateDynamicObject(2960, 1274.811035, 702.049682, 18.044340, 0.000029, 0.000014, 89.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18641, "flashlight1", "metalblack1-2", 0x00000000);
    bennyxadaws = CreateDynamicObject(2960, 1274.811035, 706.659423, 18.044340, 0.000029, 0.000014, 89.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18641, "flashlight1", "metalblack1-2", 0x00000000);
    bennyxadaws = CreateDynamicObject(2960, 1274.811035, 711.259887, 18.044343, 0.000037, 0.000014, 89.999885, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18641, "flashlight1", "metalblack1-2", 0x00000000);
    bennyxadaws = CreateDynamicObject(2960, 1274.811035, 715.869750, 18.044342, 0.000037, 0.000014, 89.999885, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18641, "flashlight1", "metalblack1-2", 0x00000000);
    bennyxadaws = CreateDynamicObject(2960, 1274.811035, 720.479858, 18.044340, 0.000037, 0.000014, 89.999885, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18641, "flashlight1", "metalblack1-2", 0x00000000);
    bennyxadaws = CreateDynamicObject(2960, 1274.811035, 725.099365, 18.044340, 0.000037, 0.000014, 89.999885, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18641, "flashlight1", "metalblack1-2", 0x00000000);
    bennyxadaws = CreateDynamicObject(2960, 1274.811035, 729.709106, 18.044340, 0.000037, 0.000014, 89.999885, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18641, "flashlight1", "metalblack1-2", 0x00000000);
    bennyxadaws = CreateDynamicObject(2960, 1274.811035, 734.329711, 18.044343, 0.000045, 0.000014, 89.999862, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18641, "flashlight1", "metalblack1-2", 0x00000000);
    bennyxadaws = CreateDynamicObject(2960, 1274.811035, 738.939575, 18.044342, 0.000045, 0.000014, 89.999862, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18641, "flashlight1", "metalblack1-2", 0x00000000);
    bennyxadaws = CreateDynamicObject(2960, 1274.811035, 743.549682, 18.044340, 0.000045, 0.000014, 89.999862, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18641, "flashlight1", "metalblack1-2", 0x00000000);
    bennyxadaws = CreateDynamicObject(2960, 1274.811035, 748.169189, 18.044340, 0.000045, 0.000014, 89.999862, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18641, "flashlight1", "metalblack1-2", 0x00000000);
    bennyxadaws = CreateDynamicObject(2960, 1274.811035, 752.778930, 18.044340, 0.000045, 0.000014, 89.999862, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18641, "flashlight1", "metalblack1-2", 0x00000000);
    bennyxadaws = CreateDynamicObject(2960, 1274.811035, 757.399353, 18.044343, 0.000052, 0.000014, 89.999839, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18641, "flashlight1", "metalblack1-2", 0x00000000);
    bennyxadaws = CreateDynamicObject(2960, 1274.811035, 762.009216, 18.044342, 0.000052, 0.000014, 89.999839, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18641, "flashlight1", "metalblack1-2", 0x00000000);
    bennyxadaws = CreateDynamicObject(2960, 1274.811035, 766.619323, 18.044340, 0.000052, 0.000014, 89.999839, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18641, "flashlight1", "metalblack1-2", 0x00000000);
    bennyxadaws = CreateDynamicObject(2960, 1274.811035, 771.238830, 18.044340, 0.000052, 0.000014, 89.999839, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18641, "flashlight1", "metalblack1-2", 0x00000000);
    bennyxadaws = CreateDynamicObject(2960, 1274.811035, 775.848571, 18.044340, 0.000052, 0.000014, 89.999839, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18641, "flashlight1", "metalblack1-2", 0x00000000);
    bennyxadaws = CreateDynamicObject(2960, 1272.935546, 778.354614, 18.044340, 0.000029, -0.000029, 179.999603, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18641, "flashlight1", "metalblack1-2", 0x00000000);
    bennyxadaws = CreateDynamicObject(2960, 1268.316040, 778.354614, 18.044340, 0.000029, -0.000029, 179.999603, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18641, "flashlight1", "metalblack1-2", 0x00000000);
    bennyxadaws = CreateDynamicObject(2960, 1263.706298, 778.354614, 18.044340, 0.000029, -0.000029, 179.999603, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18641, "flashlight1", "metalblack1-2", 0x00000000);
    bennyxadaws = CreateDynamicObject(2960, 1272.935546, 754.074768, 18.054342, 0.000029, -0.000037, 179.999557, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18641, "flashlight1", "metalblack1-2", 0x00000000);
    bennyxadaws = CreateDynamicObject(2960, 1268.316040, 754.074768, 18.054338, 0.000029, -0.000037, 179.999557, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18641, "flashlight1", "metalblack1-2", 0x00000000);
    bennyxadaws = CreateDynamicObject(2960, 1263.706298, 754.074768, 18.054338, 0.000029, -0.000037, 179.999557, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18641, "flashlight1", "metalblack1-2", 0x00000000);
    bennyxadaws = CreateDynamicObject(2960, 1272.935546, 738.544494, 18.054342, 0.000029, -0.000045, 179.999511, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18641, "flashlight1", "metalblack1-2", 0x00000000);
    bennyxadaws = CreateDynamicObject(2960, 1268.316040, 738.544494, 18.054338, 0.000029, -0.000045, 179.999511, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18641, "flashlight1", "metalblack1-2", 0x00000000);
    bennyxadaws = CreateDynamicObject(2960, 1263.706298, 738.544494, 18.054338, 0.000029, -0.000045, 179.999511, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18641, "flashlight1", "metalblack1-2", 0x00000000);
    bennyxadaws = CreateDynamicObject(2960, 1272.935546, 723.043823, 18.054342, 0.000029, -0.000052, 179.999465, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18641, "flashlight1", "metalblack1-2", 0x00000000);
    bennyxadaws = CreateDynamicObject(2960, 1268.316040, 723.043823, 18.054338, 0.000029, -0.000052, 179.999465, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18641, "flashlight1", "metalblack1-2", 0x00000000);
    bennyxadaws = CreateDynamicObject(2960, 1263.706298, 723.043823, 18.054338, 0.000029, -0.000052, 179.999465, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18641, "flashlight1", "metalblack1-2", 0x00000000);
    bennyxadaws = CreateDynamicObject(2960, 1288.061889, 687.938659, 18.064342, -0.000014, -0.000037, -90.000244, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18641, "flashlight1", "metalblack1-2", 0x00000000);
    bennyxadaws = CreateDynamicObject(2960, 1288.051879, 685.919067, 18.054338, -0.000014, -0.000037, -90.000244, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18641, "flashlight1", "metalblack1-2", 0x00000000);
    bennyxadaws = CreateDynamicObject(2960, 1288.051879, 681.309326, 18.054338, -0.000014, -0.000037, -90.000244, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18641, "flashlight1", "metalblack1-2", 0x00000000);
    bennyxadaws = CreateDynamicObject(2960, 1303.811767, 687.938659, 18.064342, -0.000022, -0.000037, -90.000221, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18641, "flashlight1", "metalblack1-2", 0x00000000);
    bennyxadaws = CreateDynamicObject(2960, 1303.801757, 685.919067, 18.054338, -0.000022, -0.000037, -90.000221, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18641, "flashlight1", "metalblack1-2", 0x00000000);
    bennyxadaws = CreateDynamicObject(2960, 1303.801757, 681.309326, 18.054338, -0.000022, -0.000037, -90.000221, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18641, "flashlight1", "metalblack1-2", 0x00000000);
    bennyxadaws = CreateDynamicObject(2960, 1319.242187, 687.938659, 18.064342, -0.000029, -0.000037, -90.000198, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18641, "flashlight1", "metalblack1-2", 0x00000000);
    bennyxadaws = CreateDynamicObject(2960, 1319.232177, 685.919067, 18.054338, -0.000029, -0.000037, -90.000198, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18641, "flashlight1", "metalblack1-2", 0x00000000);
    bennyxadaws = CreateDynamicObject(2960, 1319.232177, 681.309326, 18.054338, -0.000029, -0.000037, -90.000198, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18641, "flashlight1", "metalblack1-2", 0x00000000);
    bennyxadaws = CreateDynamicObject(19088, 1322.888793, 690.055419, 15.178762, 0.000018, 180.000000, -90.500251, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 16640, "a51", "concreteyellow256 copy", 0x00000000);
    bennyxadaws = CreateDynamicObject(19087, 1322.890014, 690.056518, 16.342838, 0.000018, -0.000039, -90.500251, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 16640, "a51", "concreteyellow256 copy", 0x00000000);
    bennyxadaws = CreateDynamicObject(1952, 1322.889892, 690.054443, 13.878774, -0.000018, 990.000000, 89.499839, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(bennyxadaws, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(bennyxadaws, 2, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    bennyxadaws = CreateDynamicObject(1974, 1322.891235, 690.065673, 13.898748, 0.000018, -0.000048, -90.500251, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    bennyxadaws = CreateDynamicObject(19088, 1303.978393, 690.055419, 15.178762, 0.000012, 180.000000, -90.500228, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 16640, "a51", "concreteyellow256 copy", 0x00000000);
    bennyxadaws = CreateDynamicObject(19087, 1303.979614, 690.056518, 16.342838, 0.000012, -0.000039, -90.500228, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 16640, "a51", "concreteyellow256 copy", 0x00000000);
    bennyxadaws = CreateDynamicObject(1952, 1303.979492, 690.054443, 13.878774, -0.000012, 990.000000, 89.499816, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(bennyxadaws, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(bennyxadaws, 2, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    bennyxadaws = CreateDynamicObject(1974, 1303.980834, 690.065673, 13.898748, 0.000012, -0.000048, -90.500228, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    bennyxadaws = CreateDynamicObject(19088, 1284.827270, 690.055419, 15.178762, 0.000003, 180.000000, -90.500205, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 16640, "a51", "concreteyellow256 copy", 0x00000000);
    bennyxadaws = CreateDynamicObject(19087, 1284.828491, 690.056518, 16.342838, 0.000003, -0.000039, -90.500205, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 16640, "a51", "concreteyellow256 copy", 0x00000000);
    bennyxadaws = CreateDynamicObject(1952, 1284.828369, 690.054443, 13.878774, -0.000003, 990.000000, 89.499794, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(bennyxadaws, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(bennyxadaws, 2, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    bennyxadaws = CreateDynamicObject(1974, 1284.829711, 690.065673, 13.898748, 0.000003, -0.000048, -90.500205, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    bennyxadaws = CreateDynamicObject(19088, 1274.855102, 718.548339, 15.178762, 0.000018, 180.000000, -0.500281, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 16640, "a51", "concreteyellow256 copy", 0x00000000);
    bennyxadaws = CreateDynamicObject(19087, 1274.854003, 718.549560, 16.342838, 0.000018, -0.000009, -0.500281, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 16640, "a51", "concreteyellow256 copy", 0x00000000);
    bennyxadaws = CreateDynamicObject(1952, 1274.856079, 718.549438, 13.878774, -0.000018, 990.000000, 179.499633, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(bennyxadaws, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(bennyxadaws, 2, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    bennyxadaws = CreateDynamicObject(1974, 1274.844848, 718.550781, 13.898748, 0.000018, -0.000018, -0.500281, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    bennyxadaws = CreateDynamicObject(19088, 1274.855102, 737.558288, 15.178762, 0.000018, 180.000000, -0.500281, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 16640, "a51", "concreteyellow256 copy", 0x00000000);
    bennyxadaws = CreateDynamicObject(19087, 1274.854003, 737.559509, 16.342838, 0.000018, -0.000001, -0.500281, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 16640, "a51", "concreteyellow256 copy", 0x00000000);
    bennyxadaws = CreateDynamicObject(1952, 1274.856079, 737.559387, 13.878774, -0.000018, 990.000000, 179.499588, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(bennyxadaws, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(bennyxadaws, 2, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    bennyxadaws = CreateDynamicObject(1974, 1274.844848, 737.560729, 13.898748, 0.000018, -0.000009, -0.500281, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    bennyxadaws = CreateDynamicObject(19088, 1274.855102, 756.948608, 15.178762, 0.000018, 180.000000, -0.500281, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 16640, "a51", "concreteyellow256 copy", 0x00000000);
    bennyxadaws = CreateDynamicObject(19087, 1274.854003, 756.949829, 16.342838, 0.000018, 0.000004, -0.500281, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 16640, "a51", "concreteyellow256 copy", 0x00000000);
    bennyxadaws = CreateDynamicObject(1952, 1274.856079, 756.949707, 13.878774, -0.000018, 990.000000, 179.499542, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(bennyxadaws, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(bennyxadaws, 2, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    bennyxadaws = CreateDynamicObject(1974, 1274.844848, 756.951049, 13.898748, 0.000018, -0.000001, -0.500281, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    bennyxadaws = CreateDynamicObject(18981, 1323.765991, 691.539550, 19.277511, 180.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 14569, "traidman", "darkgrey_carpet_256", 0x00000000);
    bennyxadaws = CreateDynamicObject(18981, 1298.786865, 691.539550, 19.277511, 180.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 14569, "traidman", "darkgrey_carpet_256", 0x00000000);
    bennyxadaws = CreateDynamicObject(18981, 1273.887329, 691.539550, 19.277511, 180.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 14569, "traidman", "darkgrey_carpet_256", 0x00000000);
    bennyxadaws = CreateDynamicObject(18981, 1273.887329, 716.509643, 19.287511, 180.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 14569, "traidman", "darkgrey_carpet_256", 0x00000000);
    bennyxadaws = CreateDynamicObject(18981, 1273.887329, 741.479248, 19.287511, 180.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 14569, "traidman", "darkgrey_carpet_256", 0x00000000);
    bennyxadaws = CreateDynamicObject(18981, 1273.887329, 766.449096, 19.287511, 180.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 14569, "traidman", "darkgrey_carpet_256", 0x00000000);
    bennyxadaws = CreateDynamicObject(18981, 1278.877441, 766.449096, 19.277511, 180.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 14569, "traidman", "darkgrey_carpet_256", 0x00000000);
    bennyxadaws = CreateDynamicObject(18981, 1278.877441, 741.469116, 19.277511, 180.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 14569, "traidman", "darkgrey_carpet_256", 0x00000000);
    bennyxadaws = CreateDynamicObject(18981, 1278.877441, 716.509216, 19.277511, 180.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 14569, "traidman", "darkgrey_carpet_256", 0x00000000);
    bennyxadaws = CreateDynamicObject(18981, 1303.817993, 697.219360, 19.277511, 180.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 14569, "traidman", "darkgrey_carpet_256", 0x00000000);
    bennyxadaws = CreateDynamicObject(18981, 1324.418945, 697.219360, 19.257511, 180.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 14569, "traidman", "darkgrey_carpet_256", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1327.239990, 679.428833, 18.348125, 0.000000, 360.000000, -179.999847, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1317.259521, 679.428833, 18.348125, 0.000000, 360.000000, -179.999847, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1307.269775, 679.428833, 18.348125, 0.000000, 360.000000, -179.999801, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1297.289306, 679.428833, 18.348125, 0.000000, 360.000000, -179.999801, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1287.291259, 679.428833, 18.348125, 0.000000, 360.000000, -179.999801, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1277.310791, 679.428833, 18.348125, 0.000000, 360.000000, -179.999801, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1261.803833, 714.924438, 18.348125, 359.999969, 360.000000, -89.999717, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1261.803833, 724.914123, 18.348125, 359.999969, 360.000000, -89.999717, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1261.803833, 734.885559, 18.348125, -0.000029, 360.000000, -89.999694, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1261.803833, 744.875244, 18.348125, -0.000029, 360.000000, -89.999694, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1261.803833, 754.855712, 18.348125, -0.000037, 360.000000, -89.999671, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1261.803833, 764.845397, 18.348125, -0.000037, 360.000000, -89.999671, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1290.943725, 740.395751, 18.348125, -0.000052, 360.000000, -89.999626, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1290.943725, 750.385437, 18.348125, -0.000052, 360.000000, -89.999626, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1290.943725, 760.365905, 18.348125, -0.000060, 360.000000, -89.999603, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1290.923706, 770.355590, 18.348125, -0.000052, 360.000000, -89.999626, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1290.973754, 713.745117, 18.348125, -0.000060, 360.000000, -89.999603, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1290.943725, 720.425476, 18.348125, -0.000060, 360.000000, -89.999603, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1290.943725, 730.405944, 18.348125, -0.000068, 360.000000, -89.999580, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1296.453369, 709.325134, 18.348125, -0.000060, 360.000000, 0.000395, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1306.452514, 709.325134, 18.348125, -0.000060, 360.000000, 0.000395, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1316.403808, 709.325134, 18.348125, -0.000060, 360.000000, 0.000395, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1267.642700, 679.448852, 6.878128, 0.000000, 270.000000, -179.999618, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1263.812133, 679.438842, 6.868124, 0.000000, 270.000000, -179.999618, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1272.591796, 679.438842, 6.878128, 0.000000, 270.000000, -179.999664, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1261.783813, 707.433959, 6.898119, -0.000029, 270.000000, -89.999694, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1288.077270, 679.438842, 6.878129, 0.000000, 270.000000, -179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(19383, 1274.909179, 681.553527, 11.602707, 0.000014, 0.000000, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18882, "hugebowls", "woodpanel1", 0x00000000);
    bennyxadaws = CreateDynamicObject(19353, 1274.889160, 684.283386, 11.592707, 0.000014, 0.000000, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18882, "hugebowls", "woodpanel1", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1270.022338, 682.448669, 13.698108, 89.999992, 360.000183, 0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18882, "hugebowls", "woodpanel1", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1266.402221, 682.448669, 13.708108, 89.999992, 360.000183, 0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18882, "hugebowls", "woodpanel1", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1268.831909, 702.688964, 13.848109, 89.999992, 360.000183, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18882, "hugebowls", "woodpanel1", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1263.931518, 702.708984, 13.838109, 89.999992, 360.000183, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18882, "hugebowls", "woodpanel1", 0x00000000);
    bennyxadaws = CreateDynamicObject(19445, 1264.017578, 697.585937, 14.398815, 90.000000, 360.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18882, "hugebowls", "woodpanel1", 0x00000000);
    bennyxadaws = CreateDynamicObject(19912, 1271.287963, 709.084045, 16.997007, 360.000061, 360.000030, 89.999809, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "black64", 0x90FFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 1, 10765, "airportgnd_sfse", "white", 0x90FFFFFF);
    SetDynamicObjectMaterial(bennyxadaws, 2, 10765, "airportgnd_sfse", "black64", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1268.871948, 704.198181, 13.838109, 89.999992, 360.000244, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18882, "hugebowls", "woodpanel1", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1263.971557, 704.218200, 13.828107, 89.999992, 360.000244, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18882, "hugebowls", "woodpanel1", 0x00000000);
    bennyxadaws = CreateDynamicObject(19445, 1266.937866, 709.186645, 14.398815, 89.999992, 540.000000, -90.000007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18882, "hugebowls", "woodpanel1", 0x00000000);
    bennyxadaws = CreateDynamicObject(19445, 1264.017578, 709.166625, 14.398815, 89.999992, 540.000000, -90.000007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18882, "hugebowls", "woodpanel1", 0x00000000);
    bennyxadaws = CreateDynamicObject(19445, 1269.608276, 709.196655, 14.398815, 89.999992, 540.000000, -90.000007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18882, "hugebowls", "woodpanel1", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1326.301757, 709.325134, 18.348125, -0.000060, 360.000000, 0.000395, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(18766, 1332.272583, 709.355163, 18.348125, -0.000060, 360.000000, 0.000395, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(19426, 1269.497924, 697.578186, 18.022823, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18882, "hugebowls", "woodpanel1", 0x00000000);
    bennyxadaws = CreateDynamicObject(2357, 1264.762939, 701.194702, 14.668807, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 16093, "a51_ext", "ws_whitewall2_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(19445, 1262.257446, 704.476440, 15.148819, 180.000000, 540.000000, -0.000007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 6867, "vgnpwrmainbld", "sw_wallbrick_02", 0x00000000);
    bennyxadaws = CreateDynamicObject(19445, 1262.257446, 694.846435, 15.148819, 180.000000, 540.000000, -0.000007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 6867, "vgnpwrmainbld", "sw_wallbrick_02", 0x00000000);
    bennyxadaws = CreateDynamicObject(19445, 1262.267456, 692.586486, 15.178820, 180.000000, 540.000000, -0.000007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 6867, "vgnpwrmainbld", "sw_wallbrick_02", 0x00000000);
    bennyxadaws = CreateDynamicObject(2357, 1264.762939, 692.424987, 14.668807, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 16093, "a51_ext", "ws_whitewall2_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(19087, 1332.765869, 681.402099, 14.213713, 0.000058, 89.999977, 179.499526, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    bennyxadaws = CreateDynamicObject(19087, 1332.761596, 680.891662, 13.973711, 0.000058, 89.999977, 179.499526, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    bennyxadaws = CreateDynamicObject(19087, 1302.465332, 681.402099, 14.213713, 0.000058, 89.999969, 179.499481, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    bennyxadaws = CreateDynamicObject(19087, 1302.461059, 680.891662, 13.973711, 0.000058, 89.999969, 179.499481, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    bennyxadaws = CreateDynamicObject(19087, 1263.622802, 724.270202, 14.213713, 0.000065, 89.999961, 89.499366, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    bennyxadaws = CreateDynamicObject(19087, 1263.112304, 724.274353, 13.973711, 0.000065, 89.999961, 89.499366, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    bennyxadaws = CreateDynamicObject(19087, 1263.622802, 755.270568, 14.213713, 0.000073, 89.999961, 89.499343, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    bennyxadaws = CreateDynamicObject(19087, 1263.112304, 755.274719, 13.973711, 0.000073, 89.999961, 89.499343, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    bennyxadaws = CreateDynamicObject(4735, 1291.658569, 724.612854, 18.226274, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(bennyxadaws, 0, "BENGKEL", 120, "Arial", 65, 1, 0xFF000000, 0x00000000, 1);
    bennyxadaws = CreateDynamicObject(4735, 1291.658569, 735.122619, 18.226274, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(bennyxadaws, 0, "KOTA", 120, "Arial", 65, 1, 0xFF000000, 0x00000000, 1);
    bennyxadaws = CreateDynamicObject(4735, 1291.658569, 745.232543, 18.226274, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(bennyxadaws, 0, "ATHERLIFE", 120, "Arial", 65, 1, 0xFF000000, 0x00000000, 1);
    bennyxadaws = CreateDynamicObject(18980, 1336.746459, 722.201904, 10.315061, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 7426, "vgncorp1", "brick2", 0x00000000);
    bennyxadaws = CreateDynamicObject(18980, 1336.746459, 767.482238, 10.315061, 0.000000, 90.000000, 450.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 7426, "vgncorp1", "brick2", 0x00000000);
    bennyxadaws = CreateDynamicObject(18980, 1323.737426, 779.472534, 10.315061, 0.000000, 90.000000, 540.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 7426, "vgncorp1", "brick2", 0x00000000);
    bennyxadaws = CreateDynamicObject(18980, 1303.588378, 779.452514, 10.305062, 0.000000, 90.000000, 540.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 7426, "vgncorp1", "brick2", 0x00000000);
    bennyxadaws = CreateDynamicObject(19383, 1271.239257, 705.193664, 11.602703, 0.000014, 0.000000, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18882, "hugebowls", "woodpanel1", 0x00000000);
    bennyxadaws = CreateDynamicObject(19353, 1271.189453, 707.553466, 11.592703, 0.000014, 0.000000, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18882, "hugebowls", "woodpanel1", 0x00000000);
    bennyxadaws = CreateDynamicObject(19353, 1264.448486, 686.193359, 14.262701, 180.000015, 90.000000, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18882, "hugebowls", "woodpanel1", 0x00000000);
    bennyxadaws = CreateDynamicObject(19353, 1263.098632, 686.213378, 14.272703, 180.000015, 90.000000, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18882, "hugebowls", "woodpanel1", 0x00000000);
    bennyxadaws = CreateDynamicObject(2608, 1266.530395, 697.937805, 15.038149, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 16093, "a51_ext", "ws_whitewall2_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(2608, 1262.569824, 707.588134, 15.038149, 0.000000, 0.000000, 450.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 16093, "a51_ext", "ws_whitewall2_top", 0x00000000);
    bennyxadaws = CreateDynamicObject(1881, 1277.328735, 697.096069, 9.728337, 89.999992, 892.488342, -57.488323, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    bennyxadaws = CreateDynamicObject(1881, 1277.301025, 696.799682, 9.728337, 89.999992, 842.488342, -57.488323, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    bennyxadaws = CreateDynamicObject(1881, 1277.380493, 696.924682, 9.728337, 89.999992, 967.488342, -57.488323, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    bennyxadaws = CreateDynamicObject(1881, 1277.293579, 697.442749, 9.728337, 89.999992, 688.288330, -57.488323, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    bennyxadaws = CreateDynamicObject(1881, 1276.971801, 697.567993, 9.728337, 89.999992, 778.288330, -57.488323, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    bennyxadaws = CreateDynamicObject(1881, 1277.130981, 697.688598, 9.728337, 89.999992, 811.588195, -57.488323, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    bennyxadaws = CreateDynamicObject(1881, 1277.468872, 697.741577, 9.728337, 89.999992, 748.488220, -57.488323, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    bennyxadaws = CreateDynamicObject(1881, 1277.266845, 698.201049, 9.728337, 89.999992, 687.988220, -57.488323, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    bennyxadaws = CreateDynamicObject(1881, 1277.261596, 698.565307, 9.728337, 89.999992, 717.988220, -57.488323, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    bennyxadaws = CreateDynamicObject(1881, 1277.246704, 698.791381, 9.728337, 89.999992, 667.688232, -57.488323, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    bennyxadaws = CreateDynamicObject(1881, 1277.276489, 699.109985, 9.728337, 89.999992, 687.988220, -57.488323, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    bennyxadaws = CreateDynamicObject(1881, 1276.973999, 699.397827, 9.728337, 89.999992, 777.988220, -57.488323, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    bennyxadaws = CreateDynamicObject(1881, 1277.284057, 699.390991, 9.728337, 89.999992, 777.988220, -57.488323, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    bennyxadaws = CreateDynamicObject(1881, 1277.584106, 699.393676, 9.728337, 89.999992, 777.988220, -57.488323, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    bennyxadaws = CreateDynamicObject(1881, 1277.268798, 699.989868, 9.728337, 89.999992, 687.988220, -57.488323, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    bennyxadaws = CreateDynamicObject(1881, 1277.248535, 700.174438, 9.728337, 89.999992, 719.588256, -57.488323, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    bennyxadaws = CreateDynamicObject(1881, 1277.206542, 700.391479, 9.728337, 89.999992, 679.988220, -57.488323, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    bennyxadaws = CreateDynamicObject(1881, 1277.328735, 701.016967, 9.728337, 89.999992, 892.488342, -57.488323, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    bennyxadaws = CreateDynamicObject(1881, 1277.301025, 700.720581, 9.728337, 89.999992, 842.488342, -57.488323, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    bennyxadaws = CreateDynamicObject(1881, 1277.380493, 700.845581, 9.728337, 89.999992, 967.488342, -57.488323, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    bennyxadaws = CreateDynamicObject(19456, 1337.093627, 674.175354, 11.520966, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5710, "cemetery_law", "brickgrey", 0x00000000);
    bennyxadaws = CreateDynamicObject(19456, 1337.093627, 664.545776, 11.520966, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5710, "cemetery_law", "brickgrey", 0x00000000);
    bennyxadaws = CreateDynamicObject(19456, 1337.093627, 654.924621, 11.520966, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5710, "cemetery_law", "brickgrey", 0x00000000);
    bennyxadaws = CreateDynamicObject(19456, 1337.093627, 645.295043, 11.520966, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5710, "cemetery_law", "brickgrey", 0x00000000);
    bennyxadaws = CreateDynamicObject(19376, 1337.057983, 636.990966, 7.964910, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 5710, "cemetery_law", "brickgrey", 0x00000000);
    bennyxadaws = CreateDynamicObject(18981, 1384.974121, 797.854919, 0.189163, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 7426, "vgncorp1", "brick2", 0x00000000);
    bennyxadaws = CreateDynamicObject(18981, 1366.855834, 797.804870, 0.099164, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 7426, "vgncorp1", "brick2", 0x00000000);
    bennyxadaws = CreateDynamicObject(18981, 1346.025146, 788.872924, 0.139164, 0.000000, 0.000000, 135.100006, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 7426, "vgncorp1", "brick2", 0x00000000);
    bennyxadaws = CreateDynamicObject(2047, 1335.588500, 778.809875, 21.148630, 0.000009, 0.000013, -45.000274, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18646, "matcolours", "red-4", 0x00000000);
    bennyxadaws = CreateDynamicObject(2047, 1335.588500, 778.809875, 21.148630, 0.000046, -0.000013, 134.999618, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 18646, "matcolours", "red-4", 0x00000000);
    bennyxadaws = CreateDynamicObject(2047, 1335.588500, 778.809875, 20.328624, 0.000014, 0.000008, -45.000274, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    bennyxadaws = CreateDynamicObject(2047, 1335.588500, 778.809875, 20.328624, 0.000041, -0.000008, 134.999618, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    bennyxadaws = CreateDynamicObject(2614, 1303.674926, 708.714111, 13.836403, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 1, 10810, "ap_build4e", "redwhite_stripe", 0x00000000);
    bennyxadaws = CreateDynamicObject(2614, 1290.375488, 742.303955, 15.366407, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 1, 10810, "ap_build4e", "redwhite_stripe", 0x00000000);
    bennyxadaws = CreateDynamicObject(2614, 1262.384765, 738.473205, 15.366407, 0.000000, 0.000000, 450.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bennyxadaws, 1, 10810, "ap_build4e", "redwhite_stripe", 0x00000000);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(8614, 1274.862060, 689.091613, 9.663267, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1998, 1263.505859, 702.728210, 9.871438, -0.000007, 0.000000, -89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2008, 1264.491210, 700.780761, 9.865180, -0.000007, 0.000000, -89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1753, 1267.342407, 702.715270, 9.869664, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2725, 1265.910400, 702.691589, 10.270175, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14705, 1265.953857, 702.637207, 10.950400, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19999, 1266.015014, 689.760742, 9.856845, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1714, 1268.533447, 691.735290, 9.854314, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1714, 1268.533447, 693.115295, 9.854314, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1714, 1268.533447, 694.665283, 9.854314, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1714, 1263.363037, 694.665405, 9.854313, 0.000000, 0.000007, 89.999938, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1714, 1263.363037, 693.285278, 9.854314, 0.000000, 0.000007, 89.999938, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1714, 1263.363037, 691.735351, 9.854314, 0.000000, 0.000007, 89.999938, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2002, 1269.524536, 689.244018, 9.887482, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2567, 1266.512817, 687.115783, 11.781159, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19899, 1332.582153, 685.997497, 10.782891, 0.000000, 180.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19899, 1325.082641, 685.997497, 10.782891, 0.000000, 180.000000, 179.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19899, 1322.972167, 685.247314, 10.782891, 0.000000, 180.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19899, 1315.472656, 685.247314, 10.782891, 0.000000, 180.000000, 179.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19899, 1313.181762, 685.247314, 10.782891, 0.000000, 180.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19899, 1305.682250, 685.247314, 10.782891, 0.000000, 180.000000, 179.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19899, 1303.191406, 685.997497, 10.782891, 0.000000, 180.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19899, 1295.691894, 685.997497, 10.782891, 0.000000, 180.000000, 179.999816, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19899, 1293.579589, 685.247314, 10.782891, 0.000000, 180.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19899, 1286.080078, 685.247314, 10.782891, 0.000000, 180.000000, 179.999816, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19899, 1283.780639, 685.997497, 10.782891, 0.000000, 180.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19899, 1276.281127, 685.997497, 10.782891, 0.000000, 180.000000, 179.999725, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19899, 1268.040893, 709.717895, 10.782891, 0.000000, 180.000000, -90.000038, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19899, 1268.040893, 717.217407, 10.782891, 0.000000, 180.000000, 89.999679, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19899, 1266.109863, 719.338195, 10.782891, -0.000014, 180.000000, -89.999992, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19899, 1266.109863, 726.837707, 10.782891, 0.000014, 180.000000, 89.999633, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19899, 1266.109863, 729.118347, 10.782891, -0.000022, 180.000000, -89.999969, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19899, 1266.109863, 736.617858, 10.782891, 0.000022, 180.000000, 89.999610, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19899, 1270.539916, 738.818298, 10.782891, -0.000037, 180.000000, -89.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19899, 1270.539916, 746.317810, 10.782891, 0.000037, 180.000000, 89.999565, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19899, 1270.539916, 748.428527, 10.782891, -0.000045, 180.000000, -89.999900, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19899, 1270.539916, 755.928039, 10.782891, 0.000045, 180.000000, 89.999542, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19899, 1267.589355, 758.208923, 10.782891, -0.000060, 180.000000, -89.999855, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19899, 1267.589355, 765.708435, 10.782891, 0.000060, 180.000000, 89.999496, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1753, 1262.741943, 700.225036, 14.329662, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1753, 1266.741943, 702.255126, 14.329662, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1753, 1262.741943, 691.424865, 14.329662, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1703, 1270.693725, 691.703979, 14.331974, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1703, 1270.693725, 695.733947, 14.331974, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2011, 1262.688110, 688.401428, 14.320581, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2011, 1270.828247, 692.701538, 14.320581, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1822, 1270.153808, 696.216918, 14.330392, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1822, 1270.153808, 688.066833, 14.330392, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2567, 1264.957763, 681.171081, 11.560946, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(939, 1265.164550, 778.474060, 12.279899, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(939, 1271.864501, 778.474060, 12.279899, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19899, 1262.810058, 774.486450, 9.798707, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19899, 1262.810058, 769.666503, 9.798707, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19900, 1324.194580, 680.865356, 9.817429, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19900, 1314.244628, 680.865356, 9.817429, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19900, 1304.163818, 680.865356, 9.817429, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19900, 1284.893310, 680.865356, 9.817429, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19815, 1319.235839, 679.960815, 12.895509, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19815, 1288.145629, 679.960815, 12.895509, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19815, 1262.314208, 738.500854, 13.192743, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1335.179077, 680.262084, 14.113698, -89.999992, -269.044891, 90.454719, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1332.708984, 680.283691, 14.113698, -89.999992, -269.044891, 90.454719, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1081, 1334.929687, 680.873535, 14.453679, -0.000058, 0.000048, -0.500015, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1081, 1334.539550, 680.876953, 14.453679, -0.000058, 0.000048, -0.500015, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1081, 1334.139404, 680.880554, 14.453679, -0.000058, 0.000048, -0.500015, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1081, 1333.729125, 680.884155, 14.453679, -0.000058, 0.000048, -0.500015, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1081, 1333.338989, 680.887329, 14.453679, -0.000058, 0.000048, -0.500015, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1081, 1332.958740, 680.890686, 14.453679, -0.000058, 0.000048, -0.500015, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1304.878540, 680.262084, 14.113698, -89.999992, -269.272247, 90.227340, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1302.408447, 680.283691, 14.113698, -89.999992, -269.272247, 90.227340, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1081, 1304.629150, 680.873535, 14.453679, -0.000058, 0.000055, -0.500015, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1081, 1304.239013, 680.876953, 14.453679, -0.000058, 0.000055, -0.500015, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1081, 1303.838867, 680.880554, 14.453679, -0.000058, 0.000055, -0.500015, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1081, 1303.428588, 680.884155, 14.453679, -0.000058, 0.000055, -0.500015, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1081, 1303.038452, 680.887329, 14.453679, -0.000058, 0.000055, -0.500015, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1081, 1302.658203, 680.890686, 14.453679, -0.000058, 0.000055, -0.500015, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1262.482666, 721.856872, 14.113698, -89.999992, -513.931823, 115.567810, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1262.504394, 724.327087, 14.113698, -89.999992, -513.931823, 115.567810, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1081, 1263.094238, 722.106384, 14.453679, -0.000065, 0.000062, -90.500030, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1081, 1263.097656, 722.496520, 14.453679, -0.000065, 0.000062, -90.500030, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1081, 1263.101196, 722.896667, 14.453679, -0.000065, 0.000062, -90.500030, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1081, 1263.104736, 723.306823, 14.453679, -0.000065, 0.000062, -90.500030, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1081, 1263.107910, 723.696960, 14.453679, -0.000065, 0.000062, -90.500030, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1081, 1263.111328, 724.077331, 14.453679, -0.000065, 0.000062, -90.500030, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1262.482666, 752.857238, 14.113698, -89.999992, -526.108947, 103.390678, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1262.504394, 755.327453, 14.113698, -89.999992, -526.108947, 103.390678, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1081, 1263.094238, 753.106750, 14.453679, -0.000073, 0.000062, -90.500007, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1081, 1263.097656, 753.496887, 14.453679, -0.000073, 0.000062, -90.500007, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1081, 1263.101196, 753.897033, 14.453679, -0.000073, 0.000062, -90.500007, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1081, 1263.104736, 754.307189, 14.453679, -0.000073, 0.000062, -90.500007, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1081, 1263.107910, 754.697326, 14.453679, -0.000073, 0.000062, -90.500007, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1081, 1263.111328, 755.077697, 14.453679, -0.000073, 0.000062, -90.500007, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(931, 1335.330078, 696.222778, 10.899426, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(925, 1335.329345, 699.914855, 10.916460, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(939, 1318.679443, 707.487731, 12.284369, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(939, 1289.069580, 727.817749, 12.284369, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(925, 1289.501586, 743.230041, 10.942803, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11729, 1263.666992, 708.819641, 9.816927, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11729, 1264.336914, 708.819641, 9.816927, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11729, 1264.996826, 708.819641, 9.816927, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11729, 1265.666748, 708.819641, 9.816927, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19899, 1267.030761, 704.607849, 10.782891, 0.000000, 180.000000, -90.000038, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(931, 1335.329345, 699.934936, 13.059433, -0.000014, -0.000007, -89.999961, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(925, 1335.330078, 696.242797, 13.076469, -0.000014, -0.000007, -89.999961, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 1282.167358, 767.272827, 18.700487, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 1282.167358, 752.922668, 18.700487, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 1282.167358, 738.382629, 18.700487, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 1282.167358, 724.032470, 18.700487, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 1284.341430, 699.106689, 18.700487, 0.000029, 0.000014, 89.999877, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 1300.541870, 699.106689, 18.700487, 0.000029, 0.000014, 89.999877, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 1317.891967, 699.106689, 18.700487, 0.000029, 0.000014, 89.999877, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, 1271.938964, 681.535522, 14.918743, 0.000000, 0.000000, -89.199996, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, 1267.864501, 683.258850, 14.918743, 0.000000, 0.000000, 90.800003, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1746, 1269.291992, 707.848999, 9.841533, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 1336.729980, 756.130065, 11.362763, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 1336.729980, 766.530334, 11.362763, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 1336.729980, 779.460876, 11.362763, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 1326.344604, 779.455688, 11.362763, 0.000014, 0.000007, 89.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 1315.944335, 779.455688, 11.362763, 0.000014, 0.000007, 89.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 1303.013793, 779.455688, 11.362763, 0.000014, 0.000007, 89.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 1336.750122, 733.800720, 11.362763, 0.000022, 0.000000, 179.999832, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 1336.750122, 723.400451, 11.362763, 0.000022, 0.000000, 179.999832, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 1336.750122, 710.469909, 11.362763, 0.000022, 0.000000, 179.999832, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1294, 1292.473144, 742.659301, 17.742139, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1294, 1292.473144, 726.999084, 17.742139, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1294, 1292.473144, 757.828857, 17.742139, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1294, 1303.523315, 710.719177, 17.742139, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1294, 1319.093750, 710.719177, 17.742139, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1294, 1292.473144, 773.369201, 17.742139, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2008, 1265.942504, 706.875000, 14.325196, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(8614, 1270.281372, 686.801391, 13.113261, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19999, 1265.524780, 708.530700, 14.306842, 0.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2002, 1262.793823, 696.034057, 14.317478, 0.000000, 0.000000, 450.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2002, 1262.833862, 703.834106, 14.317478, 0.000000, 0.000000, 450.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2257, 1268.413085, 709.067565, 17.028287, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2011, 1270.828247, 699.041564, 14.320581, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2011, 1270.828247, 708.121459, 14.320581, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19786, 1262.263305, 692.440551, 17.650770, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19786, 1264.713623, 697.670837, 17.070756, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(931, 1283.496337, 779.029418, 10.899426, 0.000007, -0.000007, 179.999908, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(925, 1279.804199, 779.028686, 10.916460, 0.000007, -0.000007, 179.999908, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(931, 1279.784179, 779.028686, 13.059433, -0.000022, 0.000000, 0.000029, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(925, 1283.476318, 779.029418, 13.076469, -0.000022, 0.000000, 0.000029, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(16101, 1334.923706, 779.516662, 10.726812, -0.000013, -0.000020, -135.000167, 0, 0, -1, 200.00, 200.00);
}