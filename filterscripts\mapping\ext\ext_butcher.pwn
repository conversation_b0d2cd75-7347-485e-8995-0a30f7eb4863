//Butcher v1

/*RemoveChickenCorpBuilding(playerid)
{
    RemoveBuildingForPlayer(playerid, 18240, -2097.449, -2430.199, 29.617, 0.250);
    RemoveBuildingForPlayer(playerid, 18528, -2097.449, -2430.199, 29.617, 0.250);
    RemoveBuildingForPlayer(playerid, 18238, -2077.229, -2444.939, 29.617, 0.250);
    RemoveBuildingForPlayer(playerid, 18529, -2077.229, -2444.939, 29.617, 0.250);
    RemoveBuildingForPlayer(playerid, 18204, -2088.649, -2454.300, 41.125, 0.250);
    RemoveBuildingForPlayer(playerid, 1332, -2087.989, -2440.949, 30.695, 0.250);
    RemoveBuildingForPlayer(playerid, 1338, -2081.840, -2428.229, 30.335, 0.250);
    RemoveBuildingForPlayer(playerid, 18257, -2084.419, -2423.719, 29.617, 0.250);
    RemoveBuildingForPlayer(playerid, 1332, -2085.320, -2414.510, 30.640, 0.250);
    RemoveBuildingForPlayer(playerid, 1338, -2091.860, -2415.709, 30.335, 0.250);
    RemoveBuildingForPlayer(playerid, 1338, -2098.100, -2412.419, 30.335, 0.250);
    RemoveBuildingForPlayer(playerid, 1334, -2107.209, -2423.889, 30.796, 0.250);
}

CreateExtPabrikAyam()
{
    //mappingan pabrik ayam
    static payam;
    payam = CreateDynamicObject(18981, -2100.640136, -2417.465332, 23.347898, 0.000004, -0.000005, 141.400054, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 6340, "rodeo05_law2", "citywall6", 0x00000000);
    payam = CreateDynamicObject(18981, -2061.793212, -2448.181396, 24.227914, 0.000004, -0.000006, 142.700042, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 6340, "rodeo05_law2", "citywall6", 0x00000000);
    payam = CreateDynamicObject(18981, -2090.486572, -2425.568359, 29.137928, 0.000000, 90.000000, 141.400070, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 8391, "ballys01", "greyground256128", 0x00000000);
    payam = CreateDynamicObject(18765, -2097.340820, -2421.710693, 31.627643, 0.000000, 0.000000, -38.700057, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 9227, "sfn_caravansfn", "trail_wall4", 0x00000000);
    payam = CreateDynamicObject(18765, -2091.810546, -2414.804443, 31.627643, 0.000000, 0.000000, -38.700057, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 9227, "sfn_caravansfn", "trail_wall4", 0x00000000);
    payam = CreateDynamicObject(18763, -2098.421875, -2433.281494, 32.084701, 0.000000, 0.000000, -38.199989, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 9227, "sfn_caravansfn", "trail_wall4", 0x00000000);
    payam = CreateDynamicObject(18763, -2096.070800, -2435.132324, 32.084701, 0.000000, 0.000000, -38.199989, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 9227, "sfn_caravansfn", "trail_wall4", 0x00000000);
    payam = CreateDynamicObject(18763, -2091.858886, -2434.631835, 32.084701, 0.000000, 0.000000, -38.199989, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 9227, "sfn_caravansfn", "trail_wall4", 0x00000000);
    payam = CreateDynamicObject(18763, -2088.998779, -2433.070556, 32.084701, 0.000000, 0.000000, -38.199989, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 9227, "sfn_caravansfn", "trail_wall4", 0x00000000);
    payam = CreateDynamicObject(18763, -2087.142578, -2430.711669, 32.084701, 0.000000, 0.000000, -38.199989, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 9227, "sfn_caravansfn", "trail_wall4", 0x00000000);
    payam = CreateDynamicObject(18763, -2087.107421, -2428.597167, 32.084701, 0.000000, 0.000000, -38.199989, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 6332, "rodeo01_law2", "rodwall04_LAw2", 0x00000000);
    payam = CreateDynamicObject(18763, -2082.292480, -2424.517578, 32.084701, 0.000000, 0.000000, -38.199989, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 6332, "rodeo01_law2", "rodwall04_LAw2", 0x00000000);
    payam = CreateDynamicObject(18763, -2079.150878, -2423.209472, 32.084701, 0.000000, 0.000000, -38.199989, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 9227, "sfn_caravansfn", "trail_wall4", 0x00000000);
    payam = CreateDynamicObject(18763, -2077.807617, -2421.501953, 32.084701, 0.000000, 0.000000, -38.199989, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 9227, "sfn_caravansfn", "trail_wall4", 0x00000000);
    payam = CreateDynamicObject(18763, -2071.951904, -2434.053222, 32.084701, 0.000000, 0.000000, -38.199989, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 16640, "a51", "vgs_shopwall01_128", 0x00000000);
    payam = CreateDynamicObject(18763, -2079.933593, -2426.373779, 32.084701, 0.000000, 0.000000, -38.199989, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 6332, "rodeo01_law2", "rodwall04_LAw2", 0x00000000);
    payam = CreateDynamicObject(18763, -2084.757812, -2430.446289, 32.084701, 0.000000, 0.000000, -38.199989, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 6332, "rodeo01_law2", "rodwall04_LAw2", 0x00000000);
    payam = CreateDynamicObject(18763, -2083.713623, -2431.267822, 32.084701, 0.000000, 0.000000, -38.199989, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 6332, "rodeo01_law2", "rodwall04_LAw2", 0x00000000);
    payam = CreateDynamicObject(18763, -2078.078125, -2424.015625, 32.084701, 0.000000, 0.000000, -38.199989, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 6332, "rodeo01_law2", "rodwall04_LAw2", 0x00000000);
    payam = CreateDynamicObject(18763, -2076.756347, -2422.334716, 32.084701, 0.000000, 0.000000, -38.199989, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 6332, "rodeo01_law2", "rodwall04_LAw2", 0x00000000);
    payam = CreateDynamicObject(18981, -2070.948974, -2441.161865, 29.147928, 0.000000, 90.000000, 141.400070, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 8391, "ballys01", "greyground256128", 0x00000000);
    payam = CreateDynamicObject(18981, -2083.636718, -2415.407226, 23.347898, -0.000005, -0.000004, -128.599929, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 6340, "rodeo05_law2", "citywall6", 0x00000000);
    payam = CreateDynamicObject(18763, -2068.931640, -2428.561279, 32.084701, 0.000000, 0.000000, -38.199989, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 16640, "a51", "vgs_shopwall01_128", 0x00000000);
    payam = CreateDynamicObject(18763, -2063.402099, -2432.966796, 32.084701, 0.000000, 0.000000, -38.199989, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 16640, "a51", "vgs_shopwall01_128", 0x00000000);
    payam = CreateDynamicObject(18766, -2061.178955, -2439.816406, 32.095855, 0.000000, 0.000000, -38.499973, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 16640, "a51", "vgs_shopwall01_128", 0x00000000);
    payam = CreateDynamicObject(18763, -2065.250732, -2435.318847, 32.084701, 0.000000, 0.000000, -38.199989, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 16640, "a51", "vgs_shopwall01_128", 0x00000000);
    payam = CreateDynamicObject(18766, -2075.705322, -2435.547851, 32.095855, 0.000004, 0.000003, 51.500026, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 16640, "a51", "vgs_shopwall01_128", 0x00000000);
    payam = CreateDynamicObject(18766, -2081.257324, -2442.528564, 32.095855, 0.000004, 0.000003, 51.500026, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 16640, "a51", "vgs_shopwall01_128", 0x00000000);
    payam = CreateDynamicObject(18766, -2086.951171, -2436.957031, 32.095855, 0.000004, 0.000003, 51.500026, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 6332, "rodeo01_law2", "rodwall04_LAw2", 0x00000000);
    payam = CreateDynamicObject(18981, -2078.639160, -2450.465576, 24.237911, -0.000005, -0.000004, -128.599929, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 6340, "rodeo05_law2", "citywall6", 0x00000000);
    payam = CreateDynamicObject(18981, -2092.304443, -2439.531005, 23.347898, -0.000005, -0.000004, -128.599929, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 6340, "rodeo05_law2", "citywall6", 0x00000000);
    payam = CreateDynamicObject(18981, -2064.451416, -2430.722656, 23.347898, -0.000005, -0.000004, -128.599929, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 6340, "rodeo05_law2", "citywall6", 0x00000000);
    payam = CreateDynamicObject(3799, -2091.781494, -2425.063232, 29.478845, 0.000000, 0.000000, -37.699985, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 14415, "carter_block_2", "mp_carter_cage", 0x00000000);
    payam = CreateDynamicObject(3799, -2103.573486, -2424.280029, 29.478845, 0.000000, 0.000000, -37.699985, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 14415, "carter_block_2", "mp_carter_cage", 0x00000000);
    payam = CreateDynamicObject(3799, -2090.255859, -2421.995361, 30.868864, 0.000000, 0.000000, -37.699985, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 14415, "carter_block_2", "mp_carter_cage", 0x00000000);
    payam = CreateDynamicObject(3799, -2090.255859, -2421.995361, 28.648849, 0.000000, 0.000000, -37.699985, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 14415, "carter_block_2", "mp_carter_cage", 0x00000000);
    payam = CreateDynamicObject(3799, -2088.464843, -2419.676757, 30.868864, 0.000000, 0.000000, -37.699985, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 14415, "carter_block_2", "mp_carter_cage", 0x00000000);
    payam = CreateDynamicObject(3799, -2088.465332, -2419.676757, 28.648849, 0.000000, 0.000000, -37.699985, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 14415, "carter_block_2", "mp_carter_cage", 0x00000000);
    payam = CreateDynamicObject(3799, -2086.673583, -2417.358642, 30.868864, 0.000000, 0.000000, -37.699985, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 14415, "carter_block_2", "mp_carter_cage", 0x00000000);
    payam = CreateDynamicObject(3799, -2086.674316, -2417.358154, 28.648849, 0.000000, 0.000000, -37.699985, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 14415, "carter_block_2", "mp_carter_cage", 0x00000000);
    payam = CreateDynamicObject(3799, -2085.218750, -2415.473632, 30.868864, 0.000000, 0.000000, -37.699985, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 14415, "carter_block_2", "mp_carter_cage", 0x00000000);
    payam = CreateDynamicObject(3799, -2082.930175, -2417.290527, 30.868864, 0.000000, 0.000000, -37.699985, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 14415, "carter_block_2", "mp_carter_cage", 0x00000000);
    payam = CreateDynamicObject(3799, -2080.629882, -2419.091552, 30.868864, 0.000000, 0.000000, -37.699985, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 14415, "carter_block_2", "mp_carter_cage", 0x00000000);
    payam = CreateDynamicObject(3799, -2079.540039, -2421.249023, 30.868864, 0.000000, 0.000000, -37.699985, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 14415, "carter_block_2", "mp_carter_cage", 0x00000000);
    payam = CreateDynamicObject(3799, -2081.207763, -2423.408935, 30.868864, 0.000000, 0.000000, -37.699985, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 14415, "carter_block_2", "mp_carter_cage", 0x00000000);
    payam = CreateDynamicObject(3799, -2082.905761, -2417.259277, 28.628875, 0.000000, 0.000000, -37.699985, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 14415, "carter_block_2", "mp_carter_cage", 0x00000000);
    payam = CreateDynamicObject(3799, -2080.586914, -2419.051269, 28.628875, 0.000000, 0.000000, -37.699985, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 14415, "carter_block_2", "mp_carter_cage", 0x00000000);
    payam = CreateDynamicObject(3799, -2080.624511, -2422.697509, 28.628875, 0.000000, 0.000000, 322.300018, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 14415, "carter_block_2", "mp_carter_cage", 0x00000000);
    payam = CreateDynamicObject(18092, -2078.034179, -2431.885009, 30.209274, 0.000000, 0.000000, 51.700019, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    SetDynamicObjectMaterial(payam, 1, 16640, "a51", "scratchedmetal", 0x00000000);
    payam = CreateDynamicObject(18766, -2084.068115, -2438.790283, 32.088722, 0.000000, 0.000000, -38.600021, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 6332, "rodeo01_law2", "rodwall04_LAw2", 0x00000000);
    payam = CreateDynamicObject(18763, -2083.944091, -2436.351806, 32.084701, 0.000000, 0.000000, -38.199989, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 6332, "rodeo01_law2", "rodwall04_LAw2", 0x00000000);
    payam = CreateDynamicObject(19433, -2080.925292, -2438.527587, 30.684391, 0.000000, 90.000000, 51.399993, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 1560, "7_11_door", "cj_sheetmetal2", 0x00000000);
    payam = CreateDynamicObject(19433, -2078.781250, -2435.836914, 30.684391, 0.000000, 90.000000, 51.399993, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 1560, "7_11_door", "cj_sheetmetal2", 0x00000000);
    payam = CreateDynamicObject(19433, -2078.105224, -2434.985107, 30.694391, 0.000000, 90.000000, 51.399993, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 1560, "7_11_door", "cj_sheetmetal2", 0x00000000);
    payam = CreateDynamicObject(18766, -2067.037109, -2446.104248, 32.095855, 0.000000, 0.000000, -38.499973, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 16640, "a51", "vgs_shopwall01_128", 0x00000000);
    payam = CreateDynamicObject(18766, -2073.226562, -2441.184082, 32.095855, 0.000000, 0.000000, -38.499973, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 16640, "a51", "vgs_shopwall01_128", 0x00000000);
    payam = CreateDynamicObject(19433, -2075.525878, -2435.837890, 30.684391, 0.000000, 90.000000, 141.399993, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 1560, "7_11_door", "cj_sheetmetal2", 0x00000000);
    payam = CreateDynamicObject(19433, -2072.798583, -2438.014892, 30.684391, 0.000000, 90.000000, 141.399993, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 1560, "7_11_door", "cj_sheetmetal2", 0x00000000);
    payam = CreateDynamicObject(19433, -2070.056396, -2440.204101, 30.684391, 0.000000, 90.000000, 141.399993, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 1560, "7_11_door", "cj_sheetmetal2", 0x00000000);
    payam = CreateDynamicObject(19433, -2067.320800, -2442.386718, 30.684391, 0.000000, 90.000000, 141.399993, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 1560, "7_11_door", "cj_sheetmetal2", 0x00000000);
    payam = CreateDynamicObject(19433, -2064.593994, -2444.561279, 30.684391, 0.000000, 90.000000, 141.399993, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 1560, "7_11_door", "cj_sheetmetal2", 0x00000000);
    payam = CreateDynamicObject(19433, -2062.195312, -2446.477294, 31.735395, 0.000000, 127.599922, 141.399993, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 1560, "7_11_door", "cj_sheetmetal2", 0x00000000);
    payam = CreateDynamicObject(2267, -2076.182128, -2435.291748, 31.169118, 0.000000, 0.000000, 231.500000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 1, 3032, "bdwinx", "ct_canopy", 0x00000000);
    payam = CreateDynamicObject(2267, -2075.363281, -2435.980712, 31.169118, 0.000000, 0.000000, 51.500003, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 1, 3032, "bdwinx", "ct_canopy", 0x00000000);
    payam = CreateDynamicObject(18981, -2090.486572, -2425.568359, 34.557998, 0.000000, 90.000000, 141.400070, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 3063, "col_wall1x", "mp_diner_ceilingdirt", 0x00000000);
    payam = CreateDynamicObject(18981, -2071.551269, -2440.677978, 34.567996, 0.000000, 90.000000, 141.400070, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 3063, "col_wall1x", "mp_diner_ceilingdirt", 0x00000000);
    payam = CreateDynamicObject(18981, -2090.507324, -2425.591796, 35.338031, 0.000000, 90.000000, 141.400070, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 6340, "rodeo05_law2", "citywall6", 0x00000000);
    payam = CreateDynamicObject(18981, -2071.880371, -2440.424560, 35.338031, 0.000000, 90.000000, 141.400070, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 6340, "rodeo05_law2", "citywall6", 0x00000000);
    payam = CreateDynamicObject(19449, -2104.772705, -2429.722167, 34.068473, 0.000000, 0.000000, 51.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(payam, 0, 9625, "garage_sfw", "garage1b_sfw", 0x00000000);
    payam = CreateDynamicObject(19479, -2098.707275, -2435.065673, 34.392158, 0.000000, 0.000000, -128.599990, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(payam, 0, "PT. Unggas ATHERLIFE", 120, "Times New Roman", 30, 1, 0xFFFFFFFF, 0x00000000, 1);
    payam = CreateDynamicObject(19479, -2057.406738, -2435.689453, 35.072074, -0.000004, 0.000005, 51.399993, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(payam, 0, "PT. Unggas ATHERLIFE", 120, "Times New Roman", 30, 1, 0xFFFFFFFF, 0x00000000, 1);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(18763, -2093.713867, -2436.988525, 32.084701, 0.000000, 0.000000, -38.199989, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1808, -2089.125488, -2429.142089, 29.594091, 0.000000, 0.000000, -39.399993, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19397, -2085.308593, -2426.089599, 31.365362, 0.000000, 0.000000, -37.799999, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11729, -2090.679199, -2432.388183, 29.602762, 0.000000, 0.000000, -127.599937, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11729, -2090.276367, -2431.864257, 29.602762, 0.000000, 0.000000, -127.599937, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19397, -2071.934082, -2430.115234, 31.365362, -0.000003, 0.000006, -37.799999, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11729, -2089.886962, -2431.358642, 29.602762, -0.000006, -0.000003, -127.599914, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11729, -2089.478027, -2430.827636, 29.602762, -0.000006, -0.000003, -127.599914, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11729, -2089.075195, -2430.303710, 29.602762, -0.000006, -0.000003, -127.599914, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2654, -2090.105224, -2431.605957, 31.857845, 0.000000, 0.000000, -36.099979, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2685, -2089.237060, -2430.913574, 31.978218, 0.000000, 0.000000, -128.199859, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1498, -2095.478515, -2427.361083, 29.598640, 0.000000, 0.000000, 51.399971, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1362, -2093.837158, -2426.012207, 30.225170, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3798, -2101.584228, -2426.022949, 29.631259, 0.000000, 0.000000, -38.499973, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3260, -2099.083251, -2428.155029, 29.670446, 270.000000, 360.000000, 54.899990, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3015, -2100.033447, -2426.660888, 29.824195, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1217, -2099.222167, -2427.740722, 30.098232, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1217, -2100.233154, -2427.740722, 30.098232, 0.000000, 0.000000, 96.400009, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1217, -2098.772216, -2428.661621, 30.098232, 0.000000, 0.000000, -60.399993, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2612, -2098.756835, -2427.039062, 31.589899, 0.000000, 0.000000, -38.599956, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1498, -2092.549072, -2433.117187, 29.598640, 0.000000, 0.000000, 231.399963, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19903, -2099.527099, -2431.771240, 29.625343, 0.000000, 0.000000, 140.099838, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1462, -2095.943115, -2428.736816, 29.625541, 0.000000, 0.000000, 53.000011, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19468, -2093.422851, -2426.588867, 29.715030, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19468, -2092.962402, -2426.398681, 29.715030, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1426, -2089.872558, -2422.071533, 29.795513, 0.000000, 0.000000, 51.800033, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1426, -2088.325927, -2420.107910, 29.805513, 0.000000, 0.000000, 51.800033, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1426, -2086.266113, -2417.489746, 29.805513, 0.000000, 0.000000, 51.800033, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1426, -2084.082275, -2415.836181, 29.805513, 0.000000, 0.000000, 141.800033, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1426, -2081.497558, -2417.871093, 29.805513, 0.000000, 0.000000, 141.800033, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1426, -2078.947998, -2419.953369, 29.805513, 0.000000, 0.000000, 141.800033, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1426, -2080.520263, -2421.950927, 29.805513, 0.000000, 0.000000, 231.800033, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1438, -2090.111816, -2422.041259, 33.219688, 0.000000, 0.000000, 48.800003, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19587, -2091.590332, -2425.788085, 31.842037, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1685, -2104.202392, -2424.396240, 30.383417, 0.000000, 0.000000, -38.900001, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1685, -2092.047851, -2424.732910, 30.383417, 0.000000, 0.000000, -38.900001, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1224, -2084.262939, -2419.537109, 30.220649, 0.000000, 0.000000, -74.599967, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1271, -2085.824951, -2419.798828, 30.003904, 0.000000, 0.000000, 52.299995, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1271, -2086.265380, -2420.369628, 30.003904, 0.000000, 0.000000, 51.400005, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19587, -2086.061523, -2420.134033, 30.352050, 0.000000, 0.000000, 53.599948, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1230, -2084.787841, -2416.708984, 33.629657, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1230, -2080.759277, -2419.800048, 33.629657, 0.000000, 0.000000, -60.799991, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2804, -2076.940185, -2430.237060, 30.730468, 0.000000, 0.000000, -89.400001, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2804, -2078.274169, -2431.751464, 30.730468, 0.000000, 0.000000, -176.499984, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(335, -2078.520019, -2431.901367, 30.719848, 90.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(335, -2077.919433, -2431.130615, 31.059846, 360.000000, 180.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2804, -2077.878662, -2431.224609, 30.730468, 0.000000, 0.000000, 45.400028, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(16101, -2075.648681, -2431.013183, 32.512550, 0.000000, 90.000000, -128.199935, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(16101, -2070.907226, -2424.986083, 32.512550, 0.000000, 90.000000, -128.199935, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11706, -2076.407714, -2429.767333, 29.632946, 0.000000, 0.000000, 141.799942, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1438, -2071.774169, -2427.063964, 29.726358, 0.000000, 0.000000, -40.199981, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1450, -2096.351806, -2432.213134, 30.211214, 0.000000, 0.000000, 141.099990, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1498, -2077.956298, -2426.252197, 29.598640, 0.000000, 0.000000, 51.799964, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1498, -2083.717285, -2434.673339, 29.598640, 0.000000, 0.000000, 321.799957, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19903, -2079.921630, -2435.431396, 29.635343, 0.000000, 0.000000, 140.099838, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2805, -2077.835205, -2433.805175, 31.730413, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2805, -2079.544921, -2435.992675, 31.820415, 0.000000, 0.000000, -37.899993, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2806, -2080.639648, -2438.158203, 30.766237, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2804, -2076.465332, -2435.068115, 30.793128, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2804, -2078.276367, -2435.577636, 30.793128, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3383, -2065.827636, -2445.781738, 29.594945, 0.000000, 0.000000, 141.299911, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2917, -2081.695312, -2439.112304, 33.788520, 0.000000, 0.000000, -38.599998, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1358, -2074.334472, -2424.997070, 30.824506, 0.000000, 0.000000, -36.200019, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3384, -2081.349853, -2431.870849, 31.050716, 0.000000, 0.000000, 141.700012, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19587, -2070.913085, -2439.524169, 30.777366, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19587, -2067.432861, -2442.254394, 30.777366, 0.000000, 0.000000, -38.299983, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(941, -2068.260253, -2439.866210, 30.116365, 0.000000, 0.000000, -38.999996, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2804, -2071.095703, -2439.477294, 30.783130, 0.000000, 0.000000, 143.399978, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2804, -2067.460937, -2442.357666, 30.783130, 0.000000, 0.000000, -95.400047, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2947, -2083.651123, -2429.381835, 29.621164, 0.000000, 0.000000, 52.199989, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2947, -2083.740966, -2429.513427, 29.621164, 0.000000, 0.000000, 232.199981, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2687, -2085.140136, -2428.202392, 31.021045, 0.000000, 0.000000, 141.699768, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(941, -2062.668945, -2446.134033, 30.116365, 0.000000, 0.000000, -38.999996, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1221, -2066.586425, -2441.188476, 30.075975, 0.000000, 0.000000, 53.099994, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1221, -2060.009521, -2443.923339, 30.085975, 0.000000, 0.000000, 8.099997, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1221, -2060.666992, -2444.996826, 30.085975, 0.000000, 0.000000, -40.399971, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2675, -2089.546630, -2426.826904, 29.706735, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2670, -2084.391357, -2421.665039, 29.717929, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2671, -2086.803710, -2422.665771, 29.647928, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2676, -2075.988037, -2426.748291, 29.747930, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2673, -2061.709228, -2443.581054, 29.727935, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2912, -2064.752441, -2431.634765, 29.627927, 0.000000, 0.000000, -38.600025, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2912, -2065.299316, -2431.199218, 29.627927, 0.000000, 0.000000, -38.600025, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2912, -2064.932128, -2431.494628, 30.317935, 0.000000, 0.000000, -38.600025, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1271, -2067.256347, -2430.100830, 29.977933, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1271, -2066.150878, -2430.604003, 29.977926, 0.000000, 0.000000, -37.299957, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1271, -2066.883056, -2430.458984, 30.657922, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2961, -2063.142333, -2438.902587, 30.937931, 0.000000, 0.000000, 141.199890, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11713, -2063.552490, -2438.608154, 30.697933, 0.000000, 0.000000, -128.400085, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11713, -2069.336181, -2430.192626, 30.597930, 0.000000, 0.000000, -128.199951, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11713, -2082.247802, -2426.515380, 30.747930, 0.000000, 0.000000, -128.500045, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2690, -2082.289794, -2426.557617, 30.807935, 0.000000, 0.000000, -32.599998, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1338, -2097.642333, -2428.973632, 30.315000, 0.000000, 0.000000, 45.700004, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1338, -2092.521728, -2427.375244, 30.317922, 0.000000, 0.000000, -33.800022, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1338, -2062.774658, -2440.144775, 30.327922, 0.000000, 0.000000, 134.099929, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11709, -2074.503662, -2432.541015, 30.327930, 0.000000, 0.000000, -128.899917, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2685, -2074.206787, -2432.816894, 31.218208, 0.000000, 0.000000, -128.199859, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(941, -2066.435302, -2433.468017, 30.116365, 0.000000, 0.000000, 51.000003, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(919, -2087.889648, -2419.585205, 36.266101, 0.000000, 0.000000, -37.499980, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(919, -2095.700439, -2429.762207, 36.266101, 0.000000, 0.000000, -37.499980, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(919, -2067.728515, -2436.085449, 36.266101, -0.000004, 0.000006, -37.499980, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(919, -2075.539306, -2446.262451, 36.266101, -0.000004, 0.000006, -37.499980, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2653, -2065.335205, -2445.504394, 33.977382, 180.000000, 0.000000, 51.099987, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2653, -2071.474853, -2440.549804, 33.977382, 180.000000, 0.000000, 51.099987, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2653, -2077.615722, -2435.594970, 33.977382, 180.000000, 0.000000, 51.099987, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2653, -2083.754882, -2430.641113, 33.977382, 180.000000, 0.000000, 51.099987, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19397, -2080.718017, -2429.651367, 31.365362, 0.000000, 0.000000, -37.799999, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3812, -2061.361572, -2447.088623, 32.682666, 0.000000, 0.000000, 142.100128, 0, 0, -1, 200.00, 200.00); 
}
*/

//butcher v2
/*
RemoveChickenCorpBuilding(playerid)
{
    //kandang ayam
    RemoveBuildingForPlayer(playerid, 13002, 1554.050, -0.312, 26.812, 0.250);
    RemoveBuildingForPlayer(playerid, 13003, 1559.630, 20.015, 23.546, 0.250);
    RemoveBuildingForPlayer(playerid, 935, 1566.739, 16.367, 23.671, 0.250);
    RemoveBuildingForPlayer(playerid, 935, 1550.560, 11.109, 23.632, 0.250);

    //kantor ayam
    RemoveBuildingForPlayer(playerid, 3375, 1925.130, 160.093, 40.671, 0.250);
    RemoveBuildingForPlayer(playerid, 3376, 1925.130, 160.093, 40.671, 0.250);
}

CreateExtPabrikAyam()
{
    //kandang ayam
    btchrstx = CreateDynamicObject(19353, 1537.975219, 7.299127, 24.566040, 0.000007, -0.000002, 107.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1541.028198, 8.291072, 24.566040, 0.000007, -0.000002, 107.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(12922, 1553.674804, 40.612930, 26.481822, 0.000000, 0.000000, -166.399917, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(btchrstx, 2, "KANDANG AYAM ATHERLIFE", 110, "Arial", 40, 1, 0xFF000000, 0xFFFF91A4, 1);
    btchrstx = CreateDynamicObject(19353, 1544.081176, 9.283017, 24.566040, 0.000007, -0.000002, 107.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1547.076660, 10.259128, 24.566040, 0.000014, -0.000004, 107.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1550.129638, 11.251073, 24.566040, 0.000014, -0.000004, 107.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1553.182617, 12.243018, 24.566040, 0.000014, -0.000004, 107.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1556.185913, 13.209144, 24.566040, 0.000014, -0.000004, 107.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1559.238891, 14.201090, 24.566040, 0.000014, -0.000004, 107.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1562.291870, 15.193035, 24.566040, 0.000014, -0.000004, 107.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1565.287353, 16.169147, 24.566040, 0.000021, -0.000007, 107.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1568.340332, 17.161090, 24.566040, 0.000021, -0.000007, 107.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1571.393310, 18.153036, 24.566040, 0.000021, -0.000007, 107.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1535.919433, 8.219160, 24.566040, 0.000007, -0.000002, 197.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1536.804565, 10.299139, 24.566040, 0.000014, -0.000004, 107.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1539.857543, 11.291084, 24.566040, 0.000014, -0.000004, 107.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1542.910522, 12.283028, 24.566040, 0.000014, -0.000004, 107.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1545.906005, 13.259140, 24.566040, 0.000021, -0.000007, 107.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1548.958984, 14.251085, 24.566040, 0.000021, -0.000007, 107.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1552.011962, 15.243029, 24.566040, 0.000021, -0.000007, 107.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1555.015258, 16.209156, 24.566040, 0.000021, -0.000007, 107.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1558.068237, 17.201103, 24.566040, 0.000021, -0.000007, 107.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1561.121215, 18.193046, 24.566040, 0.000021, -0.000007, 107.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1564.116699, 19.169158, 24.566040, 0.000029, -0.000009, 107.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1567.169677, 20.161102, 24.566040, 0.000029, -0.000009, 107.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1570.222656, 21.153047, 24.566040, 0.000029, -0.000009, 107.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1569.703369, 22.750820, 24.566040, 0.000029, -0.000009, 197.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1568.708740, 25.813215, 24.566040, 0.000029, -0.000009, 197.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1567.720336, 28.856586, 24.566040, 0.000029, -0.000009, 197.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1566.728515, 31.909484, 24.566040, 0.000029, -0.000009, 197.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1565.733398, 34.971900, 24.566040, 0.000029, -0.000009, 197.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1566.653198, 37.058395, 24.566040, 0.000029, -0.000009, 107.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1568.615600, 35.908199, 24.566040, 0.000029, -0.000009, 197.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1569.607299, 32.855289, 24.566040, 0.000029, -0.000009, 197.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1570.596923, 29.811901, 24.566040, 0.000029, -0.000009, 197.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1571.589233, 26.759004, 24.566040, 0.000029, -0.000009, 197.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1572.581665, 23.706106, 24.566041, 0.000029, -0.000009, 197.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1573.573486, 20.653215, 24.566041, 0.000029, -0.000009, 197.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1572.655761, 18.573528, 24.566040, 0.000021, -0.000007, 107.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1568.204711, 35.774906, 28.385887, 0.000026, 44.999984, -162.000045, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1565.972534, 35.049430, 28.371749, 0.000031, 45.000000, 17.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1565.743408, 34.971900, 25.996047, 0.000026, -0.000016, -162.000045, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1566.653198, 37.068393, 25.996047, 0.000036, -0.000011, 107.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1568.605590, 35.908199, 25.996047, 0.000026, -0.000016, -162.000045, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1569.174682, 32.724922, 28.385887, 0.000021, 44.999969, -161.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1566.942504, 31.999444, 28.371749, 0.000036, 45.000015, 17.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1566.713378, 31.921915, 25.996047, 0.000021, -0.000031, -161.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1570.155639, 29.674940, 28.385887, 0.000019, 44.999961, -161.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1569.575561, 32.858215, 25.996047, 0.000021, -0.000031, -161.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1567.923461, 28.949462, 28.371749, 0.000038, 45.000022, 17.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1567.694335, 28.871932, 25.996047, 0.000019, -0.000038, -161.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1570.556518, 29.808233, 25.996047, 0.000019, -0.000038, -161.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1571.145751, 26.684947, 28.385887, 0.000017, 44.999954, -161.999862, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1568.913574, 25.959470, 28.371749, 0.000040, 45.000030, 17.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1568.684448, 25.881940, 25.996047, 0.000017, -0.000045, -161.999862, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1571.546630, 26.818241, 25.996047, 0.000017, -0.000045, -161.999862, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1572.134765, 23.634935, 28.385887, 0.000014, 44.999946, -161.999816, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1569.902587, 22.909458, 28.371749, 0.000043, 45.000038, 17.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1569.673461, 22.831928, 25.996047, 0.000014, -0.000052, -161.999816, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1572.535644, 23.768228, 25.996047, 0.000014, -0.000052, -161.999816, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1573.115356, 20.584941, 28.385887, 0.000012, 44.999938, -161.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1570.447021, 21.200460, 28.371749, 0.000045, 45.000045, 17.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1567.956665, 17.040872, 25.996047, 0.000002, -0.000065, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1573.546997, 20.654756, 25.996047, 0.000012, -0.000060, -161.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1570.200683, 20.576396, 28.385887, 0.000002, 44.999935, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1570.783203, 18.297876, 28.371749, 0.000055, 45.000049, 107.999855, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1571.009521, 18.032825, 25.996047, 0.000002, -0.000065, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1570.014404, 21.075712, 25.996047, 0.000002, -0.000065, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1572.626831, 18.589767, 26.356046, 0.000002, -0.000065, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1567.862792, 17.349210, 28.371753, 0.000055, 45.000049, 107.999855, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1572.506835, 18.850461, 28.378820, 0.000055, 45.000049, 107.999855, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1566.962158, 20.083749, 25.996047, 0.000002, -0.000065, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1563.909667, 19.091777, 25.996047, 0.000002, -0.000065, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1560.856933, 18.099817, 25.996047, 0.000002, -0.000065, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1557.804565, 17.107858, 25.996047, 0.000002, -0.000065, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1554.752075, 16.115896, 25.996047, 0.000002, -0.000065, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1551.708740, 15.127028, 25.996047, 0.000002, -0.000065, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1548.656005, 14.135070, 25.996047, 0.000002, -0.000065, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1545.613159, 13.146203, 25.996047, 0.000002, -0.000065, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1542.560302, 12.154247, 25.996047, 0.000002, -0.000065, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1539.516235, 11.165380, 25.996047, 0.000002, -0.000065, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1536.828735, 10.312964, 25.996047, 0.000002, -0.000065, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1567.148315, 19.584430, 28.385887, 0.000002, 44.999935, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1564.095458, 18.592473, 28.385887, 0.000002, 44.999935, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1566.481689, 36.991394, 27.065397, 45.300052, -0.000011, 107.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1561.042724, 17.600519, 28.385887, 0.000002, 44.999935, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1557.999511, 16.611650, 28.385887, 0.000002, 44.999935, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1554.946533, 15.619693, 28.385887, 0.000002, 44.999935, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1551.893798, 14.627738, 28.385887, 0.000002, 44.999935, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1548.841308, 13.635785, 28.385887, 0.000002, 44.999935, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1545.789062, 12.643833, 28.385887, 0.000002, 44.999935, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1542.736694, 11.651874, 28.385887, 0.000002, 44.999935, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1539.693115, 10.663003, 28.385887, 0.000002, 44.999935, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1536.640869, 9.671046, 28.385887, 0.000002, 44.999935, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1564.904174, 16.048912, 25.996047, 0.000002, -0.000065, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1561.860595, 15.060047, 25.996047, 0.000002, -0.000065, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1558.807128, 14.068089, 25.996047, 0.000002, -0.000065, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1555.754272, 13.076131, 25.996047, 0.000002, -0.000065, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1552.717773, 12.096776, 25.996047, 0.000002, -0.000065, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1549.674926, 11.107909, 25.996047, 0.000002, -0.000065, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1546.622192, 10.115951, 25.996047, 0.000002, -0.000065, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1543.569702, 9.123994, 25.996047, 0.000002, -0.000065, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1540.516845, 8.132033, 25.996047, 0.000002, -0.000065, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1537.849731, 7.213041, 25.996047, 0.000002, -0.000065, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1535.909912, 8.216070, 25.986045, 0.000007, -0.000002, 197.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1564.809692, 16.357271, 28.371753, 0.000055, 45.000049, 107.999855, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1561.756103, 15.365326, 28.371753, 0.000055, 45.000049, 107.999855, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1558.703369, 14.373387, 28.371753, 0.000055, 45.000049, 107.999855, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1555.660034, 13.384542, 28.371755, 0.000055, 45.000049, 107.999855, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1552.607055, 12.392602, 28.371755, 0.000055, 45.000049, 107.999855, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1549.554199, 11.400664, 28.371755, 0.000055, 45.000049, 107.999855, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1546.501586, 10.408727, 28.371755, 0.000055, 45.000049, 107.999855, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1543.448974, 9.416791, 28.371761, 0.000055, 45.000049, 107.999855, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1540.396118, 8.424851, 28.371763, 0.000055, 45.000049, 107.999855, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1537.343261, 7.432912, 28.371763, 0.000055, 45.000049, 107.999855, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1535.958740, 8.098112, 27.273000, 45.000007, -0.000002, 197.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1567.882690, 36.721549, 28.371746, 0.000026, 44.999984, -162.000045, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1565.664184, 36.000473, 28.371749, 0.000031, 45.000000, 17.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1564.344360, 34.635559, 26.954713, 0.000031, 70.400001, 17.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1565.336059, 31.582666, 26.954713, 0.000031, 70.400001, 17.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1566.328491, 28.529767, 26.954713, 0.000031, 70.400001, 17.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1567.320922, 25.476863, 26.954713, 0.000031, 70.400001, 17.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1568.313110, 22.423969, 26.954713, 0.000031, 70.400001, 17.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1561.764160, 20.093425, 26.832109, 0.000031, 70.400001, 287.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1564.826171, 21.088462, 26.832109, 0.000031, 70.400001, 287.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1567.869750, 22.077320, 26.832109, 0.000031, 70.400001, 287.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1558.711425, 19.101488, 26.832109, 0.000031, 70.400001, 287.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1555.658569, 18.109544, 26.832109, 0.000031, 70.400001, 287.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1552.605468, 17.117607, 26.832109, 0.000031, 70.400001, 287.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1549.552978, 16.125661, 26.832109, 0.000031, 70.400001, 287.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1546.500122, 15.133721, 26.832109, 0.000031, 70.400001, 287.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1543.447143, 14.141777, 26.832109, 0.000031, 70.400001, 287.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1540.394165, 13.149837, 26.832107, 0.000031, 70.400001, 287.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1537.350708, 12.160984, 26.842107, 0.000031, 70.400001, 287.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1535.942504, 11.703644, 26.832107, 0.000031, 70.400001, 287.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1564.094482, 35.384803, 26.973554, 0.000031, 70.400001, 17.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(994, 1515.753906, 9.801617, 23.203521, 0.000006, 0.000029, 7.499997, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(994, 1510.779296, 13.618581, 23.203521, -0.000016, 0.000025, -37.499980, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(994, 1510.840087, 13.616508, 23.203521, 0.000029, -0.000006, 97.499908, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(994, 1513.852661, 24.804109, 23.203521, -0.000025, -0.000016, -127.499908, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(994, 1513.842895, 24.802799, 23.203521, 0.000006, 0.000029, 7.499997, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(994, 1520.029418, 25.588851, 23.203521, -0.000016, 0.000025, -37.499980, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(994, 1524.988769, 21.773773, 23.203521, -0.000029, 0.000006, -82.499923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(994, 1521.965087, 10.619387, 23.203521, 0.000025, 0.000016, 52.499980, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1497, 1539.664550, 11.338301, 23.104505, 0.000000, 0.000000, -161.700012, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1497, 1544.522583, 12.892190, 23.104505, 0.000000, 0.000000, -161.700012, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1497, 1550.009887, 14.675446, 23.104505, 0.000000, 0.000000, -161.700012, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1497, 1558.409179, 17.400522, 23.104505, 0.000000, 0.000000, -161.700012, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1497, 1565.685424, 19.764577, 23.104505, 0.000000, 0.000000, -161.700012, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1497, 1568.894653, 24.965305, 23.104505, 0.000000, 0.000000, -71.700012, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1497, 1567.491088, 29.272483, 23.104505, 0.000000, 0.000000, -71.700012, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1497, 1565.939819, 33.994270, 23.104505, 0.000000, 0.000000, -71.700012, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3276, 1583.176147, 48.060085, 25.084934, 0.000000, 0.099999, 15.099996, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3276, 1565.149414, 43.195892, 24.527444, 0.000000, -6.399998, 15.099996, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3276, 1540.897827, 36.652076, 23.981355, 0.000000, 0.399999, 15.099996, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3276, 1519.512939, 30.882057, 23.985994, 0.000000, -0.500000, 15.099996, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3276, 1508.103393, 27.803607, 23.942806, 0.000000, 2.199999, 15.099996, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3276, 1503.918823, 20.164430, 24.184272, 0.000000, -1.800000, 105.099998, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3276, 1507.096679, 8.389190, 24.060823, 0.000000, -0.500000, 105.099998, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3276, 1514.642089, 4.065498, 23.857076, 0.000000, -0.500000, 195.100006, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3276, 1525.986816, 2.375143, 23.550115, 0.000000, 3.199998, 330.100006, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3276, 1545.111206, 4.073156, 23.270299, 0.000000, 1.799999, 195.100006, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3276, 1556.581420, 7.168145, 23.443563, 0.000000, 0.899999, 195.100006, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3276, 1568.421875, 10.363190, 23.208080, 0.000000, -4.299999, 195.100006, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3425, 1518.368286, -2.218461, 33.794979, 0.000000, 0.000000, -11.999993, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(12912, 1549.539062, -30.307855, 31.599973, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14875, 1536.279418, 17.664127, 23.856653, 0.000000, 0.000000, 15.800000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14875, 1538.167968, 18.333774, 23.896654, 0.000000, 0.000000, 195.800003, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, 1546.673095, 14.387599, 23.581342, 0.000000, 0.000000, 20.600002, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, 1540.953979, 18.839576, 23.568382, 0.000000, 0.000000, 17.500000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(17055, 1540.971069, 33.434844, 25.067192, 0.000000, 0.000000, -164.600021, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(935, 1566.369628, 30.444257, 23.695444, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(935, 1566.369628, 31.204261, 23.695444, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(933, 1568.717041, 21.985834, 23.071239, 1.000000, 0.399999, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(925, 1534.922363, 8.005655, 23.953439, 0.000000, 5.199999, -71.700012, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3447, 1538.329956, 13.820089, 18.918203, 0.000000, 180.000000, 17.899997, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3447, 1549.197265, 17.330091, 18.918203, 0.000000, 180.000000, 17.899997, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3447, 1561.216430, 21.211996, 18.918203, 0.000000, 180.000000, 17.899997, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3447, 1564.618408, 29.225408, 18.918203, 0.000000, 180.000000, 107.899993, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, 1561.324951, 36.274059, 26.456228, 0.000000, 0.000000, 20.300001, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, 1521.954833, 33.321456, 27.016218, 0.000000, 0.000000, 290.299987, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, 1505.025146, 28.935462, 26.976213, 0.000000, 0.000000, 290.299987, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, 1564.890869, 44.853694, 27.516216, 0.000000, 0.000000, 290.299987, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, 1582.738159, 49.845394, 28.146211, 0.000000, 0.000000, 290.299987, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, 1539.264892, 37.922302, 26.906188, 0.000000, 0.000000, 290.299987, 0, 0, -1, 200.00, 200.00); 

    CreateDynamicObject(1451, 1535.097778, 33.523986, 23.922494, 0.000000, 0.000000, -78.899971, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1451, 1535.558471, 31.178686, 23.922494, 0.000000, 0.000000, -78.899971, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, 1507.198486, 15.163917, 26.976209, 0.000000, 0.000000, -169.100021, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, 1520.152343, 6.273227, 26.816204, 0.000000, 0.000000, -79.100021, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1449, 1551.885620, 15.519187, 23.642843, 0.000000, 0.000000, -161.400054, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1449, 1568.223022, 26.324935, 23.652843, 0.000000, 0.000000, -71.400054, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1450, 1564.972778, 35.032402, 23.830081, 0.000000, 0.000000, -71.699951, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1368, 1561.347656, 18.798233, 23.830848, 0.000000, 0.000000, -162.199935, 0, 0, -1, 200.00, 200.00);

    //kantor ayam
    btchrstx = CreateDynamicObject(11502, 1941.769165, 153.510147, 39.457389, 0.000014, 0.000003, 72.099975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 16322, "a51_stores", "des_ghotwood1", 0x00000000);
    btchrstx = CreateDynamicObject(12928, 1939.249023, 156.562454, 36.321105, -0.000003, 0.000014, -17.699995, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 16322, "a51_stores", "des_ghotwood1", 0x00000000);
    SetDynamicObjectMaterial(btchrstx, 2, 16322, "a51_stores", "des_ghotwood1", 0x00000000);
    SetDynamicObjectMaterial(btchrstx, 3, 14871, "gf3", "kickhay", 0x00000000);
    btchrstx = CreateDynamicObject(11502, 1935.384033, 155.572525, 39.797393, 0.000014, 0.000003, 72.099975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 16322, "a51_stores", "des_ghotwood1", 0x00000000);
    btchrstx = CreateDynamicObject(3419, 1933.287353, 152.054718, 36.294780, 0.000014, 0.000003, 71.799972, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 16322, "a51_stores", "des_ghotwood1", 0x00000000);
    SetDynamicObjectMaterial(btchrstx, 2, 14871, "gf3", "kickhay", 0x00000000);
    SetDynamicObjectMaterial(btchrstx, 5, 14871, "gf3", "kickhay", 0x00000000);
    btchrstx = CreateDynamicObject(19447, 1942.749389, 151.464584, 36.180221, -0.000001, 90.000007, -17.499998, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14871, "gf3", "kickhay", 0x00000000);
    btchrstx = CreateDynamicObject(19447, 1943.807983, 151.130813, 36.160221, -0.000001, 90.000007, -17.499998, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14871, "gf3", "kickhay", 0x00000000);
    btchrstx = CreateDynamicObject(19377, 1937.571655, 161.039733, 36.261077, 0.000000, 0.000000, 72.400016, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 19480, "signsurf", "sign", 0x00000000);
    btchrstx = CreateDynamicObject(19377, 1943.195068, 159.256027, 36.261077, 0.000000, 0.000000, 72.400016, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 19480, "signsurf", "sign", 0x00000000);
    btchrstx = CreateDynamicObject(19434, 1932.707763, 161.617187, 38.131080, 0.000000, 0.000000, 162.400024, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 19480, "signsurf", "sign", 0x00000000);
    btchrstx = CreateDynamicObject(19434, 1930.999145, 156.231689, 38.131080, 0.000000, 0.000000, 162.400024, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 19480, "signsurf", "sign", 0x00000000);
    btchrstx = CreateDynamicObject(19377, 1946.603271, 154.219833, 36.261077, 0.000000, 0.000000, 162.400024, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 19480, "signsurf", "sign", 0x00000000);
    btchrstx = CreateDynamicObject(19373, 1937.088134, 154.295349, 36.851112, -0.000000, 90.000007, -14.999999, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 3629, "arprtxxref_las", "planetunnel_64HV", 0x00000000);
    btchrstx = CreateDynamicObject(19373, 1934.061767, 155.054626, 36.831111, -0.000000, 90.000007, -14.999999, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 3629, "arprtxxref_las", "planetunnel_64HV", 0x00000000);
    btchrstx = CreateDynamicObject(19604, 1937.045410, 153.417526, 36.992290, 0.000007, 0.000000, 75.499969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2803, "cj_meaty", "CJ_FLESH_2", 0x00000000);
    btchrstx = CreateDynamicObject(19604, 1937.980712, 152.731369, 37.764854, -29.999990, 0.000001, 75.499969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2803, "cj_meaty", "CJ_FLESH_2", 0x00000000);
    btchrstx = CreateDynamicObject(19377, 1937.434936, 155.858932, 31.911113, 0.000007, 180.000000, 74.999992, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 4835, "airoads_las", "tardor2", 0x00000000);
    btchrstx = CreateDynamicObject(19373, 1932.390747, 155.651992, 35.211082, 0.000000, 0.000000, 165.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 4835, "airoads_las", "tardor2", 0x00000000);
    btchrstx = CreateDynamicObject(19373, 1931.038696, 156.014328, 35.211082, 0.000000, 0.000000, 165.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 4835, "airoads_las", "tardor2", 0x00000000);
    btchrstx = CreateDynamicObject(19373, 1933.055786, 157.016052, 35.161079, 0.000000, 0.000000, 255.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 4835, "airoads_las", "tardor2", 0x00000000);
    btchrstx = CreateDynamicObject(19373, 1932.610839, 155.354721, 36.711078, 0.000000, 90.000000, 255.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2803, "cj_meaty", "CJ_FLESH_1", 0x00000000);
    btchrstx = CreateDynamicObject(18766, 1941.429199, 152.402404, 36.321105, 0.000000, 90.000000, 70.600021, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 4835, "airoads_las", "tardor2", 0x00000000);
    btchrstx = CreateDynamicObject(18766, 1942.274658, 152.062423, 36.321105, 0.000000, 90.000000, 70.600021, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 4835, "airoads_las", "tardor2", 0x00000000);
    btchrstx = CreateDynamicObject(19373, 1939.019775, 153.777694, 37.111110, -0.000000, 90.000007, -14.999999, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 3629, "arprtxxref_las", "planetunnel_64HV", 0x00000000);
    btchrstx = CreateDynamicObject(19373, 1940.551391, 153.429275, 37.191108, -0.000000, 90.000007, -14.999999, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 3629, "arprtxxref_las", "planetunnel_64HV", 0x00000000);
    btchrstx = CreateDynamicObject(18766, 1940.125610, 149.935211, 35.481098, 0.000000, 90.000000, 70.600021, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 16322, "a51_stores", "des_ghotwood1", 0x00000000);
    btchrstx = CreateDynamicObject(18762, 1941.144409, 149.576553, 37.891071, 0.000000, 180.000000, 70.600021, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 16322, "a51_stores", "des_ghotwood1", 0x00000000);
    btchrstx = CreateDynamicObject(2382, 1939.762084, 159.347122, 38.249794, 0.000000, 0.000000, 68.900001, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2803, "cj_meaty", "CJ_FLESH_2", 0x00000000);
    SetDynamicObjectMaterial(btchrstx, 1, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(16309, 1945.987426, 157.748886, 34.230648, 0.000000, 5.299999, -17.600002, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 3, 3629, "arprtxxref_las", "planetunnel_64HV", 0x00000000);
    btchrstx = CreateDynamicObject(2382, 1938.157592, 159.966339, 38.249794, 0.000000, 0.000000, 68.900001, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2803, "cj_meaty", "CJ_FLESH_2", 0x00000000);
    SetDynamicObjectMaterial(btchrstx, 1, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(2382, 1935.881835, 160.487106, 38.249794, 0.000007, 0.000001, 68.899986, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2803, "cj_meaty", "CJ_FLESH_2", 0x00000000);
    SetDynamicObjectMaterial(btchrstx, 1, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(2382, 1934.277343, 161.106323, 38.249794, 0.000007, 0.000001, 68.899986, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2803, "cj_meaty", "CJ_FLESH_2", 0x00000000);
    SetDynamicObjectMaterial(btchrstx, 1, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(2382, 1942.019897, 158.475936, 38.249794, 0.000000, 0.000000, 68.900001, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2803, "cj_meaty", "CJ_FLESH_2", 0x00000000);
    SetDynamicObjectMaterial(btchrstx, 1, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(2382, 1944.669555, 157.453552, 38.249794, 0.000000, 0.000000, 68.900001, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2803, "cj_meaty", "CJ_FLESH_2", 0x00000000);
    SetDynamicObjectMaterial(btchrstx, 1, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(2960, 1934.954467, 161.027267, 38.899799, 90.000007, 0.000001, 158.899993, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 1, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(2960, 1939.236938, 159.374816, 38.899799, 90.000007, 0.000001, 158.899993, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 1, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(2960, 1943.537597, 157.715133, 38.899799, 90.000007, 0.000001, 158.899993, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 1, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(2960, 1947.818237, 156.062698, 38.899799, 90.000007, 0.000001, 158.899993, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 1, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(2960, 1952.091308, 154.413925, 38.899799, 90.000007, 0.000001, 158.899993, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 1, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(2960, 1953.974975, 153.376617, 36.749790, 180.000000, 90.000000, 248.899993, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 1, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(2790, 1931.390136, 158.596450, 41.507385, 0.000014, 0.000003, 252.099975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 16322, "a51_stores", "Gen_Freight", 0x00000000);
    SetDynamicObjectMaterial(btchrstx, 1, 14581, "ab_mafiasuitea", "ab_walnutLite", 0x00000000);
    btchrstx = CreateDynamicObject(2790, 1931.285400, 158.630249, 41.957386, 0.000014, 0.000003, 252.099975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterialText(btchrstx, 1, "KANTOR AYAM", 130, "Arial", 60, 1, 0xFFFFFFFF, 0x00000000, 1);
    btchrstx = CreateDynamicObject(2790, 1931.285400, 158.630249, 41.177391, 0.000014, 0.000003, 252.099975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterialText(btchrstx, 1, "KOTA ATHERLIFE", 130, "Arial", 40, 1, 0xFFFFFFFF, 0x00000000, 1);
    btchrstx = CreateDynamicObject(18766, 1940.201049, 149.908599, 39.861095, 90.000000, 90.000000, 70.600021, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 16322, "a51_stores", "des_ghotwood1", 0x00000000);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(3276, 1938.417236, 147.735870, 37.087295, 0.000000, 0.000000, -17.299999, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14873, 1925.232299, 155.552474, 36.991245, 0.000000, 0.000000, -107.900009, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3252, 1910.586547, 155.420013, 36.193191, 0.000000, 0.000000, -13.799996, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3663, 1939.292236, 153.185272, 38.361099, 0.000000, 0.000000, 166.599975, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2803, 1933.870605, 157.366226, 36.701107, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2804, 1931.588378, 155.811294, 36.841110, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2804, 1931.588378, 156.401321, 36.841110, 0.000000, 0.000000, -52.299999, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2804, 1932.004150, 155.863250, 36.841110, 0.000000, 0.000000, 175.899993, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2803, 1934.970947, 157.046203, 36.591106, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2991, 1928.997558, 157.069976, 36.868167, 0.000000, 0.000000, -15.499999, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1389, 1934.095214, 161.244445, 36.509796, 0.000000, 0.000000, -21.099998, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(914, 1941.244628, 153.438919, 39.771102, 0.000000, 90.000000, -107.400016, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2649, 1934.113037, 160.899688, 40.251113, 0.000000, 180.000000, 341.600006, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2653, 1936.323974, 160.164215, 40.551116, 0.000000, 180.000000, 431.600006, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2653, 1943.544921, 157.762054, 40.551116, 0.000000, 180.000000, 431.600006, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3066, 1938.527221, 162.063812, 36.509796, 0.000000, 0.000000, 71.800003, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(16083, 1940.000366, 152.829025, 34.741100, 0.000000, 0.000000, -28.300003, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3066, 1947.656982, 159.062286, 36.509796, 0.000000, 0.000000, 71.800003, 0, 0, -1, 200.00, 200.00);
}
*/

RemoveChickenCorpBuilding(playerid)
{
    //pengambilan ayam
    RemoveBuildingForPlayer(playerid, 13002, 1554.050, -0.312, 26.812, 0.250);
    RemoveBuildingForPlayer(playerid, 13003, 1559.630, 20.015, 23.546, 0.250);
    RemoveBuildingForPlayer(playerid, 935, 1566.739, 16.367, 23.671, 0.250);
    RemoveBuildingForPlayer(playerid, 935, 1550.560, 11.109, 23.632, 0.250);

    //pabrik ayam / slaughterhouse
    RemoveBuildingForPlayer(playerid, 18240, -2097.449, -2430.199, 29.617, 0.250);
    RemoveBuildingForPlayer(playerid, 18528, -2097.449, -2430.199, 29.617, 0.250);
    RemoveBuildingForPlayer(playerid, 18238, -2077.229, -2444.939, 29.617, 0.250);
    RemoveBuildingForPlayer(playerid, 18529, -2077.229, -2444.939, 29.617, 0.250);
    RemoveBuildingForPlayer(playerid, 18204, -2088.649, -2454.300, 41.125, 0.250);
    RemoveBuildingForPlayer(playerid, 1332, -2087.989, -2440.949, 30.695, 0.250);
    RemoveBuildingForPlayer(playerid, 1338, -2081.840, -2428.229, 30.335, 0.250);
    RemoveBuildingForPlayer(playerid, 18257, -2084.419, -2423.719, 29.617, 0.250);
    RemoveBuildingForPlayer(playerid, 1332, -2085.320, -2414.510, 30.640, 0.250);
    RemoveBuildingForPlayer(playerid, 1338, -2091.860, -2415.709, 30.335, 0.250);
    RemoveBuildingForPlayer(playerid, 1338, -2098.100, -2412.419, 30.335, 0.250);
    RemoveBuildingForPlayer(playerid, 1334, -2107.209, -2423.889, 30.796, 0.250);
    RemoveBuildingForPlayer(playerid, 18448, -2069.770, -2514.810, 42.515, 0.250);
}

CreateExtPabrikAyam()
{
    new STREAMER_TAG_OBJECT: btchrstx;

    //pengambilan ayam
    btchrstx = CreateDynamicObject(19353, 1537.975219, 7.299127, 24.566040, 0.000007, -0.000001, 107.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1541.028198, 8.291071, 24.566040, 0.000007, -0.000001, 107.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(12922, 1553.674804, 40.612930, 26.481821, 0.000000, 0.000000, -166.399917, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(btchrstx, 2, "MilkerAN AYAM ATHERLIFE", 110, "Arial", 40, 1, 0xFF000000, 0xFFFF91A4, 1);
    btchrstx = CreateDynamicObject(19353, 1544.081176, 9.283017, 24.566040, 0.000007, -0.000001, 107.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1547.076660, 10.259127, 24.566040, 0.000014, -0.000003, 107.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1550.129638, 11.251072, 24.566040, 0.000014, -0.000003, 107.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1553.182617, 12.243018, 24.566040, 0.000014, -0.000003, 107.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1556.185913, 13.209143, 24.566040, 0.000014, -0.000003, 107.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1559.238891, 14.201089, 24.566040, 0.000014, -0.000003, 107.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1562.291870, 15.193035, 24.566040, 0.000014, -0.000003, 107.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1565.287353, 16.169147, 24.566040, 0.000020, -0.000007, 107.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1568.340332, 17.161090, 24.566040, 0.000020, -0.000007, 107.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1571.393310, 18.153036, 24.566040, 0.000020, -0.000007, 107.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1535.919433, 8.219160, 24.566040, 0.000007, -0.000001, 197.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1536.804565, 10.299139, 24.566040, 0.000014, -0.000003, 107.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1539.857543, 11.291084, 24.566040, 0.000014, -0.000003, 107.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1542.910522, 12.283027, 24.566040, 0.000014, -0.000003, 107.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1545.906005, 13.259140, 24.566040, 0.000020, -0.000007, 107.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1548.958984, 14.251085, 24.566040, 0.000020, -0.000007, 107.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1552.011962, 15.243028, 24.566040, 0.000020, -0.000007, 107.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1555.015258, 16.209156, 24.566040, 0.000020, -0.000007, 107.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1558.068237, 17.201103, 24.566040, 0.000020, -0.000007, 107.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1561.121215, 18.193046, 24.566040, 0.000020, -0.000007, 107.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1564.116699, 19.169158, 24.566040, 0.000029, -0.000009, 107.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1567.169677, 20.161102, 24.566040, 0.000029, -0.000009, 107.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1570.222656, 21.153047, 24.566040, 0.000029, -0.000009, 107.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1569.703369, 22.750820, 24.566040, 0.000029, -0.000009, 197.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1568.708740, 25.813215, 24.566040, 0.000029, -0.000009, 197.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1567.720336, 28.856586, 24.566040, 0.000029, -0.000009, 197.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1566.728515, 31.909484, 24.566040, 0.000029, -0.000009, 197.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1565.733398, 34.971900, 24.566040, 0.000029, -0.000009, 197.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1566.653198, 37.058395, 24.566040, 0.000029, -0.000009, 107.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1568.615600, 35.908199, 24.566040, 0.000029, -0.000009, 197.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1569.607299, 32.855289, 24.566040, 0.000029, -0.000009, 197.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1570.596923, 29.811901, 24.566040, 0.000029, -0.000009, 197.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1571.589233, 26.759004, 24.566040, 0.000029, -0.000009, 197.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1572.581665, 23.706106, 24.566041, 0.000029, -0.000009, 197.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1573.573486, 20.653215, 24.566041, 0.000029, -0.000009, 197.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1572.655761, 18.573528, 24.566040, 0.000020, -0.000007, 107.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1568.204711, 35.774906, 28.385887, 0.000025, 44.999984, -162.000045, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1565.972534, 35.049430, 28.371749, 0.000030, 45.000000, 17.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1565.743408, 34.971900, 25.996046, 0.000025, -0.000015, -162.000045, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1566.653198, 37.068393, 25.996046, 0.000036, -0.000011, 107.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1568.605590, 35.908199, 25.996046, 0.000025, -0.000015, -162.000045, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1569.174682, 32.724922, 28.385887, 0.000020, 44.999969, -161.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1566.942504, 31.999443, 28.371749, 0.000036, 45.000015, 17.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1566.713378, 31.921915, 25.996046, 0.000020, -0.000030, -161.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1570.155639, 29.674940, 28.385887, 0.000018, 44.999961, -161.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1569.575561, 32.858215, 25.996046, 0.000020, -0.000030, -161.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1567.923461, 28.949462, 28.371749, 0.000037, 45.000022, 17.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1567.694335, 28.871931, 25.996046, 0.000018, -0.000037, -161.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1570.556518, 29.808233, 25.996046, 0.000018, -0.000037, -161.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1571.145751, 26.684946, 28.385887, 0.000017, 44.999954, -161.999862, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1568.913574, 25.959470, 28.371749, 0.000039, 45.000030, 17.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1568.684448, 25.881940, 25.996046, 0.000017, -0.000045, -161.999862, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1571.546630, 26.818241, 25.996046, 0.000017, -0.000045, -161.999862, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1572.134765, 23.634935, 28.385887, 0.000014, 44.999946, -161.999816, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1569.902587, 22.909458, 28.371749, 0.000043, 45.000038, 17.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1569.673461, 22.831928, 25.996046, 0.000014, -0.000051, -161.999816, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1572.535644, 23.768228, 25.996046, 0.000014, -0.000051, -161.999816, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1573.115356, 20.584941, 28.385887, 0.000012, 44.999938, -161.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1570.447021, 21.200460, 28.371749, 0.000045, 45.000045, 17.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1567.956665, 17.040872, 25.996046, 0.000001, -0.000065, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1573.546997, 20.654756, 25.996046, 0.000012, -0.000059, -161.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1570.200683, 20.576396, 28.385887, 0.000001, 44.999935, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1570.783203, 18.297876, 28.371749, 0.000055, 45.000049, 107.999855, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1571.009521, 18.032825, 25.996046, 0.000001, -0.000065, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1570.014404, 21.075712, 25.996046, 0.000001, -0.000065, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1572.626831, 18.589767, 26.356046, 0.000001, -0.000065, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1567.862792, 17.349210, 28.371753, 0.000055, 45.000049, 107.999855, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1572.506835, 18.850460, 28.378820, 0.000055, 45.000049, 107.999855, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1566.962158, 20.083749, 25.996046, 0.000001, -0.000065, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1563.909667, 19.091777, 25.996046, 0.000001, -0.000065, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1560.856933, 18.099817, 25.996046, 0.000001, -0.000065, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1557.804565, 17.107858, 25.996046, 0.000001, -0.000065, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1554.752075, 16.115896, 25.996046, 0.000001, -0.000065, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1551.708740, 15.127028, 25.996046, 0.000001, -0.000065, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1548.656005, 14.135069, 25.996046, 0.000001, -0.000065, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1545.613159, 13.146203, 25.996046, 0.000001, -0.000065, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1542.560302, 12.154247, 25.996046, 0.000001, -0.000065, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1539.516235, 11.165379, 25.996046, 0.000001, -0.000065, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1536.828735, 10.312964, 25.996046, 0.000001, -0.000065, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1567.148315, 19.584430, 28.385887, 0.000001, 44.999935, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1564.095458, 18.592472, 28.385887, 0.000001, 44.999935, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1566.481689, 36.991394, 27.065397, 45.300052, -0.000011, 107.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1561.042724, 17.600519, 28.385887, 0.000001, 44.999935, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1557.999511, 16.611650, 28.385887, 0.000001, 44.999935, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1554.946533, 15.619692, 28.385887, 0.000001, 44.999935, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1551.893798, 14.627737, 28.385887, 0.000001, 44.999935, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1548.841308, 13.635785, 28.385887, 0.000001, 44.999935, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1545.789062, 12.643833, 28.385887, 0.000001, 44.999935, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1542.736694, 11.651873, 28.385887, 0.000001, 44.999935, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1539.693115, 10.663002, 28.385887, 0.000001, 44.999935, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1536.640869, 9.671046, 28.385887, 0.000001, 44.999935, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1564.904174, 16.048912, 25.996046, 0.000001, -0.000065, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1561.860595, 15.060047, 25.996046, 0.000001, -0.000065, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1558.807128, 14.068088, 25.996046, 0.000001, -0.000065, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1555.754272, 13.076130, 25.996046, 0.000001, -0.000065, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1552.717773, 12.096776, 25.996046, 0.000001, -0.000065, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1549.674926, 11.107909, 25.996046, 0.000001, -0.000065, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1546.622192, 10.115950, 25.996046, 0.000001, -0.000065, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1543.569702, 9.123993, 25.996046, 0.000001, -0.000065, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1540.516845, 8.132033, 25.996046, 0.000001, -0.000065, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1537.849731, 7.213040, 25.996046, 0.000001, -0.000065, -71.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1535.909912, 8.216070, 25.986045, 0.000007, -0.000001, 197.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1564.809692, 16.357271, 28.371753, 0.000055, 45.000049, 107.999855, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1561.756103, 15.365325, 28.371753, 0.000055, 45.000049, 107.999855, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1558.703369, 14.373387, 28.371753, 0.000055, 45.000049, 107.999855, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1555.660034, 13.384542, 28.371755, 0.000055, 45.000049, 107.999855, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1552.607055, 12.392601, 28.371755, 0.000055, 45.000049, 107.999855, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1549.554199, 11.400664, 28.371755, 0.000055, 45.000049, 107.999855, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1546.501586, 10.408726, 28.371755, 0.000055, 45.000049, 107.999855, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1543.448974, 9.416790, 28.371761, 0.000055, 45.000049, 107.999855, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1540.396118, 8.424851, 28.371763, 0.000055, 45.000049, 107.999855, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1537.343261, 7.432911, 28.371763, 0.000055, 45.000049, 107.999855, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1535.958740, 8.098112, 27.273000, 45.000007, -0.000001, 197.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14570, "traidaqua", "sa_wood06_128", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1567.882690, 36.721549, 28.371746, 0.000025, 44.999984, -162.000045, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1565.664184, 36.000473, 28.371749, 0.000030, 45.000000, 17.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(1881, 1546.406616, 24.184078, 22.963787, 89.999992, 590.056579, -25.056560, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    btchrstx = CreateDynamicObject(1881, 1546.703002, 24.156372, 22.963787, 89.999992, 540.056579, -25.056560, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    btchrstx = CreateDynamicObject(1881, 1546.578002, 24.235748, 22.963787, 89.999992, 665.056579, -25.056560, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1564.344360, 34.635559, 26.954713, 0.000030, 70.400001, 17.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1565.336059, 31.582666, 26.954713, 0.000030, 70.400001, 17.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1566.328491, 28.529766, 26.954713, 0.000030, 70.400001, 17.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1567.320922, 25.476863, 26.954713, 0.000030, 70.400001, 17.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1568.313110, 22.423969, 26.954713, 0.000030, 70.400001, 17.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1561.764160, 20.093425, 26.832109, 0.000030, 70.400001, 287.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1564.826171, 21.088462, 26.832109, 0.000030, 70.400001, 287.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1567.869750, 22.077320, 26.832109, 0.000030, 70.400001, 287.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1558.711425, 19.101488, 26.832109, 0.000030, 70.400001, 287.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1555.658569, 18.109544, 26.832109, 0.000030, 70.400001, 287.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1552.605468, 17.117607, 26.832109, 0.000030, 70.400001, 287.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1549.552978, 16.125661, 26.832109, 0.000030, 70.400001, 287.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1546.500122, 15.133721, 26.832109, 0.000030, 70.400001, 287.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1543.447143, 14.141777, 26.832109, 0.000030, 70.400001, 287.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1540.394165, 13.149836, 26.832107, 0.000030, 70.400001, 287.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1537.350708, 12.160984, 26.842107, 0.000030, 70.400001, 287.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1535.942504, 11.703643, 26.832107, 0.000030, 70.400001, 287.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(19353, 1564.094482, 35.384803, 26.973554, 0.000030, 70.400001, 17.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 18233, "cuntwshopscs_t", "des_oldtinroof", 0x00000000);
    btchrstx = CreateDynamicObject(1881, 1546.060058, 24.149005, 22.963787, 89.999992, 385.856506, -25.056560, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    btchrstx = CreateDynamicObject(1881, 1545.934814, 23.827213, 22.963787, 89.999992, 475.856536, -25.056560, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    btchrstx = CreateDynamicObject(1881, 1545.814208, 23.986244, 22.963787, 89.999992, 509.156402, -25.056560, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    btchrstx = CreateDynamicObject(1881, 1545.761108, 24.324203, 22.963787, 89.999992, 446.056457, -25.056560, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    btchrstx = CreateDynamicObject(1881, 1545.301635, 24.122177, 22.963787, 89.999992, 385.556457, -25.056560, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    btchrstx = CreateDynamicObject(1881, 1544.937500, 24.116950, 22.963787, 89.999992, 415.556457, -25.056560, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    btchrstx = CreateDynamicObject(1881, 1544.711303, 24.102092, 22.963787, 89.999992, 365.256469, -25.056560, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    btchrstx = CreateDynamicObject(1881, 1544.392700, 24.131832, 22.963787, 89.999992, 385.556457, -25.056560, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    btchrstx = CreateDynamicObject(1881, 1544.104980, 23.829319, 22.963787, 89.999992, 475.556488, -25.056560, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    btchrstx = CreateDynamicObject(1881, 1544.111816, 24.139389, 22.963787, 89.999992, 475.556488, -25.056560, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    btchrstx = CreateDynamicObject(1881, 1544.109008, 24.439373, 22.963787, 89.999992, 475.556488, -25.056560, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    btchrstx = CreateDynamicObject(1881, 1543.512817, 24.124149, 22.963787, 89.999992, 385.556457, -25.056560, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    btchrstx = CreateDynamicObject(1881, 1543.328369, 24.103878, 22.963787, 89.999992, 417.156463, -25.056560, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    btchrstx = CreateDynamicObject(1881, 1543.111206, 24.061893, 22.963787, 89.999992, 377.556457, -25.056560, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    btchrstx = CreateDynamicObject(1881, 1542.485839, 24.184078, 22.963787, 89.999992, 590.056579, -25.056560, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    btchrstx = CreateDynamicObject(1881, 1542.782226, 24.156372, 22.963787, 89.999992, 540.056579, -25.056560, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    btchrstx = CreateDynamicObject(1881, 1542.657226, 24.235748, 22.963787, 89.999992, 665.056579, -25.056560, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(994, 1524.076171, 24.719253, 23.183534, -0.000074, 0.000012, -81.599800, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(994, 1518.396972, 2.285730, 22.913177, 0.000061, -2.699956, 53.399921, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(994, 1514.024780, 32.121894, 23.482328, -0.000043, 2.800060, -36.599971, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(994, 1512.212158, 1.416232, 22.933536, -0.000022, 0.000051, 8.400057, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(994, 1506.028686, 0.503630, 23.253471, -0.000022, 2.500051, 8.400057, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(994, 1519.020385, 28.412698, 23.183534, -0.000043, 0.000061, -36.599971, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(994, 1524.989990, 18.526393, 23.183534, -0.000074, 0.000012, -81.599800, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(994, 1522.142089, 7.325896, 23.183534, 0.000061, 0.000043, 53.399921, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1497, 1539.664550, 11.338300, 23.104505, 0.000000, 0.000000, -161.700012, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1497, 1544.522583, 12.892189, 23.104505, 0.000000, 0.000000, -161.700012, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1497, 1550.009887, 14.675445, 23.104505, 0.000000, 0.000000, -161.700012, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1497, 1558.409179, 17.400522, 23.104505, 0.000000, 0.000000, -161.700012, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1497, 1565.685424, 19.764577, 23.104505, 0.000000, 0.000000, -161.700012, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1497, 1568.894653, 24.965305, 23.104505, 0.000000, 0.000000, -71.700012, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1497, 1567.491088, 29.272483, 23.104505, 0.000000, 0.000000, -71.700012, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1497, 1565.939819, 33.994270, 23.104505, 0.000000, 0.000000, -71.700012, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3276, 1583.176147, 48.060085, 25.084934, 0.000000, 0.099999, 15.099995, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3276, 1565.149414, 43.195892, 24.527444, 0.000000, -6.399998, 15.099995, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3276, 1540.897827, 36.652076, 23.981355, 0.000000, 0.399998, 15.099995, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3276, 1521.430053, 37.696788, 24.427665, 0.000000, -0.500000, 15.099995, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3276, 1508.619140, -0.355066, 23.868770, 0.000000, -2.900000, -172.699951, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3276, 1525.986816, 2.375143, 23.550115, 0.000000, 3.199997, 330.100006, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3276, 1545.111206, 4.073155, 23.270299, 0.000000, 1.799998, 195.100006, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3276, 1556.581420, 7.168145, 23.443563, 0.000000, 0.899999, 195.100006, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3276, 1568.421875, 10.363189, 23.208080, 0.000000, -4.299999, 195.100006, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3425, 1518.368286, -2.218461, 33.794979, 0.000000, 0.000000, -11.999993, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(12912, 1549.539062, -30.307855, 31.599973, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14875, 1536.279418, 17.664127, 23.856653, 0.000000, 0.000000, 15.800000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14875, 1538.167968, 18.333774, 23.896654, 0.000000, 0.000000, 195.800003, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, 1546.673095, 14.387598, 23.581342, 0.000000, 0.000000, 20.600002, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, 1540.953979, 18.839576, 23.568382, 0.000000, 0.000000, 17.500000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(17055, 1540.971069, 33.434844, 25.067192, 0.000000, 0.000000, -164.600021, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(935, 1566.369628, 30.444257, 23.695444, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(935, 1566.369628, 31.204261, 23.695444, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(933, 1568.717041, 21.985834, 23.071239, 1.000000, 0.399998, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(925, 1534.922363, 8.005655, 23.953439, 0.000000, 5.199998, -71.700012, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3447, 1538.329956, 13.820089, 18.918203, 0.000000, 180.000000, 17.899997, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3447, 1549.197265, 17.330091, 18.918203, 0.000000, 180.000000, 17.899997, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3447, 1561.216430, 21.211996, 18.918203, 0.000000, 180.000000, 17.899997, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3447, 1564.618408, 29.225408, 18.918203, 0.000000, 180.000000, 107.899993, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, 1561.324951, 36.274059, 26.456228, 0.000000, 0.000000, 20.300001, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, 1519.817016, 39.098846, 27.546220, 0.000000, 0.000000, 290.299987, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, 1502.899169, 34.684741, 27.826215, 0.000000, 0.000000, 290.299987, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, 1564.890869, 44.853694, 27.516216, 0.000000, 0.000000, 290.299987, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, 1582.738159, 49.845394, 28.146211, 0.000000, 0.000000, 290.299987, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, 1539.264892, 37.922302, 26.906187, 0.000000, 0.000000, 290.299987, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1451, 1535.097778, 33.523986, 23.922494, 0.000000, 0.000000, -78.899971, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1451, 1535.558471, 31.178686, 23.922494, 0.000000, 0.000000, -78.899971, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, 1495.179443, 12.849389, 28.106206, 0.000000, 0.000000, -169.100021, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, 1520.421386, 4.869028, 26.686199, 0.000000, 0.000000, -79.100021, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1449, 1551.885620, 15.519186, 23.642843, 0.000000, 0.000000, -161.400054, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1449, 1568.223022, 26.324935, 23.652843, 0.000000, 0.000000, -71.400054, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1450, 1564.972778, 35.032402, 23.830081, 0.000000, 0.000000, -71.699951, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1368, 1561.347656, 18.798233, 23.830848, 0.000000, 0.000000, -162.199935, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(994, 1507.815185, 31.183372, 23.473533, -0.000022, 0.000051, 8.400057, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(994, 1501.671997, 30.276304, 23.961696, -0.000022, 5.100049, 8.400057, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3276, 1510.015014, 34.616649, 24.694414, 0.000000, 0.899999, 15.099995, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3276, 1498.620117, 31.542127, 25.768615, 0.000000, 10.000000, 15.099995, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(994, 1495.977661, 7.877205, 23.833671, 0.000009, -3.799990, 98.399887, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(994, 1501.680419, 30.337425, 24.182098, -0.000000, -3.500011, -126.599914, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(994, 1506.071044, 0.441384, 23.222948, 0.000013, -3.999999, 143.399856, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(994, 1501.061645, 4.161890, 23.642610, 0.000013, -2.800000, 143.399856, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(994, 1495.070678, 14.046595, 24.263204, 0.000009, -6.399990, 98.399887, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(994, 1497.932617, 25.291309, 24.572652, -0.000000, -3.500012, -126.599914, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3276, 1490.647216, 24.300056, 26.719039, 0.000000, -1.600003, 72.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3276, 1490.618408, 12.325286, 25.858257, 0.000000, -6.600004, 103.600006, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3276, 1497.324707, 3.250127, 24.705558, 0.000000, -3.300004, 147.400054, 0, 0, -1, 200.00, 200.00);

    //pabrik ayam
    btchrstx = CreateDynamicObject(18981, -2100.640136, -2417.465332, 23.347898, 0.000003, -0.000004, 141.400054, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 6340, "rodeo05_law2", "citywall6", 0x00000000);
    btchrstx = CreateDynamicObject(18981, -2061.793212, -2448.181396, 24.227914, 0.000003, -0.000006, 142.700042, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 6340, "rodeo05_law2", "citywall6", 0x00000000);
    btchrstx = CreateDynamicObject(18981, -2090.486572, -2425.568359, 29.137928, 0.000000, 90.000000, 141.400070, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 8391, "ballys01", "greyground256128", 0x00000000);
    btchrstx = CreateDynamicObject(18765, -2097.340820, -2421.710693, 31.627643, 0.000000, 0.000000, -38.700057, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 9227, "sfn_caravansfn", "trail_wall4", 0x00000000);
    btchrstx = CreateDynamicObject(18765, -2091.810546, -2414.804443, 31.627643, 0.000000, 0.000000, -38.700057, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 9227, "sfn_caravansfn", "trail_wall4", 0x00000000);
    btchrstx = CreateDynamicObject(18763, -2098.421875, -2433.281494, 32.084701, 0.000000, 0.000000, -38.199989, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 9227, "sfn_caravansfn", "trail_wall4", 0x00000000);
    btchrstx = CreateDynamicObject(18763, -2096.070800, -2435.132324, 32.084701, 0.000000, 0.000000, -38.199989, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 9227, "sfn_caravansfn", "trail_wall4", 0x00000000);
    btchrstx = CreateDynamicObject(18763, -2091.858886, -2434.631835, 32.084701, 0.000000, 0.000000, -38.199989, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 9227, "sfn_caravansfn", "trail_wall4", 0x00000000);
    btchrstx = CreateDynamicObject(18763, -2088.998779, -2433.070556, 32.084701, 0.000000, 0.000000, -38.199989, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 9227, "sfn_caravansfn", "trail_wall4", 0x00000000);
    btchrstx = CreateDynamicObject(18763, -2087.142578, -2430.711669, 32.084701, 0.000000, 0.000000, -38.199989, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 9227, "sfn_caravansfn", "trail_wall4", 0x00000000);
    btchrstx = CreateDynamicObject(18763, -2087.107421, -2428.597167, 32.084701, 0.000000, 0.000000, -38.199989, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 6332, "rodeo01_law2", "rodwall04_LAw2", 0x00000000);
    btchrstx = CreateDynamicObject(18763, -2082.292480, -2424.517578, 32.084701, 0.000000, 0.000000, -38.199989, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 6332, "rodeo01_law2", "rodwall04_LAw2", 0x00000000);
    btchrstx = CreateDynamicObject(18763, -2079.150878, -2423.209472, 32.084701, 0.000000, 0.000000, -38.199989, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 9227, "sfn_caravansfn", "trail_wall4", 0x00000000);
    btchrstx = CreateDynamicObject(18763, -2077.807617, -2421.501953, 32.084701, 0.000000, 0.000000, -38.199989, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 9227, "sfn_caravansfn", "trail_wall4", 0x00000000);
    btchrstx = CreateDynamicObject(18763, -2071.951904, -2434.053222, 32.084701, 0.000000, 0.000000, -38.199989, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 16640, "a51", "vgs_shopwall01_128", 0x00000000);
    btchrstx = CreateDynamicObject(18763, -2079.933593, -2426.373779, 32.084701, 0.000000, 0.000000, -38.199989, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 6332, "rodeo01_law2", "rodwall04_LAw2", 0x00000000);
    btchrstx = CreateDynamicObject(18763, -2084.757812, -2430.446289, 32.084701, 0.000000, 0.000000, -38.199989, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 6332, "rodeo01_law2", "rodwall04_LAw2", 0x00000000);
    btchrstx = CreateDynamicObject(18763, -2083.713623, -2431.267822, 32.084701, 0.000000, 0.000000, -38.199989, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 6332, "rodeo01_law2", "rodwall04_LAw2", 0x00000000);
    btchrstx = CreateDynamicObject(18763, -2078.078125, -2424.015625, 32.084701, 0.000000, 0.000000, -38.199989, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 6332, "rodeo01_law2", "rodwall04_LAw2", 0x00000000);
    btchrstx = CreateDynamicObject(18763, -2076.756347, -2422.334716, 32.084701, 0.000000, 0.000000, -38.199989, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 6332, "rodeo01_law2", "rodwall04_LAw2", 0x00000000);
    btchrstx = CreateDynamicObject(18981, -2070.948974, -2441.161865, 29.147928, 0.000000, 90.000000, 141.400070, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 8391, "ballys01", "greyground256128", 0x00000000);
    btchrstx = CreateDynamicObject(18981, -2083.636718, -2415.407226, 23.347898, -0.000004, -0.000003, -128.599929, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 6340, "rodeo05_law2", "citywall6", 0x00000000);
    btchrstx = CreateDynamicObject(18763, -2068.931640, -2428.561279, 32.084701, 0.000000, 0.000000, -38.199989, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 16640, "a51", "vgs_shopwall01_128", 0x00000000);
    btchrstx = CreateDynamicObject(18763, -2063.402099, -2432.966796, 32.084701, 0.000000, 0.000000, -38.199989, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 16640, "a51", "vgs_shopwall01_128", 0x00000000);
    btchrstx = CreateDynamicObject(18766, -2061.178955, -2439.816406, 32.095855, 0.000000, 0.000000, -38.499973, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 16640, "a51", "vgs_shopwall01_128", 0x00000000);
    btchrstx = CreateDynamicObject(18763, -2065.250732, -2435.318847, 32.084701, 0.000000, 0.000000, -38.199989, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 16640, "a51", "vgs_shopwall01_128", 0x00000000);
    btchrstx = CreateDynamicObject(18766, -2075.705322, -2435.547851, 32.095855, 0.000004, 0.000003, 51.500026, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 16640, "a51", "vgs_shopwall01_128", 0x00000000);
    btchrstx = CreateDynamicObject(18766, -2081.257324, -2442.528564, 32.095855, 0.000004, 0.000003, 51.500026, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 16640, "a51", "vgs_shopwall01_128", 0x00000000);
    btchrstx = CreateDynamicObject(18766, -2086.951171, -2436.957031, 32.095855, 0.000004, 0.000003, 51.500026, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 6332, "rodeo01_law2", "rodwall04_LAw2", 0x00000000);
    btchrstx = CreateDynamicObject(18981, -2078.639160, -2450.465576, 24.237911, -0.000004, -0.000003, -128.599929, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 6340, "rodeo05_law2", "citywall6", 0x00000000);
    btchrstx = CreateDynamicObject(18981, -2092.304443, -2439.531005, 23.347898, -0.000004, -0.000003, -128.599929, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 6340, "rodeo05_law2", "citywall6", 0x00000000);
    btchrstx = CreateDynamicObject(18981, -2064.451416, -2430.722656, 23.347898, -0.000004, -0.000003, -128.599929, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 6340, "rodeo05_law2", "citywall6", 0x00000000);
    btchrstx = CreateDynamicObject(3799, -2091.781494, -2425.063232, 29.478845, 0.000000, 0.000000, -37.699985, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14415, "carter_block_2", "mp_carter_cage", 0x00000000);
    btchrstx = CreateDynamicObject(3799, -2103.573486, -2424.280029, 29.478845, 0.000000, 0.000000, -37.699985, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14415, "carter_block_2", "mp_carter_cage", 0x00000000);
    btchrstx = CreateDynamicObject(3799, -2090.255859, -2421.995361, 30.868864, 0.000000, 0.000000, -37.699985, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14415, "carter_block_2", "mp_carter_cage", 0x00000000);
    btchrstx = CreateDynamicObject(3799, -2090.255859, -2421.995361, 28.648849, 0.000000, 0.000000, -37.699985, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14415, "carter_block_2", "mp_carter_cage", 0x00000000);
    btchrstx = CreateDynamicObject(3799, -2088.464843, -2419.676757, 30.868864, 0.000000, 0.000000, -37.699985, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14415, "carter_block_2", "mp_carter_cage", 0x00000000);
    btchrstx = CreateDynamicObject(3799, -2088.465332, -2419.676757, 28.648849, 0.000000, 0.000000, -37.699985, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14415, "carter_block_2", "mp_carter_cage", 0x00000000);
    btchrstx = CreateDynamicObject(3799, -2086.673583, -2417.358642, 30.868864, 0.000000, 0.000000, -37.699985, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14415, "carter_block_2", "mp_carter_cage", 0x00000000);
    btchrstx = CreateDynamicObject(3799, -2086.674316, -2417.358154, 28.648849, 0.000000, 0.000000, -37.699985, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14415, "carter_block_2", "mp_carter_cage", 0x00000000);
    btchrstx = CreateDynamicObject(3799, -2085.218750, -2415.473632, 30.868864, 0.000000, 0.000000, -37.699985, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14415, "carter_block_2", "mp_carter_cage", 0x00000000);
    btchrstx = CreateDynamicObject(3799, -2082.930175, -2417.290527, 30.868864, 0.000000, 0.000000, -37.699985, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14415, "carter_block_2", "mp_carter_cage", 0x00000000);
    btchrstx = CreateDynamicObject(3799, -2080.629882, -2419.091552, 30.868864, 0.000000, 0.000000, -37.699985, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14415, "carter_block_2", "mp_carter_cage", 0x00000000);
    btchrstx = CreateDynamicObject(3799, -2079.540039, -2421.249023, 30.868864, 0.000000, 0.000000, -37.699985, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14415, "carter_block_2", "mp_carter_cage", 0x00000000);
    btchrstx = CreateDynamicObject(3799, -2081.207763, -2423.408935, 30.868864, 0.000000, 0.000000, -37.699985, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14415, "carter_block_2", "mp_carter_cage", 0x00000000);
    btchrstx = CreateDynamicObject(3799, -2082.905761, -2417.259277, 28.628875, 0.000000, 0.000000, -37.699985, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14415, "carter_block_2", "mp_carter_cage", 0x00000000);
    btchrstx = CreateDynamicObject(3799, -2080.586914, -2419.051269, 28.628875, 0.000000, 0.000000, -37.699985, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14415, "carter_block_2", "mp_carter_cage", 0x00000000);
    btchrstx = CreateDynamicObject(3799, -2080.624511, -2422.697509, 28.628875, 0.000000, 0.000000, 322.300018, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 14415, "carter_block_2", "mp_carter_cage", 0x00000000);
    btchrstx = CreateDynamicObject(18092, -2078.034179, -2431.885009, 30.209274, 0.000000, 0.000000, 51.700019, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    SetDynamicObjectMaterial(btchrstx, 1, 16640, "a51", "scratchedmetal", 0x00000000);
    btchrstx = CreateDynamicObject(18766, -2084.068115, -2438.790283, 32.088722, 0.000000, 0.000000, -38.600021, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 6332, "rodeo01_law2", "rodwall04_LAw2", 0x00000000);
    btchrstx = CreateDynamicObject(18763, -2083.944091, -2436.351806, 32.084701, 0.000000, 0.000000, -38.199989, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 6332, "rodeo01_law2", "rodwall04_LAw2", 0x00000000);
    btchrstx = CreateDynamicObject(19433, -2080.925292, -2438.527587, 30.684391, 0.000000, 90.000000, 51.399993, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 1560, "7_11_door", "cj_sheetmetal2", 0x00000000);
    btchrstx = CreateDynamicObject(19433, -2078.781250, -2435.836914, 30.684391, 0.000000, 90.000000, 51.399993, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 1560, "7_11_door", "cj_sheetmetal2", 0x00000000);
    btchrstx = CreateDynamicObject(19433, -2078.105224, -2434.985107, 30.694391, 0.000000, 90.000000, 51.399993, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 1560, "7_11_door", "cj_sheetmetal2", 0x00000000);
    btchrstx = CreateDynamicObject(18766, -2067.037109, -2446.104248, 32.095855, 0.000000, 0.000000, -38.499973, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 16640, "a51", "vgs_shopwall01_128", 0x00000000);
    btchrstx = CreateDynamicObject(18766, -2073.226562, -2441.184082, 32.095855, 0.000000, 0.000000, -38.499973, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 16640, "a51", "vgs_shopwall01_128", 0x00000000);
    btchrstx = CreateDynamicObject(19433, -2075.525878, -2435.837890, 30.684391, 0.000000, 90.000000, 141.399993, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 1560, "7_11_door", "cj_sheetmetal2", 0x00000000);
    btchrstx = CreateDynamicObject(19433, -2072.798583, -2438.014892, 30.684391, 0.000000, 90.000000, 141.399993, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 1560, "7_11_door", "cj_sheetmetal2", 0x00000000);
    btchrstx = CreateDynamicObject(19433, -2070.056396, -2440.204101, 30.684391, 0.000000, 90.000000, 141.399993, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 1560, "7_11_door", "cj_sheetmetal2", 0x00000000);
    btchrstx = CreateDynamicObject(19433, -2067.320800, -2442.386718, 30.684391, 0.000000, 90.000000, 141.399993, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 1560, "7_11_door", "cj_sheetmetal2", 0x00000000);
    btchrstx = CreateDynamicObject(19433, -2064.593994, -2444.561279, 30.684391, 0.000000, 90.000000, 141.399993, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 1560, "7_11_door", "cj_sheetmetal2", 0x00000000);
    btchrstx = CreateDynamicObject(19433, -2062.195312, -2446.477294, 31.735395, 0.000000, 127.599922, 141.399993, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 1560, "7_11_door", "cj_sheetmetal2", 0x00000000);
    btchrstx = CreateDynamicObject(2267, -2076.182128, -2435.291748, 31.169118, 0.000000, 0.000000, 231.500000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 1, 3032, "bdwinx", "ct_canopy", 0x00000000);
    btchrstx = CreateDynamicObject(2267, -2075.363281, -2435.980712, 31.169118, 0.000000, 0.000000, 51.500003, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 1, 3032, "bdwinx", "ct_canopy", 0x00000000);
    btchrstx = CreateDynamicObject(18981, -2090.486572, -2425.568359, 34.557998, 0.000000, 90.000000, 141.400070, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 3063, "col_wall1x", "mp_diner_ceilingdirt", 0x00000000);
    btchrstx = CreateDynamicObject(18981, -2071.551269, -2440.677978, 34.567996, 0.000000, 90.000000, 141.400070, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 3063, "col_wall1x", "mp_diner_ceilingdirt", 0x00000000);
    btchrstx = CreateDynamicObject(18981, -2090.507324, -2425.591796, 35.338031, 0.000000, 90.000000, 141.400070, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 6340, "rodeo05_law2", "citywall6", 0x00000000);
    btchrstx = CreateDynamicObject(18981, -2071.880371, -2440.424560, 35.338031, 0.000000, 90.000000, 141.400070, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 6340, "rodeo05_law2", "citywall6", 0x00000000);
    btchrstx = CreateDynamicObject(19449, -2104.772705, -2429.722167, 34.068473, 0.000000, 0.000000, 51.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 9625, "garage_sfw", "garage1b_sfw", 0x00000000);
    btchrstx = CreateDynamicObject(19479, -2098.707275, -2435.065673, 34.392158, 0.000000, 0.000000, -128.599990, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(btchrstx, 0, "Chicken Slaughterhouse", 120, "Times New Roman", 30, 1, 0xFFFFFFFF, 0x00000000, 1);
    btchrstx = CreateDynamicObject(19479, -2057.406738, -2435.689453, 35.072074, -0.000003, 0.000004, 51.399993, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(btchrstx, 0, "Chicken Slaughterhouse", 120, "Times New Roman", 30, 1, 0xFFFFFFFF, 0x00000000, 1);
    btchrstx = CreateDynamicObject(1881, -2088.881591, -2424.605957, 29.463191, 89.999992, 107.461570, -82.461463, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    btchrstx = CreateDynamicObject(1881, -2089.177978, -2424.578369, 29.463191, 89.999992, 57.461563, -82.461463, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    btchrstx = CreateDynamicObject(1881, -2089.052978, -2424.657714, 29.463191, 89.999992, 182.461578, -82.461463, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    btchrstx = CreateDynamicObject(1881, -2088.535156, -2424.570800, 29.463191, 89.999992, -96.738426, -82.461463, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    btchrstx = CreateDynamicObject(1881, -2088.409912, -2424.249023, 29.463191, 89.999992, -6.738427, -82.461463, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    btchrstx = CreateDynamicObject(1881, -2088.289306, -2424.408203, 29.463191, 89.999992, 26.561426, -82.461463, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    btchrstx = CreateDynamicObject(1881, -2088.236083, -2424.746093, 29.463191, 89.999992, -36.538524, -82.461463, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    btchrstx = CreateDynamicObject(1881, -2087.776611, -2424.543945, 29.463191, 89.999992, -97.038520, -82.461463, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    btchrstx = CreateDynamicObject(1881, -2087.412597, -2424.538818, 29.463191, 89.999992, -67.038520, -82.461463, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    btchrstx = CreateDynamicObject(1881, -2087.186279, -2424.523925, 29.463191, 89.999992, -117.338508, -82.461463, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    btchrstx = CreateDynamicObject(1881, -2086.867919, -2424.553710, 29.463191, 89.999992, -97.038505, -82.461463, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    btchrstx = CreateDynamicObject(1881, -2086.580078, -2424.251220, 29.463191, 89.999992, -7.038506, -82.461463, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    btchrstx = CreateDynamicObject(1881, -2086.586914, -2424.561279, 29.463191, 89.999992, -7.038506, -82.461463, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    btchrstx = CreateDynamicObject(1881, -2086.584228, -2424.861328, 29.463191, 89.999992, -7.038506, -82.461463, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    btchrstx = CreateDynamicObject(1881, -2085.987792, -2424.546142, 29.463191, 89.999992, -97.038505, -82.461463, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    btchrstx = CreateDynamicObject(1881, -2085.803466, -2424.525878, 29.463191, 89.999992, -65.438499, -82.461463, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    btchrstx = CreateDynamicObject(1881, -2085.586425, -2424.483886, 29.463191, 89.999992, -105.038505, -82.461463, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    btchrstx = CreateDynamicObject(1881, -2084.960937, -2424.605957, 29.463191, 89.999992, 107.461570, -82.461463, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    btchrstx = CreateDynamicObject(1881, -2085.257324, -2424.578369, 29.463191, 89.999992, 57.461563, -82.461463, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    btchrstx = CreateDynamicObject(1881, -2085.132324, -2424.657714, 29.463191, 89.999992, 182.461578, -82.461463, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    btchrstx = CreateDynamicObject(1881, -2110.672607, -2450.365966, 29.448463, 89.999992, -379.999938, -45.000003, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    btchrstx = CreateDynamicObject(1881, -2110.645019, -2450.069580, 29.448463, 89.999992, -429.999938, -45.000003, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    btchrstx = CreateDynamicObject(1881, -2110.724365, -2450.194580, 29.448463, 89.999992, -304.999938, -45.000003, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    btchrstx = CreateDynamicObject(1881, -2110.637451, -2450.712158, 29.448463, 89.999992, -584.199951, -45.000003, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    btchrstx = CreateDynamicObject(1881, -2110.315673, -2450.837646, 29.448463, 89.999992, -494.199951, -45.000003, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    btchrstx = CreateDynamicObject(1881, -2110.474853, -2450.958251, 29.448463, 89.999992, -460.900085, -45.000003, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    btchrstx = CreateDynamicObject(1881, -2110.812744, -2451.011474, 29.448463, 89.999992, -524.000061, -45.000003, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    btchrstx = CreateDynamicObject(1881, -2110.610595, -2451.470947, 29.448463, 89.999992, -584.500061, -45.000003, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    btchrstx = CreateDynamicObject(1881, -2110.605468, -2451.834716, 29.448463, 89.999992, -554.500061, -45.000003, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    btchrstx = CreateDynamicObject(1881, -2110.590576, -2452.061279, 29.448463, 89.999992, -604.800048, -45.000003, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    btchrstx = CreateDynamicObject(1881, -2110.620361, -2452.379638, 29.448463, 89.999992, -584.500061, -45.000003, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    btchrstx = CreateDynamicObject(1881, -2110.317871, -2452.667236, 29.448463, 89.999992, -494.500000, -45.000003, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    btchrstx = CreateDynamicObject(1881, -2110.627929, -2452.660400, 29.448463, 89.999992, -494.500000, -45.000003, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    btchrstx = CreateDynamicObject(1881, -2110.927978, -2452.663330, 29.448463, 89.999992, -494.500000, -45.000003, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    btchrstx = CreateDynamicObject(1881, -2110.612792, -2453.259521, 29.448463, 89.999992, -584.500061, -45.000003, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    btchrstx = CreateDynamicObject(1881, -2110.592529, -2453.444091, 29.448463, 89.999992, -552.900024, -45.000003, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    btchrstx = CreateDynamicObject(1881, -2110.550537, -2453.660888, 29.448463, 89.999992, -592.500061, -45.000003, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    btchrstx = CreateDynamicObject(1881, -2110.672607, -2454.286376, 29.448463, 89.999992, -379.999938, -45.000003, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    btchrstx = CreateDynamicObject(1881, -2110.645019, -2453.989990, 29.448463, 89.999992, -429.999938, -45.000003, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    btchrstx = CreateDynamicObject(1881, -2110.724365, -2454.114990, 29.448463, 89.999992, -304.999938, -45.000003, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(btchrstx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(18763, -2093.713867, -2436.988525, 32.084701, 0.000000, 0.000000, -38.199989, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1808, -2089.125488, -2429.142089, 29.594091, 0.000000, 0.000000, -39.399993, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19397, -2085.308593, -2426.089599, 31.365362, 0.000000, 0.000000, -37.799999, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11729, -2090.679199, -2432.388183, 29.602762, 0.000000, 0.000000, -127.599937, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11729, -2090.276367, -2431.864257, 29.602762, 0.000000, 0.000000, -127.599937, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19397, -2071.934082, -2430.115234, 31.365362, -0.000003, 0.000006, -37.799999, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11729, -2089.886962, -2431.358642, 29.602762, -0.000006, -0.000003, -127.599914, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11729, -2089.478027, -2430.827636, 29.602762, -0.000006, -0.000003, -127.599914, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11729, -2089.075195, -2430.303710, 29.602762, -0.000006, -0.000003, -127.599914, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2654, -2090.105224, -2431.605957, 31.857845, 0.000000, 0.000000, -36.099979, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2685, -2089.237060, -2430.913574, 31.978218, 0.000000, 0.000000, -128.199859, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1498, -2095.478515, -2427.361083, 29.598640, 0.000000, 0.000000, 51.399971, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1362, -2093.837158, -2426.012207, 30.225170, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3798, -2101.584228, -2426.022949, 29.631259, 0.000000, 0.000000, -38.499973, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3260, -2099.083251, -2428.155029, 29.670446, 270.000000, 360.000000, 54.899990, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3015, -2100.033447, -2426.660888, 29.824195, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1217, -2099.222167, -2427.740722, 30.098232, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1217, -2100.233154, -2427.740722, 30.098232, 0.000000, 0.000000, 96.400009, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1217, -2098.772216, -2428.661621, 30.098232, 0.000000, 0.000000, -60.399993, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2612, -2098.756835, -2427.039062, 31.589899, 0.000000, 0.000000, -38.599956, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1498, -2092.549072, -2433.117187, 29.598640, 0.000000, 0.000000, 231.399963, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19903, -2099.527099, -2431.771240, 29.625343, 0.000000, 0.000000, 140.099838, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1462, -2095.943115, -2428.736816, 29.625541, 0.000000, 0.000000, 53.000011, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19468, -2093.422851, -2426.588867, 29.715030, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19468, -2092.962402, -2426.398681, 29.715030, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1426, -2089.872558, -2422.071533, 29.795513, 0.000000, 0.000000, 51.800033, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1426, -2088.325927, -2420.107910, 29.805513, 0.000000, 0.000000, 51.800033, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1426, -2086.266113, -2417.489746, 29.805513, 0.000000, 0.000000, 51.800033, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1426, -2084.082275, -2415.836181, 29.805513, 0.000000, 0.000000, 141.800033, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1426, -2081.497558, -2417.871093, 29.805513, 0.000000, 0.000000, 141.800033, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1426, -2078.947998, -2419.953369, 29.805513, 0.000000, 0.000000, 141.800033, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1426, -2080.520263, -2421.950927, 29.805513, 0.000000, 0.000000, 231.800033, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1438, -2090.111816, -2422.041259, 33.219688, 0.000000, 0.000000, 48.800003, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19587, -2091.590332, -2425.788085, 31.842037, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1685, -2104.202392, -2424.396240, 30.383417, 0.000000, 0.000000, -38.900001, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1685, -2092.047851, -2424.732910, 30.383417, 0.000000, 0.000000, -38.900001, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1224, -2084.262939, -2419.537109, 30.220649, 0.000000, 0.000000, -74.599967, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1271, -2085.824951, -2419.798828, 30.003904, 0.000000, 0.000000, 52.299995, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1271, -2086.265380, -2420.369628, 30.003904, 0.000000, 0.000000, 51.400005, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19587, -2086.061523, -2420.134033, 30.352050, 0.000000, 0.000000, 53.599948, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1230, -2084.787841, -2416.708984, 33.629657, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1230, -2080.759277, -2419.800048, 33.629657, 0.000000, 0.000000, -60.799991, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2804, -2076.940185, -2430.237060, 30.730468, 0.000000, 0.000000, -89.400001, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2804, -2078.274169, -2431.751464, 30.730468, 0.000000, 0.000000, -176.499984, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(335, -2078.520019, -2431.901367, 30.719848, 90.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(335, -2077.919433, -2431.130615, 31.059846, 360.000000, 180.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2804, -2077.878662, -2431.224609, 30.730468, 0.000000, 0.000000, 45.400028, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(16101, -2075.648681, -2431.013183, 32.512550, 0.000000, 90.000000, -128.199935, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(16101, -2070.907226, -2424.986083, 32.512550, 0.000000, 90.000000, -128.199935, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11706, -2076.407714, -2429.767333, 29.632946, 0.000000, 0.000000, 141.799942, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1438, -2071.774169, -2427.063964, 29.726358, 0.000000, 0.000000, -40.199981, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1450, -2096.351806, -2432.213134, 30.211214, 0.000000, 0.000000, 141.099990, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1498, -2077.956298, -2426.252197, 29.598640, 0.000000, 0.000000, 51.799964, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1498, -2083.717285, -2434.673339, 29.598640, 0.000000, 0.000000, 321.799957, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19903, -2079.921630, -2435.431396, 29.635343, 0.000000, 0.000000, 140.099838, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2805, -2077.835205, -2433.805175, 31.730413, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2805, -2079.544921, -2435.992675, 31.820415, 0.000000, 0.000000, -37.899993, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2806, -2080.639648, -2438.158203, 30.766237, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2804, -2076.465332, -2435.068115, 30.793127, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2804, -2078.276367, -2435.577636, 30.793127, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3383, -2065.827636, -2445.781738, 29.594945, 0.000000, 0.000000, 141.299911, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2917, -2081.695312, -2439.112304, 33.788520, 0.000000, 0.000000, -38.599998, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1358, -2074.334472, -2424.997070, 30.824506, 0.000000, 0.000000, -36.200019, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3384, -2081.349853, -2431.870849, 31.050716, 0.000000, 0.000000, 141.700012, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19587, -2070.913085, -2439.524169, 30.777366, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19587, -2067.432861, -2442.254394, 30.777366, 0.000000, 0.000000, -38.299983, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(941, -2068.260253, -2439.866210, 30.116365, 0.000000, 0.000000, -38.999996, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2804, -2071.095703, -2439.477294, 30.783130, 0.000000, 0.000000, 143.399978, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2804, -2067.460937, -2442.357666, 30.783130, 0.000000, 0.000000, -95.400047, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2947, -2083.651123, -2429.381835, 29.621164, 0.000000, 0.000000, 52.199989, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2947, -2083.740966, -2429.513427, 29.621164, 0.000000, 0.000000, 232.199981, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2687, -2085.140136, -2428.202392, 31.021045, 0.000000, 0.000000, 141.699768, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(941, -2062.668945, -2446.134033, 30.116365, 0.000000, 0.000000, -38.999996, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1221, -2066.586425, -2441.188476, 30.075975, 0.000000, 0.000000, 53.099994, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1221, -2060.009521, -2443.923339, 30.085975, 0.000000, 0.000000, 8.099996, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1221, -2060.666992, -2444.996826, 30.085975, 0.000000, 0.000000, -40.399971, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2675, -2089.546630, -2426.826904, 29.706735, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2670, -2084.391357, -2421.665039, 29.717929, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2671, -2086.803710, -2422.665771, 29.647928, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2676, -2075.988037, -2426.748291, 29.747930, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2673, -2061.709228, -2443.581054, 29.727935, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2912, -2064.752441, -2431.634765, 29.627927, 0.000000, 0.000000, -38.600025, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2912, -2065.299316, -2431.199218, 29.627927, 0.000000, 0.000000, -38.600025, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2912, -2064.932128, -2431.494628, 30.317935, 0.000000, 0.000000, -38.600025, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1271, -2067.256347, -2430.100830, 29.977933, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1271, -2066.150878, -2430.604003, 29.977926, 0.000000, 0.000000, -37.299957, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1271, -2066.883056, -2430.458984, 30.657922, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2961, -2063.142333, -2438.902587, 30.937931, 0.000000, 0.000000, 141.199890, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11713, -2063.552490, -2438.608154, 30.697933, 0.000000, 0.000000, -128.400085, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11713, -2069.336181, -2430.192626, 30.597930, 0.000000, 0.000000, -128.199951, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11713, -2082.247802, -2426.515380, 30.747930, 0.000000, 0.000000, -128.500045, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2690, -2082.289794, -2426.557617, 30.807935, 0.000000, 0.000000, -32.599998, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1338, -2097.642333, -2428.973632, 30.315000, 0.000000, 0.000000, 45.700004, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1338, -2092.521728, -2427.375244, 30.317922, 0.000000, 0.000000, -33.800022, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1338, -2062.774658, -2440.144775, 30.327922, 0.000000, 0.000000, 134.099929, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11709, -2074.503662, -2432.541015, 30.327930, 0.000000, 0.000000, -128.899917, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2685, -2074.206787, -2432.816894, 31.218208, 0.000000, 0.000000, -128.199859, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(941, -2066.435302, -2433.468017, 30.116365, 0.000000, 0.000000, 51.000003, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(919, -2087.889648, -2419.585205, 36.266101, 0.000000, 0.000000, -37.499980, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(919, -2095.700439, -2429.762207, 36.266101, 0.000000, 0.000000, -37.499980, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(919, -2067.728515, -2436.085449, 36.266101, -0.000003, 0.000006, -37.499980, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(919, -2075.539306, -2446.262451, 36.266101, -0.000003, 0.000006, -37.499980, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2653, -2065.335205, -2445.504394, 33.977382, 180.000000, 0.000000, 51.099987, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2653, -2071.474853, -2440.549804, 33.977382, 180.000000, 0.000000, 51.099987, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2653, -2077.615722, -2435.594970, 33.977382, 180.000000, 0.000000, 51.099987, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2653, -2083.754882, -2430.641113, 33.977382, 180.000000, 0.000000, 51.099987, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19397, -2080.718017, -2429.651367, 31.365362, 0.000000, 0.000000, -37.799999, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3812, -2061.361572, -2447.088623, 32.682666, 0.000000, 0.000000, 142.100128, 0, 0, -1, 200.00, 200.00);
}