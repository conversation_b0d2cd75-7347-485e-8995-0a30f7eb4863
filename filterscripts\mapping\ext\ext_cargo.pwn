RemoveCargoBuilding(playerid)
{
    RemoveBuildingForPlayer(playerid, 10773, -1711.020, 65.125, 5.515, 0.250);
    RemoveBuildingForPlayer(playerid, 985, 2497.409, 2777.070, 11.531, 0.250);
    RemoveBuildingForPlayer(playerid, 986, 2497.409, 2769.110, 11.531, 0.250);
    RemoveBuildingForPlayer(playerid, 10832, -1737.949, 37.539, 4.335, 0.250);
    RemoveBuildingForPlayer(playerid, 968, -1736.739, 31.882, 3.507, 0.250);
    RemoveBuildingForPlayer(playerid, 966, -1736.739, 31.929, 2.531, 0.250);
}

CreateCargoExt()
{
    new STREAMER_TAG_OBJECT: crgoxts;

    crgoxts = CreateDynamicObject(18981, -1675.534545, 25.071865, -7.410508, 0.000000, 0.000000, -22.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(crgoxts, 0, 10101, "2notherbuildsfe", "sl_vicwall02", 0x00000000);
    crgoxts = CreateDynamicObject(18980, -1680.598022, 13.209155, -2.472908, 0.000000, 0.000000, -44.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(crgoxts, 0, 5710, "cemetery_law", "brickgrey", 0x00000000);
    crgoxts = CreateDynamicObject(18980, -1670.972290, 36.260322, -2.682907, 0.000000, 0.000000, -21.899997, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(crgoxts, 0, 5710, "cemetery_law", "brickgrey", 0x00000000);
    crgoxts = CreateDynamicObject(18980, -1701.950195, 116.315292, -6.402907, 0.000000, 0.000000, 45.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(crgoxts, 0, 5710, "cemetery_law", "brickgrey", 0x00000000);
    crgoxts = CreateDynamicObject(19479, -1723.423950, 91.666282, 3.711275, 0.000000, 0.000000, -42.000011, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(crgoxts, 0, "PARKIR", 90, "Calibri", 50, 1, 0xFF000000, 0x00000000, 1);
    crgoxts = CreateDynamicObject(19479, -1728.783081, 85.713661, 3.711275, 0.000000, 0.000000, -42.000011, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(crgoxts, 0, "AREA", 90, "Calibri", 50, 1, 0xFF000000, 0x00000000, 1);
    crgoxts = CreateDynamicObject(19479, -1712.109985, 27.426631, 8.031277, 0.000000, 0.000000, 134.999847, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(crgoxts, 0, "ATHERLIFE LOGISTICS", 90, "Calibri", 35, 1, 0xFFFFFFFF, 0x00000000, 1);
    crgoxts = CreateDynamicObject(19479, -1694.723022, -1.343530, 3.711275, 0.000000, 0.000000, -44.099990, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(crgoxts, 0, "DILARANG", 90, "Calibri", 30, 1, 0xFF000000, 0x00000000, 1);
    crgoxts = CreateDynamicObject(19479, -1689.465087, 3.989132, 3.711275, 0.000000, 0.000000, -44.899978, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(crgoxts, 0, "PARKER!!!", 90, "Calibri", 30, 1, 0xFF000000, 0x00000000, 1);
    crgoxts = CreateDynamicObject(2790, -1665.689331, 41.929965, 8.230217, 0.000000, 0.000000, 405.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(crgoxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(crgoxts, 2, 16093, "a51_ext", "ws_whitewall2_top", 0x00000000);
    crgoxts = CreateDynamicObject(19482, -1665.801147, 42.003067, 8.291329, 0.000000, 0.000000, 135.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(crgoxts, 0, "MOHON ANTRE\nNYELAK = HAWAI", 90, "Calibri", 35, 1, 0xFF000000, 0x00000000, 1);
    crgoxts = CreateDynamicObject(18980, -1755.642089, 24.093883, -2.812909, 0.000000, 0.000000, -0.299999, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(crgoxts, 0, 5710, "cemetery_law", "brickgrey", 0x00000000);
    crgoxts = CreateDynamicObject(18980, -1740.921875, 24.066802, -2.812909, 0.000000, 0.000000, -0.299999, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(crgoxts, 0, 5710, "cemetery_law", "brickgrey", 0x00000000);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(19456, -1745.790893, 24.104038, 4.271453, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19456, -1755.420898, 24.104038, 4.271453, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19456, -1691.258422, 105.898796, 4.271453, 0.000001, -0.000019, -135.000091, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19456, -1684.449096, 99.089347, 4.271453, 0.000001, -0.000019, -135.000091, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19456, -1698.068115, 112.708267, 4.271453, 0.000001, -0.000019, -135.000091, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19959, -1734.570800, 24.120531, 2.508171, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(7025, -1586.157226, 108.336944, 5.972301, 0.000000, 0.000000, 225.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(7025, -1599.699462, 95.927268, 5.972301, 0.000000, 0.000000, 765.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(10832, -1611.449462, 83.948410, 4.365808, 0.000000, 0.000000, 135.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3626, -1679.169677, 23.708276, 3.966550, 0.000000, 0.000000, -111.500000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2911, -1680.017089, 25.849123, 2.753551, 0.000000, 0.000000, 68.299987, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(10832, -1737.132934, 49.049297, 4.278493, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1237, -1660.085571, 48.923107, 2.536062, 0.000000, 0.000000, 135.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1237, -1661.867553, 50.704986, 2.536062, 0.000000, 0.000000, 135.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(966, -1739.836181, 43.335453, 2.526720, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(968, -1739.826416, 43.355064, 3.114600, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(983, -1679.154418, 31.793251, 3.208128, 0.000000, 0.000000, -22.200006, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(970, -1677.769409, 29.525583, 3.080812, 0.000000, 0.000000, 158.500015, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(983, -1672.569824, 41.973522, 3.208128, 0.000000, 0.000000, 472.799987, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(983, -1676.757934, 37.770923, 3.208128, 0.000000, 0.000000, 337.799987, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(970, -1676.080810, 30.568778, 3.080812, 0.000000, 0.000000, 158.500015, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(970, -1676.596801, 32.502914, 3.080812, 0.000000, 0.000000, 158.500015, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(970, -1674.869750, 33.562999, 3.080812, 0.000000, 0.000000, 158.500015, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(970, -1675.413330, 35.508152, 3.080812, 0.000000, 0.000000, 158.500015, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(970, -1673.664184, 36.538673, 3.080812, 0.000000, 0.000000, 158.500015, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(970, -1674.240722, 38.485481, 3.080812, 0.000000, 0.000000, 158.500015, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(970, -1671.208007, 39.010208, 3.080812, 0.000000, 0.000000, 158.500015, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(640, -1667.139160, 40.891700, 2.949614, 0.000000, 0.000000, 134.800003, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(983, -1667.277099, 45.375202, 3.208128, 0.000000, 0.000000, 852.799987, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(640, -1664.462768, 43.548225, 2.949614, 0.000000, 0.000000, 134.800003, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, -1669.335327, 38.700771, 3.081522, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, -1662.335815, 45.590713, 3.081522, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19641, -1678.935791, 16.702075, 4.537319, 0.000007, 0.000002, 67.900032, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19641, -1675.927001, 24.114297, 4.537319, 0.000007, 0.000002, 67.900032, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19641, -1672.917846, 31.526521, 4.537319, 0.000007, 0.000002, 67.900032, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19641, -1672.306152, 33.005226, 4.537319, 0.000007, 0.000002, 67.900032, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19353, -1678.895751, 28.132247, 0.813930, 180.000000, 0.000000, 68.800018, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, -1687.475952, 14.470772, 3.081522, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19641, -1683.511230, 10.153934, 4.537319, 0.000000, 0.000000, 45.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19641, -1689.168457, 4.497082, 4.537319, 0.000000, 0.000000, 45.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19641, -1694.825561, -1.159770, 4.537319, 0.000000, 0.000000, 45.899986, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19641, -1700.381958, -6.914077, 4.537319, 0.000000, 0.000000, 46.099983, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19641, -1705.926513, -12.726678, 4.537319, 0.000000, 0.000000, 46.499977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19641, -1709.390014, -16.389471, 4.537319, 0.000000, 0.000000, 46.499977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19641, -1715.533569, -21.445358, 4.537319, 0.000000, 0.000000, 32.699970, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19641, -1719.713500, -24.116531, 4.537319, 0.000000, 0.000000, 32.699970, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19641, -1740.772094, 20.177370, 4.537319, 0.000000, 0.000000, 89.999938, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19641, -1740.772094, 12.177377, 4.537319, 0.000000, 0.000000, 89.999938, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19641, -1740.772094, 4.187380, 4.537319, 0.000000, 0.000000, 89.999938, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19641, -1740.772094, -3.812619, 4.537319, 0.000000, 0.000000, 89.999938, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19456, -1745.790161, 24.104038, 7.761462, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19456, -1751.270385, 24.074037, 7.761462, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00);
}