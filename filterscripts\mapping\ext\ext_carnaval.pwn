RemoveCarnavalBuilding(playerid)
{
    RemoveBuildingForPlayer(playerid, 3752, 389.875, -2021.640, 29.929, 0.250);
    RemoveBuildingForPlayer(playerid, 3751, 389.875, -2021.640, 29.929, 0.250);
    RemoveBuildingForPlayer(playerid, 3752, 389.875, -2028.500, 32.226, 0.250);
    RemoveBuildingForPlayer(playerid, 3751, 389.875, -2028.500, 32.226, 0.250);
    RemoveBuildingForPlayer(playerid, 3752, 389.875, -2017.449, 24.031, 0.250);
    RemoveBuildingForPlayer(playerid, 3751, 389.875, -2017.449, 24.031, 0.250);
    RemoveBuildingForPlayer(playerid, 3752, 389.875, -2035.380, 29.953, 0.250);
    RemoveBuildingForPlayer(playerid, 3751, 389.875, -2035.380, 29.953, 0.250);
    RemoveBuildingForPlayer(playerid, 3752, 389.875, -2017.430, 16.851, 0.250);
    RemoveBuildingForPlayer(playerid, 3751, 389.875, -2017.430, 16.851, 0.250);
    RemoveBuildingForPlayer(playerid, 3752, 389.875, -2021.630, 10.984, 0.250);
    RemoveBuildingForPlayer(playerid, 3751, 389.875, -2021.630, 10.984, 0.250);
    RemoveBuildingForPlayer(playerid, 3752, 389.875, -2028.520, 8.781, 0.250);
    RemoveBuildingForPlayer(playerid, 3751, 389.875, -2028.520, 8.781, 0.250);
    RemoveBuildingForPlayer(playerid, 3752, 389.875, -2035.400, 10.945, 0.250);
    RemoveBuildingForPlayer(playerid, 3751, 389.875, -2035.400, 10.945, 0.250);
    RemoveBuildingForPlayer(playerid, 3752, 389.875, -2039.640, 16.843, 0.250);
    RemoveBuildingForPlayer(playerid, 3751, 389.875, -2039.640, 16.843, 0.250);
    RemoveBuildingForPlayer(playerid, 3752, 389.875, -2039.660, 24.109, 0.250);
    RemoveBuildingForPlayer(playerid, 3751, 389.875, -2039.660, 24.109, 0.250);
    RemoveBuildingForPlayer(playerid, 6298, 389.773, -2028.469, 19.804, 0.250);
    RemoveBuildingForPlayer(playerid, 6463, 389.773, -2028.469, 19.804, 0.250);
    RemoveBuildingForPlayer(playerid, 6461, 389.773, -2028.500, 20.109, 0.250);
    RemoveBuildingForPlayer(playerid, 6289, 387.890, -1900.439, 9.390, 0.250);
    RemoveBuildingForPlayer(playerid, 6459, 387.890, -1900.439, 9.390, 0.250);
    RemoveBuildingForPlayer(playerid, 6458, 379.578, -1946.300, 10.281, 0.250);
    RemoveBuildingForPlayer(playerid, 1215, 384.429, -1904.130, 7.375, 0.250);
    RemoveBuildingForPlayer(playerid, 1215, 377.453, -1897.280, 7.375, 0.250);
    RemoveBuildingForPlayer(playerid, 1280, 378.460, -1902.819, 7.210, 0.250);
}

CreateCarnavalExt()
{
    new STREAMER_TAG_OBJECT: carnvalst;
    carnvalst = CreateDynamicObject(16003, 413.355255, -1937.183715, 7.916946, 0.000000, 0.000014, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 1, 6522, "cuntclub_law2", "marinawindow1_256", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 2, 14652, "ab_trukstpa", "CJ_WOOD6", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 3, 14650, "ab_trukstpc", "sa_wood08_128", 0x00000000);
    carnvalst = CreateDynamicObject(6300, 405.085601, -1936.751831, -1.243494, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6351, "rodeo02_law2", "woodboards1", 0x00000000);
    carnvalst = CreateDynamicObject(3861, 386.979675, -1951.480590, 7.933670, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 15041, "bigsfsave", "ah_greencarp", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 14650, "ab_trukstpc", "sa_wood08_128", 0x00000000);
    carnvalst = CreateDynamicObject(3861, 392.389770, -1951.480590, 7.933670, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 2772, "airp_prop", "CJ_red_COUNTER", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 14650, "ab_trukstpc", "sa_wood08_128", 0x00000000);
    carnvalst = CreateDynamicObject(3861, 397.899719, -1951.480590, 7.933670, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 15040, "cuntcuts", "AH_pinkcurtain", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 14650, "ab_trukstpc", "sa_wood08_128", 0x00000000);
    carnvalst = CreateDynamicObject(3861, 397.899658, -1967.693237, 7.933670, 0.000007, -0.000014, 179.999786, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 7650, "vgnusedcar", "lightpurple2_32", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 14650, "ab_trukstpc", "sa_wood08_128", 0x00000000);
    carnvalst = CreateDynamicObject(3861, 392.489624, -1967.693237, 7.933670, 0.000007, -0.000014, 179.999786, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 1239, "icons6", "lyellow32", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 14650, "ab_trukstpc", "sa_wood08_128", 0x00000000);
    carnvalst = CreateDynamicObject(3861, 386.979736, -1967.693237, 7.933670, 0.000007, -0.000014, 179.999786, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 3820, "boxhses_sfsx", "LAbluewall", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 14650, "ab_trukstpc", "sa_wood08_128", 0x00000000);
    carnvalst = CreateDynamicObject(2491, 384.418365, -1965.471313, 6.810934, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 18245, "cw_junkyardmachin", "Was_scrpyd_light_yellow", 0x00000000);
    carnvalst = CreateDynamicObject(3440, 389.004791, -1965.122070, 9.098440, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 2381, "cloth_trackies", "trackTrblue", 0x00000000);
    carnvalst = CreateDynamicObject(3440, 383.764831, -1962.211181, 9.098440, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 2381, "cloth_trackies", "trackTrblue", 0x00000000);
    carnvalst = CreateDynamicObject(3440, 393.544494, -1962.211181, 9.098440, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 2381, "cloth_trackies", "trackTrblue", 0x00000000);
    carnvalst = CreateDynamicObject(3440, 389.004791, -1953.991821, 9.098440, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 2381, "cloth_trackies", "trackTrblue", 0x00000000);
    carnvalst = CreateDynamicObject(3438, 388.676086, -1959.546752, 11.387189, 0.000000, 270.000000, -179.999984, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 18901, "matclothes", "darkblue", 0x00000000);
    carnvalst = CreateDynamicObject(3440, 383.624755, -1957.081298, 9.098440, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 2381, "cloth_trackies", "trackTrblue", 0x00000000);
    carnvalst = CreateDynamicObject(3440, 393.734588, -1957.081298, 9.098440, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 2381, "cloth_trackies", "trackTrblue", 0x00000000);
    carnvalst = CreateDynamicObject(3438, 388.676086, -1959.546752, 12.447198, 0.000000, 270.000000, -179.999984, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 18901, "matclothes", "darkblue", 0x00000000);
    carnvalst = CreateDynamicObject(18667, 386.608459, -1954.184570, 11.962909, -0.000012, 0.000006, -65.299980, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(carnvalst, 0, "P", 100, "Arial", 190, 1, 0xFFFFFFFF, 0x00000000, 1);
    carnvalst = CreateDynamicObject(18667, 385.834503, -1954.531982, 11.962909, -0.000012, 0.000006, -65.499977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(carnvalst, 0, "L", 100, "Arial", 190, 1, 0xFFFFFFFF, 0x00000000, 1);
    carnvalst = CreateDynamicObject(18667, 385.094512, -1955.032714, 11.962909, -0.000011, 0.000009, -47.099956, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(carnvalst, 0, "E", 100, "Arial", 190, 1, 0xFFFFFFFF, 0x00000000, 1);
    carnvalst = CreateDynamicObject(18667, 384.318298, -1955.733154, 11.962909, -0.000011, 0.000009, -46.499977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(carnvalst, 0, "A", 100, "Arial", 190, 1, 0xFFFFFFFF, 0x00000000, 1);
    carnvalst = CreateDynamicObject(18667, 383.769195, -1956.517211, 11.962909, -0.000007, 0.000012, -29.999965, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(carnvalst, 0, "S", 100, "Arial", 190, 1, 0xFFFFFFFF, 0x00000000, 1);
    carnvalst = CreateDynamicObject(18667, 383.262023, -1957.411499, 11.962909, -0.000003, 0.000014, -12.999967, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(carnvalst, 0, "U", 100, "Arial", 190, 1, 0xFFFFFFFF, 0x00000000, 1);
    carnvalst = CreateDynamicObject(18667, 383.036163, -1958.346801, 11.962909, -0.000000, 0.000014, -7.299973, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(carnvalst, 0, "R", 100, "Arial", 190, 1, 0xFFFFFFFF, 0x00000000, 1);
    carnvalst = CreateDynamicObject(18667, 382.871582, -1959.313232, 11.962909, -0.000000, 0.000014, -4.599974, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(carnvalst, 0, "E", 100, "Arial", 190, 1, 0xFFFFFFFF, 0x00000000, 1);
    carnvalst = CreateDynamicObject(18667, 383.083251, -1960.901611, 11.962909, 0.000001, 0.000014, 10.400021, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(carnvalst, 0, "P", 100, "Arial", 190, 1, 0xFFFFFFFF, 0x00000000, 1);
    carnvalst = CreateDynamicObject(18667, 383.218597, -1961.574218, 11.962909, 0.000007, 0.000012, 27.600027, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(carnvalst, 0, "I", 100, "Arial", 190, 1, 0xFFFFFFFF, 0x00000000, 1);
    carnvalst = CreateDynamicObject(18667, 383.542663, -1962.152954, 11.962909, 0.000007, 0.000012, 27.900049, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(carnvalst, 0, "E", 100, "Arial", 190, 1, 0xFFFFFFFF, 0x00000000, 1);
    carnvalst = CreateDynamicObject(18667, 383.987396, -1962.949951, 11.962909, 0.000007, 0.000012, 31.700061, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(carnvalst, 0, "R", 100, "Arial", 190, 1, 0xFFFFFFFF, 0x00000000, 1);
    carnvalst = CreateDynamicObject(16003, 391.165161, -1959.434204, 7.916954, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 1, 6522, "cuntclub_law2", "marinawindow1_256", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 2, 14652, "ab_trukstpa", "CJ_WOOD6", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 3, 14650, "ab_trukstpc", "sa_wood08_128", 0x00000000);
    carnvalst = CreateDynamicObject(16003, 397.195251, -1959.434204, 7.916954, 0.000000, -0.000029, 179.999816, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 1, 6522, "cuntclub_law2", "marinawindow1_256", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 2, 14652, "ab_trukstpa", "CJ_WOOD6", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 3, 14650, "ab_trukstpc", "sa_wood08_128", 0x00000000);
    carnvalst = CreateDynamicObject(16003, 403.275177, -1959.434204, 7.916954, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 1, 6522, "cuntclub_law2", "marinawindow1_256", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 2, 14652, "ab_trukstpa", "CJ_WOOD6", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 3, 14650, "ab_trukstpc", "sa_wood08_128", 0x00000000);
    carnvalst = CreateDynamicObject(2491, 385.838348, -1959.291870, 6.810934, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 18245, "cw_junkyardmachin", "Was_scrpyd_light_yellow", 0x00000000);
    carnvalst = CreateDynamicObject(11674, 397.574645, -1940.127807, 6.801370, 0.000003, 0.000004, 38.499961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 2, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    carnvalst = CreateDynamicObject(6300, 404.965515, -1894.313110, -1.253494, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6351, "rodeo02_law2", "woodboards1", 0x00000000);
    carnvalst = CreateDynamicObject(16003, 413.355255, -1929.922851, 7.916946, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 1, 6522, "cuntclub_law2", "marinawindow1_256", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 2, 14652, "ab_trukstpa", "CJ_WOOD6", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 3, 14650, "ab_trukstpc", "sa_wood08_128", 0x00000000);
    carnvalst = CreateDynamicObject(16003, 413.355255, -1923.382568, 7.916946, 0.000000, 0.000014, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 1, 6522, "cuntclub_law2", "marinawindow1_256", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 2, 14652, "ab_trukstpa", "CJ_WOOD6", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 3, 14650, "ab_trukstpc", "sa_wood08_128", 0x00000000);
    carnvalst = CreateDynamicObject(19357, 406.735809, -1937.057983, 7.944983, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 14811, "lee_strip2_1", "CJ_NEON_HEART", 0x00000000);
    carnvalst = CreateDynamicObject(9244, 397.920257, -1922.910522, 11.983328, 0.000022, 0.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 1, 1426, "break_scaffold", "CJ_BLUE_WOOD", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 3, 6404, "beafron1_law2", "shingledblue_la", 0x00000000);
    carnvalst = CreateDynamicObject(9525, 395.903564, -1931.959350, 11.652013, 0.000000, -0.000014, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 7650, "vgnusedcar", "lightpurple2_32", 0x00000000);
    carnvalst = CreateDynamicObject(19357, 402.185974, -1926.598022, 11.704991, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 4212, "nitewin_lan", "neonwin1", 0x00000000);
    carnvalst = CreateDynamicObject(642, 384.533477, -1946.365478, 8.042090, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 1373, "traincross", "rednwhite", 0x00000000);
    carnvalst = CreateDynamicObject(642, 384.538330, -1940.994873, 8.042090, 0.000000, 0.000007, 102.099967, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 1373, "traincross", "rednwhite", 0x00000000);
    carnvalst = CreateDynamicObject(642, 397.483489, -1959.465942, 9.302085, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 1373, "traincross", "rednwhite", 0x00000000);
    carnvalst = CreateDynamicObject(642, 413.533477, -1923.405517, 9.352094, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 1373, "traincross", "rednwhite", 0x00000000);
    carnvalst = CreateDynamicObject(19426, 404.326202, -1912.859375, 6.773985, 90.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 18882, "hugebowls", "woodpanel1", 0x00000000);
    carnvalst = CreateDynamicObject(19426, 407.816131, -1912.859375, 6.773985, 90.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 18882, "hugebowls", "woodpanel1", 0x00000000);
    carnvalst = CreateDynamicObject(19426, 411.296020, -1912.859375, 6.773985, 90.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 18882, "hugebowls", "woodpanel1", 0x00000000);
    carnvalst = CreateDynamicObject(3440, 413.314575, -1912.861938, 9.098440, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 2381, "cloth_trackies", "trackTrblue", 0x00000000);
    carnvalst = CreateDynamicObject(19426, 423.515838, -1912.859375, 6.773985, 89.999992, -0.000003, -89.999992, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 18882, "hugebowls", "woodpanel1", 0x00000000);
    carnvalst = CreateDynamicObject(19426, 426.995727, -1912.859375, 6.773985, 89.999992, -0.000003, -89.999992, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 18882, "hugebowls", "woodpanel1", 0x00000000);
    carnvalst = CreateDynamicObject(3440, 421.474365, -1912.861938, 9.098440, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 2381, "cloth_trackies", "trackTrblue", 0x00000000);
    carnvalst = CreateDynamicObject(19426, 430.495758, -1912.859375, 6.773985, 89.999992, -0.000003, -89.999992, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 18882, "hugebowls", "woodpanel1", 0x00000000);
    carnvalst = CreateDynamicObject(19426, 433.545745, -1912.839355, 6.783986, 89.999992, -0.000003, -89.999992, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 18882, "hugebowls", "woodpanel1", 0x00000000);
    carnvalst = CreateDynamicObject(19426, 414.796051, -1912.859375, 11.863992, 90.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 2381, "cloth_trackies", "trackTrblue", 0x00000000);
    carnvalst = CreateDynamicObject(19426, 418.286132, -1912.859375, 11.863992, 90.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 2381, "cloth_trackies", "trackTrblue", 0x00000000);
    carnvalst = CreateDynamicObject(19426, 419.996398, -1912.879394, 11.853993, 90.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 2381, "cloth_trackies", "trackTrblue", 0x00000000);
    carnvalst = CreateDynamicObject(18667, 418.244903, -1912.693115, 12.133777, -0.000022, 0.000000, -89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(carnvalst, 0, "SELAMAT", 100, "Arial", 55, 1, 0xFFFFFFFF, 0x00000000, 1);
    carnvalst = CreateDynamicObject(18667, 416.354858, -1912.693115, 12.133777, -0.000022, 0.000000, -89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(carnvalst, 0, "DATANG", 100, "Arial", 55, 1, 0xFFFFFFFF, 0x00000000, 1);
    carnvalst = CreateDynamicObject(18667, 418.324768, -1912.693115, 11.613780, -0.000029, 0.000000, -89.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(carnvalst, 0, "CARNAVAL", 100, "Arial", 55, 1, 0xFFFFFFFF, 0x00000000, 1);
    carnvalst = CreateDynamicObject(18667, 416.284790, -1912.693115, 11.613780, -0.000029, 0.000000, -89.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(carnvalst, 0, "ATHERLIFE", 100, "Arial", 55, 1, 0xFFFFFFFF, 0x00000000, 1);
    carnvalst = CreateDynamicObject(18667, 416.494812, -1913.003295, 11.863778, -0.000014, 0.000000, 90.000045, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(carnvalst, 0, "DATANG", 100, "Arial", 55, 1, 0xFFFFFFFF, 0x00000000, 1);
    carnvalst = CreateDynamicObject(18667, 418.254821, -1913.003295, 11.863778, -0.000014, 0.000000, 90.000045, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(carnvalst, 0, "KEMBALI", 100, "Arial", 55, 1, 0xFFFFFFFF, 0x00000000, 1);
    carnvalst = CreateDynamicObject(19426, 413.895965, -1902.699340, 6.013984, 89.999992, 90.000000, -89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    carnvalst = CreateDynamicObject(19426, 413.895965, -1899.259765, 6.013984, 89.999992, 90.000000, -89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    carnvalst = CreateDynamicObject(19426, 413.895965, -1892.289428, 6.013984, 89.999992, 90.000007, -89.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    carnvalst = CreateDynamicObject(19426, 413.895965, -1895.779296, 6.013984, 89.999992, 90.000007, -89.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    carnvalst = CreateDynamicObject(19426, 414.466125, -1902.699340, 6.013984, 89.999992, 90.000007, -89.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    carnvalst = CreateDynamicObject(19426, 414.466125, -1899.259765, 6.013984, 89.999992, 90.000007, -89.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    carnvalst = CreateDynamicObject(19426, 414.466125, -1892.289428, 6.013984, 89.999992, 90.000022, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    carnvalst = CreateDynamicObject(19426, 414.466125, -1895.779296, 6.013984, 89.999992, 90.000022, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    carnvalst = CreateDynamicObject(970, 387.470550, -1932.999267, 7.327484, 0.000029, 0.000029, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(19426, 415.731445, -1905.119873, 6.013984, 89.999992, 109.471221, -64.471191, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    carnvalst = CreateDynamicObject(19426, 415.682037, -1900.436767, 6.013984, 89.999992, 131.601226, -86.601173, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    carnvalst = CreateDynamicObject(19426, 415.731445, -1896.439331, 6.013984, 89.999992, 131.601226, -86.601173, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    carnvalst = CreateDynamicObject(19426, 415.682037, -1892.396240, 6.013984, 89.999992, 128.226013, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    carnvalst = CreateDynamicObject(970, 383.300567, -1932.999267, 7.327480, 0.000029, 0.000029, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 380.615753, -1973.261230, 7.327486, 0.000007, 0.000007, 89.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(19426, 412.661590, -1892.406982, 6.013984, -89.999992, 149.832534, 104.832527, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    carnvalst = CreateDynamicObject(19426, 412.612274, -1896.459838, 6.013984, -89.999992, 152.058578, 107.058532, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    carnvalst = CreateDynamicObject(19426, 412.661590, -1900.457275, 6.013984, -89.999992, 152.058578, 107.058532, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    carnvalst = CreateDynamicObject(19426, 412.612274, -1905.130615, 6.013984, -89.999992, 152.058578, 107.058532, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    carnvalst = CreateDynamicObject(970, 380.615753, -1969.131469, 7.327486, 0.000007, 0.000007, 89.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 380.615753, -1964.981445, 7.327486, 0.000007, 0.000014, 89.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(19426, 410.181732, -1894.887695, 6.013984, -89.999992, 142.538497, 97.538467, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    carnvalst = CreateDynamicObject(19426, 410.132415, -1898.940551, 6.013984, -89.999992, 143.714263, 98.714195, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    carnvalst = CreateDynamicObject(19426, 410.181732, -1902.937988, 6.013984, -89.999992, 143.714263, 98.714195, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    carnvalst = CreateDynamicObject(19426, 410.132415, -1907.611328, 6.013984, -89.999992, 143.714263, 98.714195, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    carnvalst = CreateDynamicObject(970, 380.615753, -1943.350830, 7.327486, 0.000014, 0.000007, 89.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 380.615753, -1939.221069, 7.327486, 0.000014, 0.000007, 89.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(19426, 418.101959, -1894.817382, 6.013984, 89.999992, 306.066650, -81.066734, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    carnvalst = CreateDynamicObject(19426, 418.151275, -1898.870239, 6.013984, 89.999992, 306.066711, -81.066734, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    carnvalst = CreateDynamicObject(19426, 418.101959, -1902.867675, 6.013984, 89.999992, 306.066711, -81.066734, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    carnvalst = CreateDynamicObject(19426, 418.151275, -1907.541015, 6.013984, 89.999992, 306.066711, -81.066734, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    carnvalst = CreateDynamicObject(970, 380.615753, -1935.071044, 7.327486, 0.000014, 0.000014, 89.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(19426, 433.811309, -1897.140136, 6.013984, 89.999992, 225.000000, -45.000011, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    carnvalst = CreateDynamicObject(970, 382.730834, -1975.316650, 7.327486, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(19426, 433.811309, -1900.089233, 6.013984, 89.999992, 225.000000, -45.000011, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    carnvalst = CreateDynamicObject(19426, 433.811309, -1907.059570, 6.013984, 89.999992, 225.000000, -45.000011, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    carnvalst = CreateDynamicObject(19426, 433.811309, -1903.569702, 6.013984, 89.999992, 225.000000, -45.000011, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    carnvalst = CreateDynamicObject(19426, 433.811309, -1910.499145, 6.013984, 89.999992, 225.000000, -45.000011, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    carnvalst = CreateDynamicObject(970, 386.860687, -1975.316650, 7.327486, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(19426, 432.545928, -1894.229125, 6.013984, 89.999992, 241.533325, -16.533355, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    carnvalst = CreateDynamicObject(19426, 432.595367, -1898.912231, 6.013984, 89.999992, 266.550781, -41.550792, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    carnvalst = CreateDynamicObject(19426, 432.545928, -1902.909667, 6.013984, 89.999992, 266.550781, -41.550792, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    carnvalst = CreateDynamicObject(19426, 432.595367, -1906.952758, 6.013984, 89.999992, 263.025299, -38.025321, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    carnvalst = CreateDynamicObject(19426, 432.545928, -1910.950195, 6.013984, 89.999992, 263.025299, -38.025321, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    carnvalst = CreateDynamicObject(19426, 430.126129, -1908.529052, 6.013984, 89.999992, 440.717041, -35.717163, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    carnvalst = CreateDynamicObject(19426, 430.175445, -1904.531616, 6.013984, 89.999992, 440.717041, -35.717163, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    carnvalst = CreateDynamicObject(19426, 430.126129, -1900.478759, 6.013984, 89.999992, 440.717102, -35.717163, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    carnvalst = CreateDynamicObject(19426, 430.175445, -1896.481323, 6.013984, 89.999992, 440.717102, -35.717163, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    carnvalst = CreateDynamicObject(19426, 430.126129, -1891.807983, 6.013984, 89.999992, 440.717102, -35.717163, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    carnvalst = CreateDynamicObject(970, 391.010742, -1975.316650, 7.327486, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 395.140594, -1975.316650, 7.327486, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 399.280487, -1975.316650, 7.327486, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 403.410339, -1975.316650, 7.327486, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 407.560394, -1975.316650, 7.327486, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 411.690246, -1975.316650, 7.327486, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 415.820739, -1975.316650, 7.327486, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 419.960632, -1975.316650, 7.327486, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 424.090484, -1975.316650, 7.327486, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 428.240539, -1975.316650, 7.327486, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 432.370391, -1975.316650, 7.327486, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 435.270477, -1973.297119, 7.327486, 0.000007, 0.000022, 89.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 435.270477, -1969.166625, 7.327486, 0.000007, 0.000022, 89.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 435.270477, -1965.026733, 7.327486, 0.000007, 0.000022, 89.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 435.270477, -1960.896850, 7.327486, 0.000007, 0.000022, 89.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 435.270477, -1956.746826, 7.327486, 0.000007, 0.000029, 89.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 435.270477, -1952.616943, 7.327486, 0.000007, 0.000029, 89.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 435.270477, -1948.477050, 7.327486, 0.000014, 0.000022, 89.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 435.270477, -1944.346557, 7.327486, 0.000014, 0.000022, 89.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 435.270477, -1940.206665, 7.327486, 0.000014, 0.000022, 89.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 435.270477, -1936.076782, 7.327486, 0.000014, 0.000022, 89.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 435.270477, -1931.926757, 7.327486, 0.000014, 0.000029, 89.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 435.270477, -1927.796875, 7.327486, 0.000014, 0.000029, 89.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 435.270477, -1923.667846, 7.327486, 0.000014, 0.000029, 89.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 435.270477, -1919.527954, 7.327486, 0.000022, 0.000022, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 435.270477, -1915.397460, 7.327486, 0.000022, 0.000022, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 435.270477, -1911.257568, 7.327486, 0.000022, 0.000022, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 435.270477, -1907.127685, 7.327486, 0.000022, 0.000022, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 435.270477, -1902.977661, 7.327486, 0.000022, 0.000029, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 435.270477, -1898.847778, 7.327486, 0.000022, 0.000029, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 435.270477, -1894.698608, 7.327486, 0.000029, 0.000029, 89.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 435.290496, -1892.588745, 7.327486, 0.000029, 0.000029, 89.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 433.200073, -1890.527832, 7.327486, 0.000029, 0.000022, 179.999801, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 429.050903, -1890.527832, 7.327486, 0.000037, 0.000022, 179.999786, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 426.941040, -1890.507812, 7.327486, 0.000037, 0.000022, 179.999786, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 424.760498, -1880.108764, 7.327486, 0.000007, 0.000014, -90.000114, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 424.760498, -1884.257934, 7.327486, 0.000014, 0.000014, -90.000129, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 424.740478, -1888.436889, 7.327486, 0.000022, 0.000014, -90.000152, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 424.760498, -1867.608764, 7.327486, 0.000000, 0.000014, -90.000091, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 424.760498, -1871.757934, 7.327486, 0.000007, 0.000014, -90.000106, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 424.740478, -1875.936889, 7.327486, 0.000014, 0.000014, -90.000129, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 424.770507, -1857.879516, 7.327486, -0.000007, 0.000014, -90.000068, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 424.760498, -1859.308349, 7.327486, 0.000000, 0.000014, -90.000083, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 424.740478, -1863.487304, 7.327486, 0.000007, 0.000014, -90.000106, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 422.710205, -1855.818115, 7.327486, -0.000007, 0.000029, -0.000098, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 412.181152, -1855.808105, 7.327486, -0.000014, 0.000029, -0.000075, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 414.409790, -1855.818115, 7.327486, -0.000007, 0.000029, -0.000091, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 418.588745, -1855.838134, 7.327486, 0.000000, 0.000029, -0.000114, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 380.615753, -1951.661010, 7.327486, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 380.615753, -1947.531250, 7.327486, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 380.605743, -1953.750732, 7.327486, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(18981, 422.015319, -1890.979492, -6.072291, 90.000000, 270.000000, 0.000014, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 19071, "wssections", "wood1", 0x00000000);
    carnvalst = CreateDynamicObject(18981, 434.815399, -1902.968505, -6.072291, 90.000000, 270.000000, -89.999984, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 19071, "wssections", "wood1", 0x00000000);
    carnvalst = CreateDynamicObject(18981, 397.333831, -1892.543945, -6.072291, 90.000000, -82.900009, 0.000014, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 19071, "wssections", "wood1", 0x00000000);
    carnvalst = CreateDynamicObject(18981, 422.015319, -1914.929443, -6.072291, 90.000000, 270.000000, 0.000014, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 19071, "wssections", "wood1", 0x00000000);
    carnvalst = CreateDynamicObject(18981, 397.035491, -1914.929443, -6.072291, 90.000000, 270.000000, 0.000014, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 19071, "wssections", "wood1", 0x00000000);
    carnvalst = CreateDynamicObject(642, 404.323547, -1928.134765, 8.042090, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 1373, "traincross", "rednwhite", 0x00000000);
    carnvalst = CreateDynamicObject(1881, 406.112487, -1946.529541, 10.561845, 54.899810, 205.000106, -90.000076, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    carnvalst = CreateDynamicObject(970, 380.415466, -2002.210571, 7.327486, 0.000014, 0.000007, 89.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 380.415466, -1998.080688, 7.327486, 0.000014, 0.000007, 89.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 380.415466, -1993.930664, 7.327486, 0.000014, 0.000014, 89.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 380.415466, -1989.800781, 7.327486, 0.000014, 0.000014, 89.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 380.415466, -1985.660888, 7.327486, 0.000014, 0.000014, 89.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 380.415466, -1981.531127, 7.327486, 0.000014, 0.000014, 89.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 380.415466, -1977.380981, 7.327486, 0.000014, 0.000022, 89.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 380.415466, -2006.330200, 7.327486, 0.000014, 0.000007, 89.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 380.415466, -2010.509643, 7.327486, 0.000014, 0.000007, 89.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 360.515594, -2001.900268, 7.327486, 0.000029, 0.000007, 89.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 360.515594, -1997.770385, 7.327486, 0.000029, 0.000007, 89.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 360.515594, -1993.620361, 7.327486, 0.000029, 0.000014, 89.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 360.515594, -1989.490478, 7.327486, 0.000029, 0.000014, 89.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 360.515594, -1985.350585, 7.327486, 0.000029, 0.000014, 89.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 360.515594, -1981.220825, 7.327486, 0.000029, 0.000014, 89.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 360.515594, -1977.070678, 7.327486, 0.000029, 0.000022, 89.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 360.515594, -2006.019897, 7.327486, 0.000029, 0.000007, 89.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 360.515594, -2010.199340, 7.327486, 0.000029, 0.000007, 89.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 360.515594, -1964.601196, 7.327486, 0.000037, 0.000007, 89.999855, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 360.515594, -1960.471313, 7.327486, 0.000037, 0.000007, 89.999855, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 360.515594, -1956.321289, 7.327486, 0.000037, 0.000014, 89.999855, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 360.515594, -1952.191406, 7.327486, 0.000037, 0.000014, 89.999855, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 360.515594, -1948.051513, 7.327486, 0.000037, 0.000014, 89.999855, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 360.515594, -1943.921752, 7.327486, 0.000037, 0.000014, 89.999855, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 360.515594, -1939.771606, 7.327486, 0.000037, 0.000022, 89.999855, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 360.515594, -1968.720825, 7.327486, 0.000037, 0.000007, 89.999855, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 360.515594, -1972.900268, 7.327486, 0.000037, 0.000007, 89.999855, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 360.515594, -1935.611694, 7.327486, 0.000037, 0.000014, 89.999855, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 360.515594, -1931.481933, 7.327486, 0.000037, 0.000014, 89.999855, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 360.515594, -1927.331787, 7.327486, 0.000037, 0.000022, 89.999855, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 360.515594, -1914.862304, 7.327486, 0.000045, 0.000007, 89.999832, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 360.515594, -1910.732421, 7.327486, 0.000045, 0.000007, 89.999832, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 360.515594, -1906.582397, 7.327486, 0.000045, 0.000014, 89.999832, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 360.515594, -1902.452514, 7.327486, 0.000045, 0.000014, 89.999832, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 360.515594, -1898.312622, 7.327486, 0.000045, 0.000014, 89.999832, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 360.515594, -1894.182861, 7.327486, 0.000045, 0.000014, 89.999832, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 360.515594, -1890.032714, 7.327486, 0.000045, 0.000022, 89.999832, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 360.515594, -1918.981933, 7.327486, 0.000045, 0.000007, 89.999832, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 360.515594, -1923.161376, 7.327486, 0.000045, 0.000007, 89.999832, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 360.485565, -1886.033325, 7.327486, 0.000060, 0.000014, 89.999786, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(970, 360.485565, -1881.883178, 7.327486, 0.000060, 0.000022, 89.999786, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 6283, "pierb_law2", "pierfenc_LAw2", 0x00000000);
    SetDynamicObjectMaterial(carnvalst, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    carnvalst = CreateDynamicObject(1881, 406.089843, -1946.825927, 10.577776, 54.899810, 155.000106, -90.000076, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    carnvalst = CreateDynamicObject(1881, 406.154785, -1946.700927, 10.532134, 54.899810, 280.000122, -90.000076, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    carnvalst = CreateDynamicObject(1881, 406.083801, -1946.183105, 10.582014, 54.899810, 0.800101, -90.000076, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    carnvalst = CreateDynamicObject(1881, 405.820526, -1946.057861, 10.767045, 54.899810, 90.800102, -90.000076, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    carnvalst = CreateDynamicObject(1881, 405.950653, -1945.937255, 10.675602, 54.899810, 124.099960, -90.000076, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    carnvalst = CreateDynamicObject(1881, 406.227142, -1945.884033, 10.481274, 54.899810, 61.000011, -90.000076, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    carnvalst = CreateDynamicObject(1881, 406.061859, -1945.424560, 10.597440, 54.899810, 0.500006, -90.000076, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    carnvalst = CreateDynamicObject(1881, 406.057586, -1945.060546, 10.600445, 54.899810, 30.500011, -90.000076, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    carnvalst = CreateDynamicObject(1881, 406.045410, -1944.834228, 10.608990, 54.899810, -19.799976, -90.000076, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    carnvalst = CreateDynamicObject(1881, 406.069763, -1944.515625, 10.591889, 54.899810, 0.500024, -90.000076, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    carnvalst = CreateDynamicObject(1881, 405.822265, -1944.228027, 10.765834, 54.899810, 90.500022, -90.000076, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    carnvalst = CreateDynamicObject(1881, 406.075927, -1944.234863, 10.587543, 54.899810, 90.500022, -90.000076, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    carnvalst = CreateDynamicObject(1881, 406.321380, -1944.231933, 10.415049, 54.899810, 90.500022, -90.000076, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    carnvalst = CreateDynamicObject(1881, 406.063476, -1943.635742, 10.596304, 54.899810, 0.500024, -90.000076, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    carnvalst = CreateDynamicObject(1881, 406.046905, -1943.451416, 10.607962, 54.899810, 32.100032, -90.000076, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    carnvalst = CreateDynamicObject(1881, 406.012512, -1943.234130, 10.632104, 54.899810, -7.499971, -90.000076, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    carnvalst = CreateDynamicObject(1881, 406.112487, -1942.608886, 10.561845, 54.899810, 205.000106, -90.000076, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    carnvalst = CreateDynamicObject(1881, 406.089843, -1942.905273, 10.577776, 54.899810, 155.000106, -90.000076, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    carnvalst = CreateDynamicObject(1881, 406.154785, -1942.780273, 10.532134, 54.899810, 280.000122, -90.000076, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(carnvalst, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(3752, 416.429656, -1952.901489, 30.099699, 0.000029, 0.000048, 34.999965, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3752, 420.364379, -1958.520996, 32.396595, 0.000029, 0.000048, 34.999965, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3752, 414.026336, -1949.469238, 24.201297, 0.000029, 0.000048, 34.999965, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3752, 424.310607, -1964.156616, 30.123098, 0.000029, 0.000048, 34.999965, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3752, 414.014923, -1949.453002, 17.021598, 0.000029, 0.000048, 34.999965, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3752, 416.423919, -1952.893310, 11.154397, 0.000029, 0.000048, 34.999965, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3752, 420.375823, -1958.537475, 8.951251, 0.000029, 0.000048, 34.999965, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3752, 424.322113, -1964.173095, 11.115301, 0.000029, 0.000048, 34.999965, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3752, 426.754089, -1967.646240, 17.013797, 0.000029, 0.000048, 34.999965, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3752, 426.765533, -1967.662719, 24.279396, 0.000029, 0.000048, 34.999965, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18878, 420.263671, -1958.554687, 21.994705, -0.000048, 0.000029, -54.999938, 0, 0, -1, 200.00, 200.00); // rangka
    CreateDynamicObject(18877, 420.280822, -1958.579467, 21.949398, 0.000029, 0.000048, 124.999969, 0, 0, -1, 200.00, 200.00); // jari-jari
    CreateDynamicObject(1445, 392.538482, -1966.752929, 9.554689, 0.000000, 0.000014, 0.000001, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1445, 384.834838, -1952.945312, 7.354685, 0.000012, -0.000007, 122.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2593, 395.867004, -1953.344604, 7.649487, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2493, 392.838226, -1950.154541, 8.150083, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2493, 391.828155, -1950.154541, 8.150083, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2494, 392.366210, -1950.129272, 8.158758, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19525, 398.865936, -1967.035400, 7.566779, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11739, 398.242980, -1967.020263, 7.564116, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11739, 397.962982, -1967.290527, 7.564116, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11739, 397.962982, -1966.790405, 7.564116, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2221, 397.112823, -1967.129638, 7.652102, -0.000012, 0.000007, -56.699970, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2864, 396.881103, -1966.699584, 7.551510, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11744, 398.242950, -1967.013305, 7.589636, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11744, 397.962982, -1967.283569, 7.589636, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11744, 397.962982, -1966.783569, 7.589636, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2767, 393.489227, -1966.779785, 7.579136, 0.000000, 0.000014, 4.299999, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2219, 392.687347, -1967.422363, 7.630037, -25.000026, 23.900014, 18.599981, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2768, 393.623474, -1966.770629, 7.628241, 0.000000, -0.000014, 179.999908, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2880, 393.344573, -1966.860229, 7.568126, -14.200009, -80.199996, -0.999996, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2767, 391.561614, -1966.884277, 7.579136, 0.000009, 0.000018, 37.500000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2768, 391.668945, -1966.803222, 7.628241, -0.000007, -0.000019, -146.799987, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2880, 391.484649, -1967.030883, 7.568126, -14.199998, -80.199989, 32.199989, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2769, 392.002319, -1966.792968, 7.584519, -0.000014, 0.000004, -69.899986, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2599, 390.716522, -1953.255737, 7.263163, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19638, 387.888702, -1966.951171, 7.559619, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19636, 385.989685, -1967.011474, 7.563634, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19637, 386.859771, -1967.011474, 7.563634, -0.000003, 0.000014, -16.399997, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3467, 400.171752, -1966.199951, 7.369678, 0.000012, 0.000007, 56.299976, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19587, 386.638366, -1966.808227, 6.865471, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1450, 398.243927, -1949.422851, 7.385046, -0.000000, -0.000014, -175.999877, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(924, 387.039276, -1969.546997, 6.985425, 0.000004, 0.000014, 19.400001, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1357, 388.123840, -1969.742675, 7.100982, -0.000004, 0.000014, -22.299985, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2657, 385.325378, -1959.520507, 7.696342, -0.000007, 0.000000, -89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2655, 385.564910, -1959.750610, 7.699330, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2658, 385.596618, -1959.300903, 7.693539, 0.000000, -0.000007, 179.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2656, 385.828826, -1959.544799, 7.699808, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1522, 400.348358, -1947.067504, 6.783268, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1570, 403.951690, -1950.930541, 8.023335, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1342, 403.389984, -1968.162719, 7.832462, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(6299, 426.249816, -1933.167480, 8.785687, 0.000000, -0.000007, 179.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1256, 385.187988, -1972.819091, 7.437832, -0.000007, 0.000000, -89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1256, 397.717987, -1972.819091, 7.437832, -0.000007, 0.000000, -89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1256, 410.378021, -1972.819091, 7.437832, -0.000007, 0.000000, -89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1223, 388.244415, -1972.794433, 6.701253, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1223, 400.624450, -1972.794433, 6.701253, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1223, 412.944519, -1972.794433, 6.701253, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1361, 382.803527, -1964.644042, 7.572593, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1361, 382.803527, -1951.134033, 7.572593, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2011, 390.080993, -1964.356811, 6.790676, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2010, 395.480957, -1965.306884, 6.770672, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(644, 401.220916, -1965.396850, 7.050677, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2011, 406.120910, -1966.947387, 6.800674, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(644, 411.320892, -1967.026489, 7.050677, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2010, 400.749572, -1953.059570, 6.782313, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2010, 393.969482, -1953.269165, 6.782313, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3811, 395.670532, -1948.872924, 9.936871, -0.000007, 0.000000, -89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3811, 398.690582, -1948.872924, 9.936871, -0.000007, 0.000000, -89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3811, 401.670593, -1948.872924, 9.936871, -0.000007, 0.000000, -89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3811, 387.620635, -1943.782958, 9.936871, 0.000000, -0.000007, 179.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2394, 397.937896, -1950.500244, 8.720985, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2394, 397.677764, -1951.100219, 8.720985, 0.000000, -0.000007, 179.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18639, 396.764282, -1952.334106, 7.651681, 0.599986, -89.099998, 14.299996, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18970, 396.154449, -1953.411254, 8.010599, 0.000000, -60.000019, -179.099868, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18651, 403.201019, -1959.404296, 8.639113, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18648, 391.020965, -1959.404296, 8.639113, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1256, 418.047943, -1972.819091, 7.437832, -0.000007, 0.000000, -89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1256, 408.867950, -1958.969238, 7.437832, -0.000007, 0.000000, -89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1256, 408.867950, -1959.879516, 7.437832, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2714, 404.139099, -1944.800781, 8.808971, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3467, 418.672546, -1941.865112, 7.369678, 0.000011, -0.000000, 133.799972, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2599, 411.966430, -1939.055419, 7.263161, -0.000003, 0.000014, -26.699993, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1445, 413.608642, -1938.302124, 9.384686, 0.000000, 0.000000, 179.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1496, 403.459381, -1922.560302, 6.779607, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1341, 429.365478, -1949.621826, 7.566689, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1342, 423.292083, -1945.545898, 7.802513, 0.000000, 0.000000, 45.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1340, 431.407897, -1956.164306, 7.780690, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1340, 413.503662, -1963.216064, 7.780690, 0.000000, 0.000000, 36.199993, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1361, 422.013427, -1972.843261, 7.512588, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1361, 419.363342, -1930.513183, 7.512588, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1361, 403.233184, -1930.633300, 7.512588, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 407.172332, -1940.693359, 7.432131, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1571, 377.168060, -1981.891723, 8.128858, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1495, 376.508056, -1983.833862, 6.910133, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2132, 378.275695, -1981.597534, 7.006408, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11722, 375.895996, -1981.231445, 8.024873, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11723, 376.022491, -1981.329223, 8.021519, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19346, 375.963928, -1981.722778, 7.942306, 0.000000, 0.000000, 44.099987, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1571, 421.988159, -1919.411132, 8.128858, -0.000007, 0.000000, -89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1495, 421.328155, -1921.353271, 6.910133, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2132, 423.095794, -1919.116943, 7.006408, -0.000007, 0.000000, -89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11722, 420.716094, -1918.750854, 8.024873, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11723, 420.842590, -1918.848632, 8.021519, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19346, 420.784027, -1919.242187, 7.942306, 0.000004, 0.000004, 44.099983, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11722, 392.036987, -1967.282226, 7.709743, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11723, 392.281738, -1967.428955, 7.699143, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1281, 405.054687, -1972.575805, 7.631183, 0.000000, 0.000000, -1.100008, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1432, 384.513519, -1946.330688, 6.906116, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1432, 384.508544, -1941.021728, 6.906116, 0.000000, 0.000007, 102.099967, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1342, 430.568267, -1926.361938, 7.802513, 0.000000, 0.000000, 45.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1341, 430.505493, -1918.961303, 7.566689, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1231, 414.143371, -1904.296508, 9.429318, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(8843, 424.396606, -1901.528808, 6.852005, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1231, 414.143371, -1890.855590, 9.429318, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(8843, 403.846618, -1897.409301, 6.852005, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1256, 431.525512, -1971.729125, 7.437832, -0.000007, 0.000000, 135.000030, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19127, 434.837799, -1975.270874, 7.312407, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19124, 434.837799, -1964.970581, 7.312407, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19125, 434.837799, -1954.760620, 7.312407, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19126, 434.837799, -1945.670654, 7.312407, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19123, 434.837799, -1933.960571, 7.312407, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19122, 434.837799, -1925.690307, 7.312407, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19127, 434.837799, -1913.411010, 7.312407, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19126, 424.037933, -1975.031250, 7.312407, 0.000014, 0.000007, 89.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19123, 412.327850, -1975.031250, 7.312407, 0.000014, 0.000007, 89.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19122, 404.057586, -1975.031250, 7.312407, 0.000014, 0.000007, 89.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19127, 391.778289, -1975.031250, 7.312407, 0.000014, 0.000007, 89.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(16448, 405.653747, -1958.015991, 11.320643, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(16448, 405.653747, -1950.604248, 11.320643, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(16448, 409.443695, -1909.546752, 11.500608, 0.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1340, 414.259490, -1918.083618, 7.931188, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1256, 406.647796, -1917.898315, 7.471828, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1256, 434.457763, -1917.898315, 7.471828, 0.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1256, 434.457763, -1930.358764, 7.471828, 0.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1256, 434.457763, -1948.378662, 7.471828, 0.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1432, 404.303588, -1928.099975, 6.906116, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
}