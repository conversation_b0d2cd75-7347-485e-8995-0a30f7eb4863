RemoveWalkotBuilding(playerid)
{
    RemoveBuildingForPlayer(playerid, 4917, 1145.949, -2037.000, 65.515, 0.250);
    RemoveBuildingForPlayer(playerid, 1280, 1204.489, -2039.800, 68.375, 0.250);
    RemoveBuildingForPlayer(playerid, 1280, 1187.250, -2034.050, 68.375, 0.250);
    RemoveBuildingForPlayer(playerid, 1280, 1159.439, -2039.800, 68.375, 0.250);
    RemoveBuildingForPlayer(playerid, 1280, 1142.199, -2034.050, 68.375, 0.250);
    RemoveBuildingForPlayer(playerid, 1280, 1136.900, -2025.719, 68.375, 0.250);
    RemoveBuildingForPlayer(playerid, 1280, 1136.900, -2012.689, 68.375, 0.250);
    RemoveBuildingForPlayer(playerid, 1280, 1136.900, -2046.479, 68.375, 0.250);
    RemoveBuildingForPlayer(playerid, 1280, 1136.900, -2061.300, 68.375, 0.250);
    RemoveBuildingForPlayer(playerid, 691, 1260.079, -1995.780, 55.460, 0.250);
}

CreateWalkotExt()
{
    new STREAMER_TAG_OBJECT: wlktxtsma;
    wlktxtsma = CreateDynamicObject(4825, 1146.002197, -2036.993164, 65.514022, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10765, "airportgnd_sfse", "desgreengrass", 0x00000000);
    SetDynamicObjectMaterial(wlktxtsma, 1, 3979, "civic01_lan", "sl_concretewall1", 0x00000000);
    SetDynamicObjectMaterial(wlktxtsma, 2, 10041, "archybuild10", "whitedecosfe4", 0x00000000);
    SetDynamicObjectMaterial(wlktxtsma, 3, 10041, "archybuild10", "whitedecosfe4", 0x00000000);
    SetDynamicObjectMaterial(wlktxtsma, 4, 10765, "airportgnd_sfse", "black64", 0x00000000);
    wlktxtsma = CreateDynamicObject(19426, 1123.319702, -2049.143066, 71.466194, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 6052, "law_doontoon", "sf_window_mod1", 0x00000000);
    wlktxtsma = CreateDynamicObject(19325, 1122.158935, -2039.022460, 72.086219, 89.999992, 89.999992, -89.999992, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18889, "forcefields", "glass1", 0x00000000);
    wlktxtsma = CreateDynamicObject(19325, 1122.158935, -2034.902587, 72.086219, 89.999992, 89.999992, -89.999992, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18889, "forcefields", "glass1", 0x00000000);
    wlktxtsma = CreateDynamicObject(1897, 1122.145141, -2039.218139, 70.806625, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    wlktxtsma = CreateDynamicObject(1897, 1122.145141, -2039.218139, 69.546615, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    wlktxtsma = CreateDynamicObject(1897, 1122.145141, -2034.766601, 70.806625, 0.000007, -0.000007, 179.999832, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    wlktxtsma = CreateDynamicObject(1897, 1122.145141, -2034.766601, 69.546615, 0.000007, -0.000007, 179.999832, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    wlktxtsma = CreateDynamicObject(1897, 1122.145141, -2034.766601, 72.976638, 0.000007, -0.000007, 179.999832, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    wlktxtsma = CreateDynamicObject(1897, 1122.155151, -2037.572387, 73.781646, -89.999992, -281.184295, 78.815704, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    wlktxtsma = CreateDynamicObject(1897, 1122.145141, -2039.218139, 72.976638, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    wlktxtsma = CreateDynamicObject(1897, 1122.155151, -2038.832397, 73.781646, -89.999992, -281.184295, 78.815704, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    wlktxtsma = CreateDynamicObject(1897, 1122.155151, -2035.402465, 73.781646, -89.999992, -281.184295, 78.815704, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    wlktxtsma = CreateDynamicObject(1897, 1122.155151, -2038.012817, 72.321693, -89.999992, -101.184310, 78.815704, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    wlktxtsma = CreateDynamicObject(1897, 1122.135131, -2035.882934, 72.321685, -89.999992, -201.624633, 158.375381, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    wlktxtsma = CreateDynamicObject(1897, 1122.145141, -2035.467285, 70.416671, 0.000000, 179.999984, 179.999984, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    wlktxtsma = CreateDynamicObject(1897, 1122.145141, -2035.467285, 72.596656, 0.000000, -0.000007, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    wlktxtsma = CreateDynamicObject(1897, 1122.145141, -2038.218261, 72.596656, 0.000000, -0.000007, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    wlktxtsma = CreateDynamicObject(1897, 1122.145141, -2038.407470, 70.416671, 0.000000, -179.999984, -0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    wlktxtsma = CreateDynamicObject(1897, 1122.155151, -2039.383300, 69.401702, -89.999992, -101.184310, 78.815704, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    wlktxtsma = CreateDynamicObject(1897, 1122.155151, -2034.482788, 69.401702, -89.999992, -101.184310, 78.815704, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    wlktxtsma = CreateDynamicObject(1897, 1122.155151, -2034.462280, 68.991699, -89.999992, -101.184310, 78.815704, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    wlktxtsma = CreateDynamicObject(1897, 1122.155151, -2039.392211, 68.991699, -89.999992, -201.624633, 158.375381, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    wlktxtsma = CreateDynamicObject(1897, 1122.145141, -2037.016723, 73.346641, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1120.558227, -2036.921264, 70.496238, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    wlktxtsma = CreateDynamicObject(1897, 1122.145141, -2038.407470, 70.006660, 0.000000, -179.999984, -0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    wlktxtsma = CreateDynamicObject(1897, 1122.145141, -2035.467285, 70.006675, 0.000000, 179.999984, 179.999984, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    wlktxtsma = CreateDynamicObject(19482, 1122.429321, -2036.922607, 74.156211, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10101, "2notherbuildsfe", "flatdoor01_law", 0x00000000);
    SetDynamicObjectMaterialText(wlktxtsma, 0, "PEMERINTAH KOTA ATHERLIFE", 130, "Calibri", 30, 1, 0xFFFFFFFF, 0x00000000, 1);
    wlktxtsma = CreateDynamicObject(19426, 1123.319702, -2053.573486, 71.466194, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 6052, "law_doontoon", "sf_window_mod1", 0x00000000);
    wlktxtsma = CreateDynamicObject(19426, 1123.319702, -2057.955078, 71.466194, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 6052, "law_doontoon", "sf_window_mod1", 0x00000000);
    wlktxtsma = CreateDynamicObject(19426, 1123.319702, -2061.806396, 71.466194, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 6052, "law_doontoon", "sf_window_mod1", 0x00000000);
    wlktxtsma = CreateDynamicObject(19426, 1123.319702, -2024.917480, 71.466194, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 6052, "law_doontoon", "sf_window_mod1", 0x00000000);
    wlktxtsma = CreateDynamicObject(19426, 1123.319702, -2020.377807, 71.466194, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 6052, "law_doontoon", "sf_window_mod1", 0x00000000);
    wlktxtsma = CreateDynamicObject(19426, 1123.319702, -2015.908203, 71.466194, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 6052, "law_doontoon", "sf_window_mod1", 0x00000000);
    wlktxtsma = CreateDynamicObject(19426, 1123.319702, -2011.368041, 71.466194, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 6052, "law_doontoon", "sf_window_mod1", 0x00000000);
    wlktxtsma = CreateDynamicObject(19426, 1122.619262, -2062.657226, 71.466194, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 9515, "bigboxtemp1", "browntin1", 0x00000000);
    wlktxtsma = CreateDynamicObject(19426, 1122.619262, -2060.976806, 71.466194, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 9515, "bigboxtemp1", "browntin1", 0x00000000);
    wlktxtsma = CreateDynamicObject(19426, 1122.619262, -2058.696044, 71.466194, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 9515, "bigboxtemp1", "browntin1", 0x00000000);
    wlktxtsma = CreateDynamicObject(19426, 1122.619262, -2057.125976, 71.466194, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 9515, "bigboxtemp1", "browntin1", 0x00000000);
    wlktxtsma = CreateDynamicObject(19426, 1122.619262, -2054.264892, 71.466194, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 9515, "bigboxtemp1", "browntin1", 0x00000000);
    wlktxtsma = CreateDynamicObject(19426, 1122.619262, -2052.824218, 71.466194, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 9515, "bigboxtemp1", "browntin1", 0x00000000);
    wlktxtsma = CreateDynamicObject(19426, 1122.619262, -2049.903808, 71.466194, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 9515, "bigboxtemp1", "browntin1", 0x00000000);
    wlktxtsma = CreateDynamicObject(19426, 1122.619262, -2048.323730, 71.466194, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 9515, "bigboxtemp1", "browntin1", 0x00000000);
    wlktxtsma = CreateDynamicObject(19426, 1122.619262, -2025.764526, 71.466194, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 9515, "bigboxtemp1", "browntin1", 0x00000000);
    wlktxtsma = CreateDynamicObject(19426, 1122.619262, -2024.104125, 71.466194, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 9515, "bigboxtemp1", "browntin1", 0x00000000);
    wlktxtsma = CreateDynamicObject(19426, 1122.619262, -2021.154663, 71.466194, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 9515, "bigboxtemp1", "browntin1", 0x00000000);
    wlktxtsma = CreateDynamicObject(19426, 1122.619262, -2019.524169, 71.466194, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 9515, "bigboxtemp1", "browntin1", 0x00000000);
    wlktxtsma = CreateDynamicObject(19426, 1122.619262, -2016.714355, 71.466194, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 9515, "bigboxtemp1", "browntin1", 0x00000000);
    wlktxtsma = CreateDynamicObject(19426, 1122.619262, -2015.083740, 71.466194, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 9515, "bigboxtemp1", "browntin1", 0x00000000);
    wlktxtsma = CreateDynamicObject(19426, 1122.619262, -2012.133789, 71.466194, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 9515, "bigboxtemp1", "browntin1", 0x00000000);
    wlktxtsma = CreateDynamicObject(19426, 1122.619262, -2010.513427, 71.466194, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 9515, "bigboxtemp1", "browntin1", 0x00000000);
    wlktxtsma = CreateDynamicObject(19426, 1125.990356, -2043.783203, 71.466194, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 6052, "law_doontoon", "sf_window_mod1", 0x00000000);
    wlktxtsma = CreateDynamicObject(19426, 1125.990356, -2029.862548, 71.466194, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 6052, "law_doontoon", "sf_window_mod1", 0x00000000);
    wlktxtsma = CreateDynamicObject(18762, 1177.111572, -2036.963623, 68.256202, 89.999992, 89.999992, -89.999992, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_top", 0x00000000);
    wlktxtsma = CreateDynamicObject(19426, 1177.039672, -2036.124389, 69.356178, 0.000001, -0.000029, 164.999816, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 16093, "a51_ext", "ws_whitewall2_top", 0x00000000);
    wlktxtsma = CreateDynamicObject(19426, 1177.037109, -2037.715332, 69.356178, -0.000001, -0.000029, -165.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 16093, "a51_ext", "ws_whitewall2_top", 0x00000000);
    wlktxtsma = CreateDynamicObject(13649, 1178.014404, -2036.975830, 67.516265, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10765, "airportgnd_sfse", "desgreengrass", 0x00000000);
    SetDynamicObjectMaterial(wlktxtsma, 1, 7916, "vegaswaterfall", "newaterfal1_256", 0x00000000);
    wlktxtsma = CreateDynamicObject(19482, 1177.046508, -2037.359008, 70.286186, 0.000003, 0.000020, 14.999999, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterialText(wlktxtsma, 0, "PEMER", 130, "Arial", 30, 1, 0xFF000000, 0x00000000, 1);
    wlktxtsma = CreateDynamicObject(19482, 1177.041870, -2036.488647, 70.286186, -0.000003, 0.000020, -14.999953, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterialText(wlktxtsma, 0, "INTAH", 130, "Arial", 30, 1, 0xFF000000, 0x00000000, 1);
    wlktxtsma = CreateDynamicObject(19482, 1177.054199, -2037.397827, 69.846168, 0.000003, 0.000020, 14.999999, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterialText(wlktxtsma, 0, "KOTA A", 130, "Arial", 30, 1, 0xFF000000, 0x00000000, 1);
    wlktxtsma = CreateDynamicObject(19482, 1177.061767, -2036.420043, 69.846168, -0.000003, 0.000020, -14.900003, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterialText(wlktxtsma, 0, "RIVENA", 130, "Arial", 30, 1, 0xFF000000, 0x00000000, 1);
    wlktxtsma = CreateDynamicObject(13649, 1178.034423, -2036.965820, 67.496253, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 4833, "airprtrunway_las", "greyground256", 0x00000000);
    wlktxtsma = CreateDynamicObject(13649, 1178.034423, -2036.995849, 67.496253, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 4833, "airprtrunway_las", "greyground256", 0x00000000);
    wlktxtsma = CreateDynamicObject(18762, 1177.381713, -2036.938964, 65.961151, 0.000000, 18.100011, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    wlktxtsma = CreateDynamicObject(2714, 1178.067016, -2036.947143, 68.405944, -70.099853, -0.000006, 89.999855, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(wlktxtsma, 0, "Patah tumbuh hilang", 120, "Monotype Corsiva", 30, 1, 0xFFFFFF00, 0x00000000, 1);
    wlktxtsma = CreateDynamicObject(2714, 1178.179931, -2036.947143, 68.365089, -70.099853, -0.000006, 89.999855, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(wlktxtsma, 0, "berganti, takkan Melayu", 120, "Monotype Corsiva", 30, 1, 0xFFFFFF00, 0x00000000, 1);
    wlktxtsma = CreateDynamicObject(2714, 1178.292846, -2036.947143, 68.324256, -70.099853, -0.000006, 89.999855, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(wlktxtsma, 0, "hilang di bumi", 120, "Monotype Corsiva", 30, 1, 0xFFFFFF00, 0x00000000, 1);
    wlktxtsma = CreateDynamicObject(19482, 1126.565917, -2036.909301, 68.256195, 360.000000, 270.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 1975, "texttest", "kb_red", 0x00000000);
    wlktxtsma = CreateDynamicObject(11245, 1117.811035, -2036.537231, 80.513236, -0.000003, -70.399963, 89.999885, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 1975, "texttest", "kb_red", 0x00000000);
    SetDynamicObjectMaterial(wlktxtsma, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    wlktxtsma = CreateDynamicObject(11245, 1117.811035, -2036.497436, 79.193405, -0.000003, -70.399963, 89.999885, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(wlktxtsma, 1, 19962, "samproadsigns", "materialtext1", 0x00000000);
    wlktxtsma = CreateDynamicObject(19482, 1129.365600, -2036.909301, 65.486167, 450.000000, 270.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 1975, "texttest", "kb_red", 0x00000000);
    wlktxtsma = CreateDynamicObject(19482, 1125.615966, -2036.909301, 68.606178, 360.000000, 270.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 1975, "texttest", "kb_red", 0x00000000);
    wlktxtsma = CreateDynamicObject(19482, 1124.656250, -2036.909301, 68.906181, 360.000000, 270.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 1975, "texttest", "kb_red", 0x00000000);
    wlktxtsma = CreateDynamicObject(19482, 1128.414916, -2036.909301, 65.816177, 450.000000, 270.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 1975, "texttest", "kb_red", 0x00000000);
    wlktxtsma = CreateDynamicObject(19482, 1127.464111, -2036.909301, 66.116195, 450.000000, 270.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 1975, "texttest", "kb_red", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1255.760742, -2004.938720, 60.498523, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 3241, "conhooses", "des_greyslats", 0x00000000);
    wlktxtsma = CreateDynamicObject(18764, 1256.654541, -2002.504394, 56.298667, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 2423, "cj_ff_counters", "shop_floor1", 0x00000000);
    wlktxtsma = CreateDynamicObject(18764, 1259.845581, -2002.504394, 56.248672, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 2423, "cj_ff_counters", "shop_floor1", 0x00000000);
    wlktxtsma = CreateDynamicObject(18764, 1259.845581, -1997.503540, 56.248672, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 2423, "cj_ff_counters", "shop_floor1", 0x00000000);
    wlktxtsma = CreateDynamicObject(18764, 1256.654541, -1997.504394, 56.298667, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 2423, "cj_ff_counters", "shop_floor1", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1255.760742, -2002.407226, 60.498523, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 3241, "conhooses", "des_greyslats", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1254.250122, -2003.428588, 60.498523, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 3241, "conhooses", "des_greyslats", 0x00000000);
    wlktxtsma = CreateDynamicObject(18763, 1252.734863, -2003.048950, 59.735271, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1257.331054, -2003.428588, 60.498523, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 3241, "conhooses", "des_greyslats", 0x00000000);
    wlktxtsma = CreateDynamicObject(19383, 1257.322509, -1996.655151, 60.498691, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 3241, "conhooses", "des_greyslats", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1255.760742, -1995.107177, 60.498523, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 3241, "conhooses", "des_greyslats", 0x00000000);
    wlktxtsma = CreateDynamicObject(19383, 1257.322509, -2000.235473, 60.498691, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 3241, "conhooses", "des_greyslats", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1255.810791, -1998.326660, 60.498523, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 3241, "conhooses", "des_greyslats", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1255.810791, -1998.486816, 60.498523, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 3241, "conhooses", "des_greyslats", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1255.810791, -1998.606933, 60.498523, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 3241, "conhooses", "des_greyslats", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1254.250122, -2000.208740, 60.498523, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 3241, "conhooses", "des_greyslats", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1254.250122, -1997.039062, 60.498523, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 3241, "conhooses", "des_greyslats", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1254.260131, -1996.618652, 60.498523, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 3241, "conhooses", "des_greyslats", 0x00000000);
    wlktxtsma = CreateDynamicObject(18764, 1264.815673, -2002.504394, 56.248672, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 2423, "cj_ff_counters", "shop_floor1", 0x00000000);
    wlktxtsma = CreateDynamicObject(18764, 1264.815673, -1997.504760, 56.248672, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 2423, "cj_ff_counters", "shop_floor1", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1265.640747, -2004.938720, 60.498523, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 3241, "conhooses", "des_greyslats", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1265.640747, -2002.407226, 60.498523, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 3241, "conhooses", "des_greyslats", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1264.130126, -2003.428588, 60.498523, 0.000000, -0.000007, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 3241, "conhooses", "des_greyslats", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1267.211059, -2003.428588, 60.498523, 0.000000, -0.000007, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 3241, "conhooses", "des_greyslats", 0x00000000);
    wlktxtsma = CreateDynamicObject(19383, 1264.142578, -1996.655151, 60.498691, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 3241, "conhooses", "des_greyslats", 0x00000000);
    wlktxtsma = CreateDynamicObject(19383, 1264.142578, -2000.235473, 60.498691, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 3241, "conhooses", "des_greyslats", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1265.652587, -1998.326660, 60.498523, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 3241, "conhooses", "des_greyslats", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1265.652587, -1998.486816, 60.498523, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 3241, "conhooses", "des_greyslats", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1265.652587, -1998.606933, 60.498523, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 3241, "conhooses", "des_greyslats", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1267.199829, -2000.208740, 60.498523, 0.000000, -0.000007, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 3241, "conhooses", "des_greyslats", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1267.199829, -1997.039062, 60.498523, 0.000000, -0.000007, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 3241, "conhooses", "des_greyslats", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1267.209838, -1996.618652, 60.498523, 0.000000, -0.000007, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 3241, "conhooses", "des_greyslats", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1265.651123, -1995.107177, 60.498523, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 3241, "conhooses", "des_greyslats", 0x00000000);
    wlktxtsma = CreateDynamicObject(18763, 1252.734863, -2000.109985, 59.735271, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    wlktxtsma = CreateDynamicObject(18763, 1252.734863, -1997.159912, 59.735271, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    wlktxtsma = CreateDynamicObject(970, 1259.512207, -1995.068115, 59.288684, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    wlktxtsma = CreateDynamicObject(970, 1261.921630, -1995.068115, 59.288684, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    wlktxtsma = CreateDynamicObject(18763, 1268.794677, -2003.048950, 59.735271, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    wlktxtsma = CreateDynamicObject(18763, 1268.794677, -2000.058837, 59.735271, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    wlktxtsma = CreateDynamicObject(18763, 1268.794677, -1997.118286, 59.735271, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    wlktxtsma = CreateDynamicObject(18763, 1268.794677, -1997.118286, 58.645267, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1255.780761, -2003.498413, 62.308506, 180.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1255.780761, -2000.317504, 62.308506, 180.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1255.780761, -1997.166503, 62.308506, 180.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1255.780761, -1996.555908, 62.308506, 180.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1265.611816, -2003.498413, 62.308506, 0.000000, -89.999984, -0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1265.611816, -2000.317504, 62.308506, 0.000000, -89.999984, -0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1265.611816, -1997.166503, 62.308506, 0.000000, -89.999984, -0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1265.611816, -1996.555908, 62.308506, 0.000000, -89.999984, -0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1260.841796, -2002.328125, 58.358516, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 3241, "conhooses", "des_greyslats", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1260.841796, -1999.197998, 58.358516, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 3241, "conhooses", "des_greyslats", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1260.851806, -1996.707763, 58.358516, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 3241, "conhooses", "des_greyslats", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1260.851806, -2003.128784, 58.358516, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 3241, "conhooses", "des_greyslats", 0x00000000);
    wlktxtsma = CreateDynamicObject(2960, 1259.321899, -2004.828002, 62.187736, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    wlktxtsma = CreateDynamicObject(2960, 1262.052368, -2004.828002, 62.187736, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    wlktxtsma = CreateDynamicObject(2960, 1262.052368, -1995.217285, 62.187736, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    wlktxtsma = CreateDynamicObject(2960, 1259.660888, -1995.217285, 62.187736, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    wlktxtsma = CreateDynamicObject(2960, 1259.660888, -2001.627929, 62.187736, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    wlktxtsma = CreateDynamicObject(2960, 1261.881835, -2001.627929, 62.187736, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    wlktxtsma = CreateDynamicObject(2960, 1260.931396, -2004.879028, 59.797710, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    wlktxtsma = CreateDynamicObject(18762, 1260.785400, -2004.665649, 58.894958, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 3241, "conhooses", "des_greyslats", 0x00000000);
    wlktxtsma = CreateDynamicObject(2960, 1260.931396, -1995.078247, 59.797710, 0.000000, 90.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    wlktxtsma = CreateDynamicObject(18762, 1260.785400, -1994.864868, 58.894958, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 3241, "conhooses", "des_greyslats", 0x00000000);
    wlktxtsma = CreateDynamicObject(1499, 1257.341796, -2000.967041, 58.738674, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(wlktxtsma, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    wlktxtsma = CreateDynamicObject(1499, 1257.341796, -1997.386962, 58.738674, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(wlktxtsma, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    wlktxtsma = CreateDynamicObject(1499, 1264.142578, -1995.876098, 58.738674, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(wlktxtsma, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    wlktxtsma = CreateDynamicObject(1499, 1264.142578, -1999.445556, 58.738674, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(wlktxtsma, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    wlktxtsma = CreateDynamicObject(8855, 1260.780151, -2025.665405, 58.439975, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1244.195068, -2040.300415, 57.145454, 1.602386, -0.058139, 88.402381, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "grey-20-percent", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1278.234985, -2040.305053, 56.286926, 1.099915, 0.008379, 88.399795, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "grey-20-percent", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1278.319702, -2037.285644, 56.266490, 0.999915, 0.008377, 88.399932, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "grey-20-percent", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1278.235107, -2034.135131, 56.247447, 0.999624, 0.027924, 88.399696, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "grey-20-percent", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1278.319702, -2031.115722, 56.245971, 0.999624, 0.027924, 88.399696, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "grey-20-percent", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1278.234863, -2028.232299, 56.227451, 0.599631, 0.027925, 88.399673, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "grey-20-percent", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1278.319458, -2025.212890, 56.215976, 0.699631, 0.027925, 88.399673, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "grey-20-percent", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1278.234985, -2022.294067, 56.207454, 0.999631, 0.027925, 88.399673, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "grey-20-percent", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1278.319458, -2019.274658, 56.185981, 0.799631, 0.027925, 88.399673, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "grey-20-percent", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1278.234741, -2016.391235, 56.187458, 0.999639, 0.027925, 88.399650, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "grey-20-percent", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1278.319213, -2013.371826, 56.175983, 1.199639, 0.027925, 88.399650, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "grey-20-percent", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1244.281127, -2037.281005, 57.148471, 1.502387, -0.058109, 88.401466, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "grey-20-percent", 0x00000000);
    wlktxtsma = CreateDynamicObject(19483, 1256.376953, -2005.036743, 60.189628, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterialText(wlktxtsma, 0, " TOILET", 100, "Arial", 25, 1, 0xFFE5E4E2, 0x00000000, 0);
    wlktxtsma = CreateDynamicObject(19483, 1266.266235, -2005.036743, 60.189628, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterialText(wlktxtsma, 0, " TOILET", 100, "Arial", 25, 1, 0xFFE5E4E2, 0x00000000, 0);
    wlktxtsma = CreateDynamicObject(19565, 1255.569091, -2005.076782, 61.216873, 360.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    wlktxtsma = CreateDynamicObject(19565, 1265.550048, -2005.076782, 61.216873, 0.000000, 90.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    wlktxtsma = CreateDynamicObject(19565, 1255.859375, -2004.906616, 61.436855, 450.000000, 540.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    wlktxtsma = CreateDynamicObject(19565, 1255.829345, -2004.906616, 61.436855, 450.000000, 540.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    wlktxtsma = CreateDynamicObject(19565, 1265.840332, -2004.906616, 61.436855, 89.999992, 540.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    wlktxtsma = CreateDynamicObject(19565, 1265.810302, -2004.906616, 61.436855, 89.999992, 540.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1244.195922, -2034.130371, 57.136714, 1.602095, -0.038582, 88.402053, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "grey-20-percent", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1244.280151, -2031.110961, 57.138740, 1.602095, -0.038582, 88.402053, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "grey-20-percent", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1244.195678, -2028.227539, 57.147022, 1.702103, -0.038582, 88.402015, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "grey-20-percent", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1244.280029, -2025.208129, 57.139049, 1.502102, -0.038582, 88.402015, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "grey-20-percent", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1244.195312, -2022.289184, 57.137393, 1.602102, -0.038582, 88.402015, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "grey-20-percent", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1244.280029, -2019.269775, 57.139415, 1.702103, -0.038582, 88.402015, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "grey-20-percent", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1244.195312, -2016.386352, 57.137699, 1.602110, -0.038580, 88.402015, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "grey-20-percent", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1244.279907, -2013.366943, 57.139724, 1.802109, -0.038580, 88.402015, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "grey-20-percent", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1263.096313, -2034.311279, 56.590145, 1.605296, -0.180395, 88.404365, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "grey-20-percent", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1263.090332, -2031.289428, 56.602321, 1.405297, -0.180395, 88.404365, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "grey-20-percent", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1263.095947, -2028.408203, 56.590744, 1.605306, -0.180392, 88.404365, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "grey-20-percent", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1258.417602, -2034.312011, 56.734535, 1.902201, -0.046953, 88.402389, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "grey-20-percent", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1258.441894, -2031.291137, 56.738990, 2.102201, -0.046953, 88.402389, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "grey-20-percent", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1258.417114, -2028.409301, 56.734844, 1.902209, -0.046952, 88.402366, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "grey-20-percent", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1258.441284, -2025.388305, 56.729305, 1.802207, -0.046952, 88.402366, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "grey-20-percent", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1258.406860, -2022.470947, 56.725543, 1.902207, -0.046953, 88.402366, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "grey-20-percent", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1258.441406, -2019.449951, 56.729667, 1.702208, -0.046953, 88.402366, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "grey-20-percent", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1258.396972, -2016.567382, 56.736175, 1.902217, -0.046952, 88.402343, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "grey-20-percent", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1263.099121, -2025.386962, 56.602512, 1.605304, -0.180392, 88.404365, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "grey-20-percent", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1263.096313, -2022.470214, 56.611484, 1.605306, -0.180392, 88.404365, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "grey-20-percent", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1263.090698, -2019.448608, 56.593666, 1.405305, -0.180392, 88.404365, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "grey-20-percent", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1263.096069, -2016.567260, 56.602073, 1.605312, -0.180392, 88.404335, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "grey-20-percent", 0x00000000);
    wlktxtsma = CreateDynamicObject(18765, 1262.353515, -1999.364135, 56.178409, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    wlktxtsma = CreateDynamicObject(18765, 1259.101684, -1999.374145, 56.178409, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1252.820678, -1997.237915, 62.308506, 180.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1252.820678, -2000.367553, 62.308506, 180.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1252.820678, -2003.097656, 62.308506, 180.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1268.621948, -2003.097656, 62.308506, 180.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1268.621948, -1999.967773, 62.308506, 180.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    wlktxtsma = CreateDynamicObject(19353, 1268.621948, -1997.157226, 62.308506, 180.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    wlktxtsma = CreateDynamicObject(19482, 1122.409301, -2036.912597, 74.156211, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10101, "2notherbuildsfe", "flatdoor01_law", 0x00000000);
    SetDynamicObjectMaterialText(wlktxtsma, 0, "PEMERINTAH KOTA ATHERLIFE", 130, "Calibri", 30, 1, 0xFFFFFFA6, 0x00000000, 1);
    wlktxtsma = CreateDynamicObject(2047, 1169.698120, -2025.441528, 78.293174, -0.000001, -0.000010, 89.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "red-4", 0x00000000);
    wlktxtsma = CreateDynamicObject(2047, 1169.698120, -2025.441528, 78.293174, 0.000057, 0.000010, -90.000259, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "red-4", 0x00000000);
    wlktxtsma = CreateDynamicObject(2047, 1169.698120, -2025.441528, 77.473167, 0.000003, -0.000015, 89.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wlktxtsma = CreateDynamicObject(2047, 1169.698120, -2025.441528, 77.473167, 0.000052, 0.000015, -90.000244, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wlktxtsma = CreateDynamicObject(2047, 1170.036499, -2019.422241, 78.293174, 0.000024, -0.000000, 44.999885, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "red-4", 0x00000000);
    wlktxtsma = CreateDynamicObject(2047, 1170.036499, -2019.422241, 78.293174, 0.000031, 0.000000, -135.000228, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "red-4", 0x00000000);
    wlktxtsma = CreateDynamicObject(2047, 1170.036499, -2019.422241, 77.473167, 0.000029, -0.000005, 44.999885, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wlktxtsma = CreateDynamicObject(2047, 1170.036499, -2019.422241, 77.473167, 0.000026, 0.000005, -135.000183, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wlktxtsma = CreateDynamicObject(2047, 1170.036499, -2013.011230, 78.293174, 0.000029, 0.000005, 44.999885, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "red-4", 0x00000000);
    wlktxtsma = CreateDynamicObject(2047, 1170.036499, -2013.011230, 78.293174, 0.000026, -0.000005, -135.000228, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "red-4", 0x00000000);
    wlktxtsma = CreateDynamicObject(2047, 1170.036499, -2013.011230, 77.473167, 0.000034, 0.000000, 44.999885, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wlktxtsma = CreateDynamicObject(2047, 1170.036499, -2013.011230, 77.473167, 0.000021, -0.000000, -135.000183, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wlktxtsma = CreateDynamicObject(2047, 1169.698120, -2059.259277, 78.293174, 0.000013, -0.000010, 89.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "red-4", 0x00000000);
    wlktxtsma = CreateDynamicObject(2047, 1169.698120, -2059.259277, 78.293174, 0.000042, 0.000010, -90.000213, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "red-4", 0x00000000);
    wlktxtsma = CreateDynamicObject(2047, 1169.698120, -2059.259277, 77.473167, 0.000018, -0.000015, 89.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wlktxtsma = CreateDynamicObject(2047, 1169.698120, -2059.259277, 77.473167, 0.000037, 0.000015, -90.000198, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wlktxtsma = CreateDynamicObject(2047, 1170.036499, -2053.239990, 78.293174, 0.000035, 0.000010, 44.999885, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "red-4", 0x00000000);
    wlktxtsma = CreateDynamicObject(2047, 1170.036499, -2053.239990, 78.293174, 0.000020, -0.000010, -135.000228, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "red-4", 0x00000000);
    wlktxtsma = CreateDynamicObject(2047, 1170.036499, -2053.239990, 77.473167, 0.000040, 0.000005, 44.999885, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wlktxtsma = CreateDynamicObject(2047, 1170.036499, -2053.239990, 77.473167, 0.000015, -0.000005, -135.000183, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wlktxtsma = CreateDynamicObject(2047, 1170.036499, -2046.828979, 78.293174, 0.000040, 0.000016, 44.999885, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "red-4", 0x00000000);
    wlktxtsma = CreateDynamicObject(2047, 1170.036499, -2046.828979, 78.293174, 0.000015, -0.000016, -135.000228, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "red-4", 0x00000000);
    wlktxtsma = CreateDynamicObject(2047, 1170.036499, -2046.828979, 77.473167, 0.000045, 0.000011, 44.999885, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wlktxtsma = CreateDynamicObject(2047, 1170.036499, -2046.828979, 77.473167, 0.000010, -0.000011, -135.000183, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wlktxtsma = CreateDynamicObject(3281, 1116.250122, -2002.216918, 90.924316, 0.000090, 0.000000, 89.999725, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wlktxtsma = CreateDynamicObject(3281, 1116.250122, -2002.216918, 91.954269, 0.000090, 0.000000, 89.999725, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "red-4", 0x00000000);
    wlktxtsma = CreateDynamicObject(1307, 1116.224365, -2004.056274, 92.573867, 0.000000, 180.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 1675, "wshxrefhse", "greygreensubuild_128", 0x00000000);
    SetDynamicObjectMaterial(wlktxtsma, 1, 1675, "wshxrefhse", "greygreensubuild_128", 0x00000000);
    SetDynamicObjectMaterial(wlktxtsma, 2, 1675, "wshxrefhse", "greygreensubuild_128", 0x00000000);
    wlktxtsma = CreateDynamicObject(3281, 1116.160034, -2068.059570, 90.924316, 0.000105, 0.000000, 89.999679, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wlktxtsma = CreateDynamicObject(3281, 1116.160034, -2068.059570, 91.954269, 0.000105, 0.000000, 89.999679, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "red-4", 0x00000000);
    wlktxtsma = CreateDynamicObject(1307, 1116.134277, -2069.898925, 92.573867, 0.000000, 180.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 1675, "wshxrefhse", "greygreensubuild_128", 0x00000000);
    SetDynamicObjectMaterial(wlktxtsma, 1, 1675, "wshxrefhse", "greygreensubuild_128", 0x00000000);
    SetDynamicObjectMaterial(wlktxtsma, 2, 1675, "wshxrefhse", "greygreensubuild_128", 0x00000000);
    wlktxtsma = CreateDynamicObject(2047, 1257.352172, -2047.163208, 69.056900, 0.000064, -0.000006, 54.999805, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "red-4", 0x00000000);
    wlktxtsma = CreateDynamicObject(2047, 1257.352172, -2047.163208, 69.056900, -0.000008, 0.000006, -125.000091, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 18646, "matcolours", "red-4", 0x00000000);
    wlktxtsma = CreateDynamicObject(2047, 1257.352172, -2047.163208, 68.236892, 0.000069, -0.000011, 54.999805, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wlktxtsma = CreateDynamicObject(2047, 1257.352172, -2047.163208, 68.236892, -0.000013, 0.000011, -125.000091, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(660, 1126.706298, -2045.259277, 65.246200, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(660, 1126.706298, -2029.399902, 65.246200, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(861, 1178.037719, -2035.218750, 67.406219, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(861, 1178.037719, -2038.678833, 67.406219, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(859, 1179.103271, -2038.295898, 67.836219, 0.000003, 0.000006, 37.399986, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(859, 1179.343994, -2035.844970, 67.836219, 0.000003, 0.000006, 37.399986, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19325, 1258.043701, -1998.430664, 62.204059, 180.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19325, 1262.154052, -1998.430664, 62.204059, 180.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19325, 1260.543457, -2002.740844, 62.204059, 180.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1290, 1260.750610, -2015.767944, 64.236137, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1290, 1260.750610, -2035.708618, 64.236137, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2525, 1266.626831, -2000.411254, 58.738674, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2525, 1266.626831, -1996.551025, 58.738674, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2525, 1254.835083, -1996.551025, 58.738674, 0.000000, 0.000000, 450.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2525, 1254.835083, -2000.652099, 58.738674, 0.000000, 0.000000, 450.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11726, 1260.750732, -2004.611083, 60.317157, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11726, 1255.359008, -2004.301147, 61.727153, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11726, 1265.698242, -2004.301147, 61.727153, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11726, 1260.750732, -1994.921630, 60.317157, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1893, 1262.432739, -2001.635009, 62.392642, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1893, 1258.402343, -2001.635009, 62.392642, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1893, 1258.402343, -1995.214477, 62.392642, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1893, 1262.242797, -1995.214477, 62.392642, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2518, 1260.206665, -1996.856933, 58.748672, 0.000000, 0.000000, 630.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2518, 1261.437500, -1997.917846, 58.748672, 0.000000, 0.000000, 450.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2518, 1261.437500, -1999.788696, 58.748672, 0.000000, 0.000000, 450.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2518, 1261.437500, -2001.318359, 58.748672, 0.000000, 0.000000, 450.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2518, 1261.437500, -2003.108032, 58.748672, 0.000000, 0.000000, 450.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2518, 1260.206665, -1998.788330, 58.748672, 0.000000, 0.000000, 630.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2518, 1260.206665, -2000.269287, 58.748672, 0.000000, 0.000000, 630.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2518, 1260.206665, -2002.019653, 58.748672, 0.000000, 0.000000, 630.000000, 0, 0, -1, 200.00, 200.00);
    CreateDynamicObject(982, 1238.511718, -2056.819335, 59.467697, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(982, 1238.511718, -2016.838500, 59.467697, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 1238.427368, -2036.346801, 59.398078, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 1238.427368, -2038.506103, 59.398078, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 1238.427368, -2039.505371, 59.398078, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 1238.427368, -2037.535034, 59.398078, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 1238.427368, -2035.315917, 59.398078, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 1238.427368, -2034.365478, 59.398078, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(982, 1197.522827, -2072.260742, 68.617660, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(982, 1171.863281, -2072.260742, 68.617660, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(982, 1146.234985, -2072.260742, 68.617660, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(982, 1135.795654, -2072.188720, 68.617660, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(982, 1197.522827, -2001.750366, 68.617660, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(982, 1171.863281, -2001.750366, 68.617660, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(982, 1146.234985, -2001.750366, 68.617660, 0.000022, 0.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(982, 1135.795654, -2001.678344, 68.617660, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(16101, 1169.668457, -2026.410888, 67.871360, 0.000010, -0.000031, -0.000312, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(16101, 1169.330078, -2020.086669, 67.871360, 0.000000, -0.000005, -45.000293, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(16101, 1169.330078, -2013.675659, 67.871360, -0.000005, -0.000000, -45.000293, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(16101, 1169.668457, -2060.228759, 67.871360, 0.000010, -0.000016, -0.000312, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(16101, 1169.330078, -2053.904541, 67.871360, -0.000010, 0.000005, -45.000293, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(16101, 1169.330078, -2047.493408, 67.871360, -0.000016, 0.000010, -45.000293, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(16101, 1256.771728, -2047.940429, 58.635089, 0.000006, 0.000034, -35.000053, 0, 0, -1, 200.00, 200.00); 

    //Bridge
    wlktxtsma = CreateDynamicObject(19980, 2236.271240, 567.051818, 17.199735, 0.000000, 0.000000, -25.700000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(wlktxtsma, 3, "Welcome\nto\nLas Venturas", 90, "Arial", 35, 1, 0xFFFFFFFF, 0xFF004F00, 1);
    wlktxtsma = CreateDynamicObject(19980, 2224.214111, 315.627929, 17.199735, 0.000000, 0.000000, 154.300003, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(wlktxtsma, 3, "Welcome\nto\nRed County", 90, "Arial", 35, 1, 0xFFFFFFFF, 0xFF004F00, 1);
    wlktxtsma = CreateDynamicObject(1881, 2250.779052, 320.236022, 24.600725, 0.000000, 205.000000, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wlktxtsma = CreateDynamicObject(1881, 2250.779052, 320.532409, 24.628431, 0.000000, 155.000000, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wlktxtsma = CreateDynamicObject(1881, 2250.779052, 320.407409, 24.549053, 0.000000, 280.000000, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wlktxtsma = CreateDynamicObject(1881, 2250.779052, 319.889831, 24.635797, 0.000000, 0.800000, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wlktxtsma = CreateDynamicObject(1881, 2250.779052, 319.764343, 24.957590, 0.000000, 90.800003, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wlktxtsma = CreateDynamicObject(1881, 2250.779052, 319.643737, 24.798559, 0.000000, 124.099861, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wlktxtsma = CreateDynamicObject(1881, 2250.779052, 319.590515, 24.460601, 0.000000, 60.999908, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wlktxtsma = CreateDynamicObject(1881, 2250.779052, 319.131042, 24.662626, 0.000000, 0.499904, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wlktxtsma = CreateDynamicObject(1881, 2250.779052, 318.767272, 24.667852, 0.000000, 30.499910, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wlktxtsma = CreateDynamicObject(1881, 2250.779052, 318.540710, 24.682712, 0.000000, -19.800077, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wlktxtsma = CreateDynamicObject(1881, 2250.779052, 318.222351, 24.652973, 0.000000, 0.499923, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wlktxtsma = CreateDynamicObject(1881, 2250.779052, 317.934753, 24.955482, 0.000000, 90.499923, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wlktxtsma = CreateDynamicObject(1881, 2250.779052, 317.941589, 24.645414, 0.000000, 90.499923, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wlktxtsma = CreateDynamicObject(1881, 2250.779052, 317.938659, 24.345430, 0.000000, 90.499923, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wlktxtsma = CreateDynamicObject(1881, 2250.779052, 317.342468, 24.660652, 0.000000, 0.499923, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wlktxtsma = CreateDynamicObject(1881, 2250.779052, 317.157897, 24.680925, 0.000000, 32.099929, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wlktxtsma = CreateDynamicObject(1881, 2250.779052, 316.941101, 24.722911, 0.000000, -7.500072, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wlktxtsma = CreateDynamicObject(1881, 2250.779052, 316.315612, 24.600725, 0.000000, 205.000000, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wlktxtsma = CreateDynamicObject(1881, 2250.779052, 316.611999, 24.628431, 0.000000, 155.000000, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wlktxtsma = CreateDynamicObject(1881, 2250.779052, 316.486999, 24.549053, 0.000000, 280.000000, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wlktxtsma, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(18802, 2231.312988, 239.929931, 15.667461, -0.799999, 0.299999, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18450, 2230.657226, 368.704254, 17.728342, -0.799999, 0.000000, 90.299995, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18450, 2230.239013, 448.593383, 17.727342, -0.799999, 0.000000, 90.299995, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18450, 2229.818359, 528.411560, 17.726343, -0.799999, 0.000000, 90.299995, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18450, 2231.077880, 288.865417, 17.729341, -0.799999, 0.000000, 90.299995, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18796, 2229.601806, 587.982910, 16.361721, -0.799999, 6.100002, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18802, 2229.629150, 612.398498, 11.549915, -0.100000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3502, 2235.445312, 567.910095, 13.303544, 270.000000, 180.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3502, 2235.855712, 488.249908, 13.303544, -89.999992, 270.000000, 89.999992, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3502, 2224.084228, 488.249908, 13.303544, -89.999992, 270.000000, 89.999992, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3502, 2235.855712, 488.249908, 4.173536, 89.999992, -215.152191, 35.152233, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19966, 2237.415527, 623.182312, 9.636595, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19965, 2236.391113, 504.260314, 18.074047, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19965, 2224.128906, 385.746032, 17.888807, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19966, 2223.696044, 229.340927, 13.669310, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19963, 2224.598632, 288.656066, 18.122509, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19963, 2235.887695, 583.889526, 17.817726, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19987, 2237.560546, 268.715545, 18.271324, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19987, 2223.201660, 579.885131, 17.950687, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3502, 2223.673828, 567.910095, 13.303544, 270.000000, 180.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3502, 2224.084228, 488.249908, 4.173536, 89.999992, -215.152191, 35.152233, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3502, 2235.855712, 488.249908, -4.926456, 89.999992, -125.152198, -54.847763, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3502, 2224.084228, 488.249908, -4.926456, 89.999992, -125.152198, -54.847763, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3502, 2235.855712, 488.249908, -14.046449, -89.999992, 270.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3502, 2224.084228, 488.249908, -14.046449, -89.999992, 270.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3502, 2235.855712, 488.249908, -23.176458, 89.999992, -125.152198, -54.847763, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3502, 2224.084228, 488.249908, -23.176458, 89.999992, -125.152198, -54.847763, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3502, 2235.855712, 488.249908, -32.276451, 89.999992, -109.092391, -70.907562, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3502, 2224.084228, 488.249908, -32.276451, 89.999992, -109.092391, -70.907562, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3502, 2235.855712, 488.249908, -41.356620, -89.999992, 270.000000, 89.999969, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3502, 2224.084228, 488.249908, -41.356620, -89.999992, 270.000000, 89.999969, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3502, 2235.855712, 488.249908, -50.486625, 89.999992, -109.092391, -70.907562, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3502, 2224.084228, 488.249908, -50.486625, 89.999992, -109.092391, -70.907562, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3502, 2235.855712, 488.249908, -59.586620, 89.999992, -99.804092, -80.195846, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3502, 2224.084228, 488.249908, -59.586620, 89.999992, -99.804092, -80.195846, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3502, 2236.196044, 408.309509, 13.303546, -89.999992, 270.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3502, 2224.424560, 408.309509, 13.303546, -89.999992, 270.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3502, 2236.196044, 408.309509, 4.173536, 89.999992, -125.152198, -54.847763, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3502, 2224.424560, 408.309509, 4.173536, 89.999992, -125.152198, -54.847763, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3502, 2236.196044, 408.309509, -4.926456, 89.999992, -109.092391, -70.907562, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3502, 2224.424560, 408.309509, -4.926456, 89.999992, -109.092391, -70.907562, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3502, 2236.746582, 328.199859, 13.303546, -89.999992, 270.000000, 89.999969, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3502, 2224.424560, 408.309509, -14.046449, -89.999992, 270.000000, 89.999969, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3502, 2224.975097, 328.199859, 13.303546, -89.999992, 270.000000, 89.999969, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3502, 2236.746582, 328.199859, 4.173536, 89.999992, -109.092391, -70.907562, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3502, 2224.975097, 328.199859, 4.173536, 89.999992, -109.092391, -70.907562, 0, 0, -1, 200.00, 200.00);
}