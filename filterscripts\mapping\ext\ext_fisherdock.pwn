CreateFisherDockExt()
{
    static fishdocktxt;
    // fishdocktxt = CreateDynamicObject(18981, 2883.791503, -299.964447, 1.722791, 0.000000, 90.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(fishdocktxt, 0, 16640, "a51", "pavegrey128", 0x00000000);
    // fishdocktxt = CreateDynamicObject(18981, 2883.791503, -324.964508, 1.722791, 0.000000, 90.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(fishdocktxt, 0, 16640, "a51", "pavegrey128", 0x00000000);
    // fishdocktxt = CreateDynamicObject(18981, 2908.791259, -299.964447, 1.722791, 0.000000, 90.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(fishdocktxt, 0, 16640, "a51", "pavegrey128", 0x00000000);
    // fishdocktxt = CreateDynamicObject(18981, 2908.791259, -324.964508, 1.722791, 0.000000, 90.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(fishdocktxt, 0, 16640, "a51", "pavegrey128", 0x00000000);
    // fishdocktxt = CreateDynamicObject(18981, 2883.791503, -331.933898, 1.732791, 0.000000, 90.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(fishdocktxt, 0, 16640, "a51", "pavegrey128", 0x00000000);
    // fishdocktxt = CreateDynamicObject(18981, 2908.791259, -331.933898, 1.732791, 0.000000, 90.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(fishdocktxt, 0, 16640, "a51", "pavegrey128", 0x00000000);
    // fishdocktxt = CreateDynamicObject(18981, 2883.791503, -287.974273, -11.227215, 0.000000, 180.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(fishdocktxt, 0, 16640, "a51", "Metalox64", 0x00000000);
    // fishdocktxt = CreateDynamicObject(18981, 2908.792480, -287.974273, -11.227215, 0.000000, 180.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(fishdocktxt, 0, 16640, "a51", "Metalox64", 0x00000000);
    // fishdocktxt = CreateDynamicObject(18981, 2920.802246, -300.954406, -11.227215, 0.000000, 180.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(fishdocktxt, 0, 16640, "a51", "Metalox64", 0x00000000);
    // fishdocktxt = CreateDynamicObject(18981, 2920.802246, -325.954162, -11.227215, 0.000000, 180.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(fishdocktxt, 0, 16640, "a51", "Metalox64", 0x00000000);
    // fishdocktxt = CreateDynamicObject(18981, 2920.782226, -331.934356, -11.227215, 0.000000, 180.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(fishdocktxt, 0, 16640, "a51", "Metalox64", 0x00000000);
    // fishdocktxt = CreateDynamicObject(18981, 2907.782714, -343.924224, -11.227215, 0.000000, 180.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(fishdocktxt, 0, 16640, "a51", "Metalox64", 0x00000000);
    // fishdocktxt = CreateDynamicObject(18981, 2882.779296, -343.924224, -11.227215, 0.000000, 180.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(fishdocktxt, 0, 16640, "a51", "Metalox64", 0x00000000);
    // fishdocktxt = CreateDynamicObject(19481, 2890.615722, -301.907714, 9.442078, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterialText(fishdocktxt, 0, "FISH DEPOT", 120, "Arial", 25, 1, 0xFFFFFFFF, 0x00000000, 1);
    // fishdocktxt = CreateDynamicObject(19481, 2890.615722, -330.098114, 9.442078, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterialText(fishdocktxt, 0, "FISH DEPOT", 120, "Arial", 25, 1, 0xFFFFFFFF, 0x00000000, 1);
    // fishdocktxt = CreateDynamicObject(19481, 2903.846679, -287.757934, 6.372077, 0.000000, 0.000000, 450.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterialText(fishdocktxt, 0, "ATHERLIFE FISHER", 120, "Arial", 30, 1, 0xFFFFFFFF, 0x00000000, 1);
    // fishdocktxt = CreateDynamicObject(19481, 2903.846679, -344.508239, 6.372077, 0.000000, 0.000000, 630.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterialText(fishdocktxt, 0, "ATHERLIFE FISHER", 120, "Arial", 30, 1, 0xFFFFFFFF, 0x00000000, 1);
    // fishdocktxt = CreateDynamicObject(19481, 2918.147460, -330.088134, 9.442078, -0.000007, 0.000000, 0.000007, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterialText(fishdocktxt, 0, "FISH DEPOT", 120, "Arial", 25, 1, 0xFFFFFFFF, 0x00000000, 1);
    // fishdocktxt = CreateDynamicObject(19481, 2918.147460, -301.887695, 9.442078, -0.000007, 0.000000, 0.000007, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterialText(fishdocktxt, 0, "FISH DEPOT", 120, "Arial", 25, 1, 0xFFFFFFFF, 0x00000000, 1);
    // /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    // /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    // /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    // CreateDynamicObject(12990, 2894.958984, -260.264465, 1.222756, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(12990, 2919.332275, -260.254455, 1.242756, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(10831, 2904.428955, -301.828918, 7.060726, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(10831, 2904.428955, -330.048187, 7.060726, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(3577, 2882.871337, -292.488464, 2.962594, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(3577, 2886.781005, -292.488464, 2.962594, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(3576, 2883.352783, -310.906402, 3.705650, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(3576, 2887.212646, -310.906402, 3.705650, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(3576, 2887.212646, -316.676452, 3.705650, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(3576, 2882.891845, -316.676452, 3.705650, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(3578, 2881.402832, -343.601867, 2.998576, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(3578, 2881.402832, -288.252044, 2.998576, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(3578, 2876.502441, -294.232086, 2.998576, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(3578, 2876.502441, -337.482299, 2.998576, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(8659, 2921.041015, -329.378387, 1.475528, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(8659, 2921.041015, -302.538177, 1.475528, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(3577, 2882.871337, -340.548278, 2.962594, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(3577, 2887.241699, -340.548278, 2.962594, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 

    fishdocktxt = CreateDynamicObject(18765, 174.630035, -1780.974731, 0.953368, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(fishdocktxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    fishdocktxt = CreateDynamicObject(18765, 174.630035, -1790.974975, 0.953368, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(fishdocktxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    fishdocktxt = CreateDynamicObject(18765, 167.010116, -1780.974731, 0.943368, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(fishdocktxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    fishdocktxt = CreateDynamicObject(18765, 167.010116, -1790.964965, 0.943368, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(fishdocktxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    fishdocktxt = CreateDynamicObject(18765, 167.010116, -1800.923828, 0.943368, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(fishdocktxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    fishdocktxt = CreateDynamicObject(18765, 167.010116, -1810.001953, 0.943368, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(fishdocktxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    fishdocktxt = CreateDynamicObject(18765, 174.660171, -1810.001953, 0.953368, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(fishdocktxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    fishdocktxt = CreateDynamicObject(18765, 174.660171, -1800.361694, 0.953368, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(fishdocktxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    fishdocktxt = CreateDynamicObject(3578, 179.965820, -1781.087890, 3.535545, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(fishdocktxt, 0, 10370, "alleys_sfs", "ws_asphalt", 0x00000000);
    fishdocktxt = CreateDynamicObject(3578, 179.965820, -1791.367309, 3.535545, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(fishdocktxt, 0, 10370, "alleys_sfs", "ws_asphalt", 0x00000000);
    fishdocktxt = CreateDynamicObject(3578, 179.965820, -1801.657348, 3.535545, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(fishdocktxt, 0, 10370, "alleys_sfs", "ws_asphalt", 0x00000000);
    fishdocktxt = CreateDynamicObject(3578, 179.965820, -1809.888793, 3.535545, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(fishdocktxt, 0, 10370, "alleys_sfs", "ws_asphalt", 0x00000000);
    fishdocktxt = CreateDynamicObject(3578, 175.055862, -1815.358764, 3.515545, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(fishdocktxt, 0, 10370, "alleys_sfs", "ws_asphalt", 0x00000000);
    fishdocktxt = CreateDynamicObject(3578, 167.145950, -1815.358764, 3.515545, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(fishdocktxt, 0, 10370, "alleys_sfs", "ws_asphalt", 0x00000000);
    fishdocktxt = CreateDynamicObject(18765, 157.446945, -1780.974609, 0.225054, 0.000000, -8.499825, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(fishdocktxt, 0, 10370, "alleys_sfs", "ws_asphalt", 0x00000000);
    fishdocktxt = CreateDynamicObject(18765, 157.446945, -1790.993774, 0.225054, 0.000000, -8.499825, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(fishdocktxt, 0, 10370, "alleys_sfs", "ws_asphalt", 0x00000000);
    fishdocktxt = CreateDynamicObject(3578, 161.735824, -1810.458984, 3.475545, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(fishdocktxt, 0, 10370, "alleys_sfs", "ws_asphalt", 0x00000000);
    fishdocktxt = CreateDynamicObject(3578, 161.755813, -1801.138305, 3.465545, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(fishdocktxt, 0, 10370, "alleys_sfs", "ws_asphalt", 0x00000000);
    fishdocktxt = CreateDynamicObject(19992, 147.852096, -1815.919799, 3.293184, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(fishdocktxt, 2, "DANGER!\n\nBEWARE\nOF\nSHARKS", 100, "Arial", 45, 1, 0xFF000000, 0xFFFFFF00, 1);
    fishdocktxt = CreateDynamicObject(19992, 147.502136, -1831.160400, 3.263184, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(fishdocktxt, 2, "DANGER!\n\nBEWARE\nOF\nSHARKS", 100, "Arial", 45, 1, 0xFF000000, 0xFFFFFF00, 1);
    fishdocktxt = CreateDynamicObject(19992, 147.322174, -1845.340209, 3.283184, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(fishdocktxt, 2, "DANGER!\n\nBEWARE\nOF\nSHARKS", 100, "Arial", 45, 1, 0xFF000000, 0xFFFFFF00, 1);
    fishdocktxt = CreateDynamicObject(19992, 147.322174, -1858.510131, 3.303184, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(fishdocktxt, 2, "DANGER!\n\nBEWARE\nOF\nSHARKS", 100, "Arial", 45, 1, 0xFF000000, 0xFFFFFF00, 1);
    fishdocktxt = CreateDynamicObject(19992, 147.852096, -1789.419677, 3.283184, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(fishdocktxt, 2, "DANGER!\n\nBEWARE\nOF\nSHARKS", 100, "Arial", 45, 1, 0xFF000000, 0xFFFFFF00, 1);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(3361, 142.917526, -1813.444458, 1.256021, 0.000000, 0.000000, 179.399887, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3406, 136.961349, -1813.377685, -1.776737, 0.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11495, 121.058204, -1813.410766, 0.183263, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11495, 110.968162, -1825.300781, 0.183263, 0.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11495, 104.288169, -1801.511352, 0.183263, 0.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11495, 99.088256, -1813.410766, 0.183263, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11495, 99.068161, -1829.031372, 0.183263, 0.000015, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11495, 110.968162, -1847.300292, 0.183263, 0.000000, 0.000000, 720.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11495, 99.038154, -1844.481689, 0.183263, 0.000015, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11495, 77.078239, -1813.410766, 0.183263, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11495, 85.698196, -1801.511352, 0.183263, 0.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11495, 110.968162, -1869.289428, 0.183263, 0.000000, 0.000000, 720.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11495, 99.078163, -1860.572631, 0.183263, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11495, 77.088157, -1860.572631, 0.183266, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11495, 66.988159, -1872.452636, 0.183267, 0.000007, 0.000000, 359.999969, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11495, 88.968154, -1874.224121, 0.183267, 0.000007, 0.000000, 359.999969, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11495, 77.078186, -1884.333007, 0.183268, 0.000007, 0.000000, 449.999969, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11495, 88.978157, -1872.434570, 0.173267, 0.000007, 0.000000, 359.999969, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11495, 77.328018, -1829.031372, 0.193268, 0.000015, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11495, 77.048202, -1844.481689, 0.183268, 0.000015, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19353, 178.007965, -1776.293457, 1.715751, 0.000015, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19353, 179.518020, -1777.973632, 1.715751, 0.000000, -0.000015, 179.999908, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19353, 178.007965, -1779.483154, 1.715751, 0.000015, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19353, 179.518020, -1784.383178, 1.715751, 0.000000, -0.000022, 179.999862, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19353, 179.518020, -1781.183105, 1.715751, 0.000000, -0.000030, 179.999816, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19353, 178.007965, -1782.692626, 1.715751, 0.000030, 0.000000, 89.999908, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19353, 178.007965, -1785.892700, 1.715751, 0.000022, 0.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19353, 179.518020, -1787.592651, 1.715751, 0.000000, -0.000038, 179.999771, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19353, 178.007965, -1789.102172, 1.715751, 0.000038, 0.000000, 89.999885, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19353, 179.518020, -1790.804199, 1.715751, 0.000000, -0.000030, 179.999816, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19353, 178.007965, -1792.313720, 1.715751, 0.000030, 0.000000, 89.999908, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19353, 179.518020, -1794.013671, 1.715751, 0.000000, -0.000045, 179.999725, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19353, 178.007965, -1795.523193, 1.715751, 0.000045, 0.000000, 89.999862, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19353, 174.797561, -1776.293457, 1.715751, 0.000022, 0.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19353, 174.797561, -1779.483154, 1.715751, 0.000022, 0.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19353, 174.797561, -1782.692626, 1.715751, 0.000038, 0.000000, 89.999885, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19353, 174.797561, -1785.892700, 1.715751, 0.000030, 0.000000, 89.999908, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19353, 174.797561, -1789.102172, 1.715751, 0.000045, 0.000000, 89.999862, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19353, 174.797561, -1792.313720, 1.715751, 0.000038, 0.000000, 89.999885, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19353, 174.797561, -1795.523193, 1.715751, 0.000053, 0.000000, 89.999839, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19353, 179.518020, -1797.222167, 1.715751, 0.000000, -0.000038, 179.999771, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19353, 178.007965, -1798.731689, 1.715751, 0.000038, 0.000000, 89.999885, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19353, 179.518020, -1803.631713, 1.715751, 0.000000, -0.000045, 179.999725, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19353, 179.518020, -1800.431640, 1.715751, 0.000000, -0.000053, 179.999679, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19353, 178.007965, -1801.941162, 1.715751, 0.000053, 0.000000, 89.999839, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19353, 178.007965, -1805.141235, 1.715751, 0.000045, 0.000000, 89.999862, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19353, 179.518020, -1806.841186, 1.715751, 0.000000, -0.000061, 179.999633, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19353, 178.007965, -1808.350708, 1.715751, 0.000061, 0.000000, 89.999816, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19353, 179.518020, -1810.052734, 1.715751, 0.000000, -0.000053, 179.999679, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19353, 178.007965, -1811.562255, 1.715751, 0.000053, 0.000000, 89.999839, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19353, 179.518020, -1813.262207, 1.715751, 0.000000, -0.000068, 179.999588, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19353, 178.007965, -1814.771728, 1.715751, 0.000068, 0.000000, 89.999794, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19353, 174.797561, -1798.731689, 1.715751, 0.000045, 0.000000, 89.999862, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19353, 174.797561, -1801.941162, 1.715751, 0.000061, 0.000000, 89.999816, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19353, 174.797561, -1805.141235, 1.715751, 0.000053, 0.000000, 89.999839, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19353, 174.797561, -1808.350708, 1.715751, 0.000068, 0.000000, 89.999794, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19353, 174.797561, -1811.562255, 1.715751, 0.000061, 0.000000, 89.999816, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19353, 174.797561, -1814.771728, 1.715751, 0.000076, 0.000000, 89.999771, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19979, 161.764694, -1802.373901, 3.056931, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19979, 148.234786, -1802.373901, 3.236931, 0.000000, 0.000000, 450.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1608, 81.785629, -1772.751220, -17.459852, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1608, 81.785629, -1917.521362, -32.049858, 0.000000, 0.000000, 225.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1608, -85.097335, -1852.991333, -0.870842, 0.000000, 0.000000, -45.700012, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3458, 178.063949, -1795.591186, 5.722768, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3361, 150.926849, -1813.540771, 1.296021, 0.000000, 0.000000, 359.399902, 0, 0, -1, 200.00, 200.00); 

    //fish factory LV
    fishdocktxt = CreateDynamicObject(18981, 1056.575927, 2303.782958, -1.754531, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(fishdocktxt, 0, 16640, "a51", "a51_wall1", 0x00000000);
    fishdocktxt = CreateDynamicObject(18766, 1094.633056, 2303.782470, 8.252851, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(fishdocktxt, 0, 16640, "a51", "a51_wall1", 0x00000000);
    fishdocktxt = CreateDynamicObject(18766, 1027.493530, 2342.742675, 8.252851, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(fishdocktxt, 0, 16640, "a51", "a51_wall1", 0x00000000);
    fishdocktxt = CreateDynamicObject(18766, 1027.463500, 2338.001953, 8.222850, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(fishdocktxt, 0, 16640, "a51", "a51_wall1", 0x00000000);
    fishdocktxt = CreateDynamicObject(2789, 1058.508300, 2303.258300, 10.013240, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(fishdocktxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(fishdocktxt, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    fishdocktxt = CreateDynamicObject(18667, 1057.551269, 2303.135009, 10.701507, 0.000015, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(fishdocktxt, 0, "FISH", 100, "Arial", 100, 1, 0xFFFFFFFF, 0x00000000, 1);
    fishdocktxt = CreateDynamicObject(18667, 1059.451538, 2303.145019, 10.441502, 0.000015, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(fishdocktxt, 0, "FACTORY", 100, "Arial", 60, 1, 0xFFFFFFFF, 0x00000000, 1);
    fishdocktxt = CreateDynamicObject(18766, 1097.522338, 2303.772460, 8.262851, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(fishdocktxt, 0, 16640, "a51", "a51_wall1", 0x00000000);
    fishdocktxt = CreateDynamicObject(19445, 1092.594970, 2329.361083, 8.078248, 0.000007, -0.000022, 179.999832, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(fishdocktxt, 0, 7650, "vgnusedcar", "lightyellow2_32", 0x00000000);
    fishdocktxt = CreateDynamicObject(19445, 1092.594970, 2319.802978, 8.078248, 0.000007, -0.000022, 179.999832, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(fishdocktxt, 0, 7650, "vgnusedcar", "lightyellow2_32", 0x00000000);
    fishdocktxt = CreateDynamicObject(19445, 1099.874145, 2329.361083, 8.078248, 0.000007, -0.000022, 179.999832, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(fishdocktxt, 0, 7650, "vgnusedcar", "lightyellow2_32", 0x00000000);
    fishdocktxt = CreateDynamicObject(19445, 1099.874145, 2319.802978, 8.078248, 0.000007, -0.000022, 179.999832, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(fishdocktxt, 0, 7650, "vgnusedcar", "lightyellow2_32", 0x00000000);
    fishdocktxt = CreateDynamicObject(2789, 1096.208862, 2329.521484, 9.733237, 270.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(fishdocktxt, 0, "TRUCK\nLINE UP", 90, "Arial", 60, 1, 0xFFFFFF00, 0x00000000, 1);
    fishdocktxt = CreateDynamicObject(2789, 1096.208862, 2318.250732, 9.723237, 270.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(fishdocktxt, 0, "TRUCK\nLINE UP", 90, "Arial", 60, 1, 0xFFFFFF00, 0x00000000, 1);
    fishdocktxt = CreateDynamicObject(18765, 1064.577636, 2343.287353, 8.019493, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(fishdocktxt, 0, 19597, "lsbeachside", "tiles7-128x128", 0x00000000);
    fishdocktxt = CreateDynamicObject(19445, 1064.583129, 2338.222900, 8.078248, 0.000007, -0.000022, 269.999816, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(fishdocktxt, 0, 7650, "vgnusedcar", "lightyellow2_32", 0x00000000);
    fishdocktxt = CreateDynamicObject(19445, 1064.583129, 2337.932617, 8.078248, 0.000007, -0.000022, 269.999816, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(fishdocktxt, 0, 7650, "vgnusedcar", "lightyellow2_32", 0x00000000);
    fishdocktxt = CreateDynamicObject(2789, 1064.548583, 2335.041748, 9.723237, 270.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(fishdocktxt, 0, "KEEP\nCLEAR", 90, "Arial", 60, 1, 0xFFFFFF00, 0x00000000, 1);
    fishdocktxt = CreateDynamicObject(18667, 1061.504516, 2338.788818, 10.522898, 360.000000, 90.000000, 450.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(fishdocktxt, 0, "LOADING BAY", 130, "Arial", 50, 1, 0xFFFFFF00, 0x00000000, 1);
    fishdocktxt = CreateDynamicObject(18667, 1064.594116, 2338.788818, 10.522898, 360.000000, 90.000000, 450.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(fishdocktxt, 0, "LOADING BAY", 130, "Arial", 50, 1, 0xFFFFFF00, 0x00000000, 1);
    fishdocktxt = CreateDynamicObject(18667, 1068.113769, 2338.788818, 10.522898, 360.000000, 90.000000, 450.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(fishdocktxt, 0, "LOADING BAY", 130, "Arial", 50, 1, 0xFFFFFF00, 0x00000000, 1);
    fishdocktxt = CreateDynamicObject(19445, 1096.274536, 2334.240478, 8.078248, 0.000007, -0.000022, 269.999816, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(fishdocktxt, 0, 7650, "vgnusedcar", "lightyellow2_32", 0x00000000);
    fishdocktxt = CreateDynamicObject(2789, 1083.478515, 2374.878662, 14.643245, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(fishdocktxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(fishdocktxt, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    fishdocktxt = CreateDynamicObject(2789, 1083.488769, 2374.961914, 15.613247, 360.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(fishdocktxt, 0, "FISH PRICES", 130, "Arial", 50, 1, 0xFFFFFFFF, 0x00000000, 1);
    fishdocktxt = CreateDynamicObject(18667, 1083.601806, 2373.803710, 14.891492, 0.000015, 0.000000, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(fishdocktxt, 0, "Ocean:", 100, "Arial", 60, 1, 0xFFFFFFFF, 0x00000000, 1);
    fishdocktxt = CreateDynamicObject(18667, 1083.601806, 2373.803710, 14.381484, 0.000015, -0.000007, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(fishdocktxt, 0, "Lake:", 100, "Arial", 60, 1, 0xFFFFFFFF, 0x00000000, 1);
    fishdocktxt = CreateDynamicObject(18667, 1083.601806, 2373.803710, 13.831482, 0.000015, -0.000015, 179.999862, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(fishdocktxt, 0, "River:", 100, "Arial", 60, 1, 0xFFFFFFFF, 0x00000000, 1);
    fishdocktxt = CreateDynamicObject(18667, 1083.601806, 2376.424560, 14.891492, 0.000015, 0.000000, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(fishdocktxt, 0, "/ lb", 100, "Arial", 60, 1, 0xFFFFFFFF, 0x00000000, 1);
    fishdocktxt = CreateDynamicObject(18667, 1083.601806, 2376.294433, 14.391486, 0.000015, 0.000000, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(fishdocktxt, 0, "/ lb", 100, "Arial", 60, 1, 0xFFFFFFFF, 0x00000000, 1);
    fishdocktxt = CreateDynamicObject(18667, 1083.601806, 2376.254394, 13.831485, 0.000015, 0.000000, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(fishdocktxt, 0, "/ lb", 100, "Arial", 60, 1, 0xFFFFFFFF, 0x00000000, 1);
    fishdocktxt = CreateDynamicObject(19980, 1091.683105, 2366.287353, 8.756594, 0.000000, 0.000000, 65.699989, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(fishdocktxt, 3, 10765, "airportgnd_sfse", "black64", 0x00000000);
    fishdocktxt = CreateDynamicObject(18667, 1091.709716, 2366.282470, 11.391498, 0.000015, 0.000000, 155.700439, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(fishdocktxt, 0, "Barisan dimulai dari sini\njangan menyelak dan antri\nyang rapi dan tertib", 140, "Arial", 30, 1, 0xFFFFFFFF, 0x00000000, 1);
    fishdocktxt = CreateDynamicObject(2789, 1095.004150, 2382.883789, 10.013240, 0.000007, -0.000007, 179.999786, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(fishdocktxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(fishdocktxt, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    fishdocktxt = CreateDynamicObject(18667, 1095.961181, 2383.006835, 10.701507, 0.000007, -0.000007, -90.000038, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(fishdocktxt, 0, "FISH", 100, "Arial", 100, 1, 0xFFFFFFFF, 0x00000000, 1);
    fishdocktxt = CreateDynamicObject(18667, 1094.060791, 2382.997070, 10.441502, 0.000007, -0.000007, -90.000038, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(fishdocktxt, 0, "FACTORY", 100, "Arial", 60, 1, 0xFFFFFFFF, 0x00000000, 1);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(4640, 1079.923461, 2305.027832, 11.442279, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(966, 1077.436279, 2303.845458, 9.612073, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(966, 1082.426635, 2303.845458, 9.612073, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(968, 1077.494750, 2303.863769, 10.505252, 0.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(968, 1082.335205, 2303.823730, 10.505252, 0.000000, 0.000000, 720.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(984, 1050.548583, 2303.779541, 11.338592, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(984, 1062.467773, 2303.769531, 11.338592, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(984, 1096.129028, 2303.769531, 11.338592, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 1069.647338, 2303.810546, 10.300121, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 1090.068359, 2302.889648, 10.340120, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19445, 1048.874755, 2304.367431, 8.088249, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19445, 1048.874755, 2308.347167, 8.088249, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19445, 1048.874755, 2312.348144, 8.088249, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19445, 1048.874755, 2316.327880, 8.088249, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19445, 1048.874755, 2320.468750, 8.088249, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19445, 1048.874755, 2324.448486, 8.088249, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19445, 1048.874755, 2328.449462, 8.088249, 0.000015, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19445, 1048.874755, 2332.429199, 8.088249, 0.000015, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19445, 1064.623168, 2338.423095, 8.788248, 0.000007, -0.000022, 269.999816, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19445, 1069.352905, 2343.314208, 8.788248, 0.000007, -0.000022, 359.999816, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19445, 1059.892456, 2343.314208, 8.788248, 0.000007, -0.000022, 359.999816, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(8843, 1086.067993, 2317.691650, 9.857311, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(8843, 1073.658081, 2317.691650, 9.827310, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19967, 1078.129150, 2303.074707, 9.707613, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19957, 1081.641357, 2303.035156, 9.719528, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(982, 1079.906372, 2358.691406, 10.493562, 0.000000, 0.000000, 45.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(982, 1099.273925, 2339.323974, 10.493562, 0.000000, 0.000000, 45.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(970, 1086.798095, 2358.259033, 10.350411, 0.000038, 0.000000, 89.999885, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2206, 1084.807128, 2358.575439, 9.756342, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(970, 1086.798095, 2362.428710, 10.350411, 0.000038, 0.000000, 89.999885, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(970, 1085.277954, 2361.188720, 10.350411, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1514, 1084.679199, 2356.931884, 10.820314, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2059, 1084.779052, 2357.732666, 10.700324, 0.000000, 0.000000, 46.300006, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(984, 1027.547485, 2339.619140, 11.338592, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(984, 1027.547485, 2352.417480, 11.338592, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(982, 1117.494384, 2358.253906, 10.483562, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(982, 1117.484375, 2369.882568, 10.463562, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(982, 1094.793823, 2382.843750, 10.463562, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 1103.382690, 2335.795166, 10.406269, 0.000000, 0.000000, 45.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 1096.163452, 2343.014160, 10.406269, 0.000000, 0.000000, 45.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 1117.085815, 2357.317871, 10.406269, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 1117.085815, 2372.468505, 10.406269, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 1101.285644, 2382.448242, 10.406269, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 1089.204956, 2382.448242, 10.406269, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 1094.895019, 2302.958007, 10.466267, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 1064.715087, 2302.958007, 10.456267, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 1049.304931, 2302.958007, 10.456267, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 1090.628662, 2348.380371, 10.340120, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1294, 1115.847412, 2364.479003, 14.139430, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1294, 1115.847412, 2382.421386, 14.139430, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1294, 1115.847412, 2345.879882, 14.139430, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1294, 1095.507934, 2381.130126, 14.139430, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(970, 1085.287963, 2363.738037, 10.350411, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(970, 1088.250854, 2359.548583, 10.350411, 0.000083, 0.000000, 89.999748, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(970, 1088.250854, 2363.718261, 10.350411, 0.000083, 0.000000, 89.999748, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(970, 1087.329101, 2365.821289, 10.350411, 0.000045, 0.000000, 179.999862, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(970, 1089.730834, 2358.259033, 10.350411, 0.000068, 0.000000, 89.999794, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(970, 1089.730834, 2362.428710, 10.350411, 0.000068, 0.000000, 89.999794, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(970, 1088.869262, 2356.180664, 10.350417, 0.000045, -0.000015, 179.999771, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(970, 1088.979858, 2365.811279, 10.350411, 0.000045, 0.000000, 179.999862, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 1090.114990, 2360.517578, 10.446269, 0.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19445, 1112.633422, 2335.997802, 8.088249, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19445, 1112.633422, 2339.977539, 8.088249, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19445, 1112.633422, 2343.978515, 8.088249, 0.000015, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19445, 1112.633422, 2347.958251, 8.088249, 0.000015, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19445, 1112.633422, 2352.099121, 8.088249, 0.000015, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19445, 1112.633422, 2356.078857, 8.088249, 0.000015, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19445, 1112.633422, 2360.079833, 8.088249, 0.000022, 0.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19445, 1112.633422, 2364.059570, 8.088249, 0.000022, 0.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
}