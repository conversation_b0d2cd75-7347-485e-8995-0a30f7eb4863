RemoveHospitalBuilding(playerid)
{
    //pilbox ls v1
    //RemoveBuildingForPlayer(playerid, 618, 1177.729, -1315.660, 13.296, 0.250);
    //RemoveBuildingForPlayer(playerid, 617, 1178.599, -1332.069, 12.890, 0.250);

    RemoveBuildingForPlayer(playerid, 4718, 1760.160, -1127.270, 43.664, 0.250);
    RemoveBuildingForPlayer(playerid, 4719, 1760.160, -1127.270, 43.664, 0.250);
    RemoveBuildingForPlayer(playerid, 4748, 1760.160, -1127.270, 43.664, 0.250);
}

CreateHospitalExt()
{
    new STREAMER_TAG_OBJECT:HSPTLXTA;
    
    //pillbox LS v1
    // HSPTLXTA = CreateDynamicObject(18766, 1178.511840, -1323.767822, 18.563503, 89.999992, 179.999984, -89.999984, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 5708, "hospital_lawn", "hosp03b_law", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18766, 1183.511108, -1323.767822, 18.563503, 89.999992, 179.999984, -89.999984, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 5708, "hospital_lawn", "hosp03b_law", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18766, 1180.459960, -1310.423339, 18.563503, 89.999992, 203.231811, -81.231788, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 5708, "hospital_lawn", "hosp03b_law", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18766, 1183.511108, -1333.769042, 18.563503, 89.999992, 179.999984, -89.999984, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 5708, "hospital_lawn", "hosp03b_law", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(19380, 1183.487548, -1337.795532, 18.008945, 0.000000, 90.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 1675, "wshxrefhse", "greygreensubuild_128", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18762, 1183.843139, -1340.478149, 12.660700, 90.000000, 0.000000, 38.899990, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 6284, "bev_law2", "lasjmposh4", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(19380, 1183.487548, -1328.156127, 18.008945, 0.000000, 90.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 1675, "wshxrefhse", "greygreensubuild_128", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(19380, 1183.487548, -1323.486694, 18.009944, 0.000000, 90.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 1675, "wshxrefhse", "greygreensubuild_128", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(19380, 1181.728149, -1317.370117, 18.008941, 0.000003, 90.000007, 31.999994, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 1675, "wshxrefhse", "greygreensubuild_128", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18766, 1186.121826, -1323.767822, 18.563503, 89.999992, 179.999984, -89.999969, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 5708, "hospital_lawn", "hosp03b_law", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18766, 1183.851562, -1315.850341, 18.563503, 89.999992, 203.231811, -81.231788, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 5708, "hospital_lawn", "hosp03b_law", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18766, 1186.121826, -1333.769042, 18.563503, 89.999992, 179.999984, -89.999969, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 5708, "hospital_lawn", "hosp03b_law", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18762, 1180.721679, -1336.610473, 12.630700, 89.300018, 0.000000, 38.899990, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 6284, "bev_law2", "lasjmposh4", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(19380, 1178.677734, -1341.658569, 12.908287, 0.000000, 90.000000, 38.999996, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 4829, "airport_las", "Grass_128HV", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(19380, 1172.617919, -1334.174682, 12.908287, 0.000000, 90.000000, 38.999996, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 4829, "airport_las", "Grass_128HV", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18762, 1178.103027, -1335.508056, 12.700701, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 6284, "bev_law2", "lasjmposh4", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18762, 1173.103637, -1335.508056, 12.700701, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 6284, "bev_law2", "lasjmposh4", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18762, 1178.113037, -1312.235961, 12.700700, 0.000000, 90.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 6284, "bev_law2", "lasjmposh4", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18762, 1173.113647, -1312.235961, 12.700700, 0.000000, 90.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 6284, "bev_law2", "lasjmposh4", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18762, 1183.745483, -1306.958618, 12.742659, -89.100128, 38.899925, 1.399914, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 6284, "bev_law2", "lasjmposh4", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18762, 1181.010009, -1310.439941, 12.709723, -88.856872, 61.511745, 22.617864, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 6284, "bev_law2", "lasjmposh4", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(19380, 1177.482788, -1305.715209, 13.008286, 0.000000, 90.000000, -37.799999, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 4829, "airport_las", "Grass_128HV", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(19380, 1173.094116, -1311.030517, 12.938283, 0.799998, 90.000000, -73.399993, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 4829, "airport_las", "Grass_128HV", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18766, 1180.313354, -1315.897827, 18.563503, 89.999992, 179.999984, -89.999984, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 5708, "hospital_lawn", "hosp03b_law", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18766, 1177.453369, -1309.857299, 18.563503, 89.999992, 179.999984, -89.999984, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 5708, "hospital_lawn", "hosp03b_law", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18766, 1177.453369, -1319.847534, 18.563503, 89.999992, 179.999984, -89.999984, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 5708, "hospital_lawn", "hosp03b_law", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18766, 1177.453369, -1329.837646, 18.563503, 89.999992, 179.999984, -89.999984, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 5708, "hospital_lawn", "hosp03b_law", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18766, 1180.313354, -1333.777465, 18.563503, 89.999992, 179.999984, -89.999984, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 5708, "hospital_lawn", "hosp03b_law", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18766, 1177.453369, -1333.777709, 18.563503, 89.999992, 179.999984, -89.999984, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 5708, "hospital_lawn", "hosp03b_law", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18766, 1183.521118, -1337.469848, 18.563503, 89.999992, 179.999984, -89.999969, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 5708, "hospital_lawn", "hosp03b_law", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18766, 1186.131835, -1337.459838, 18.563503, 89.999992, 180.000000, -89.999961, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 5708, "hospital_lawn", "hosp03b_law", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18766, 1180.313354, -1337.468261, 18.563503, 89.999992, 179.999984, -89.999969, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 5708, "hospital_lawn", "hosp03b_law", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18766, 1177.453369, -1337.468505, 18.563503, 89.999992, 179.999984, -89.999969, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 5708, "hospital_lawn", "hosp03b_law", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18766, 1172.453491, -1309.857299, 18.563503, 89.999992, 179.999984, -89.999969, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 5708, "hospital_lawn", "hosp03b_law", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18766, 1172.453491, -1319.847534, 18.563503, 89.999992, 179.999984, -89.999969, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 5708, "hospital_lawn", "hosp03b_law", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18766, 1172.453491, -1329.837646, 18.563503, 89.999992, 179.999984, -89.999969, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 5708, "hospital_lawn", "hosp03b_law", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18766, 1172.453491, -1333.777709, 18.423500, 89.999992, 179.999984, -90.000007, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 5708, "hospital_lawn", "hosp03b_law", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18766, 1172.453491, -1337.468505, 18.563503, 89.999992, 180.000000, -89.999961, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 5708, "hospital_lawn", "hosp03b_law", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(19380, 1183.487548, -1337.795532, 19.018953, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 1675, "wshxrefhse", "greygreensubuild_128", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(19380, 1183.487548, -1328.156127, 19.018953, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 1675, "wshxrefhse", "greygreensubuild_128", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(19380, 1183.487548, -1323.486694, 19.019952, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 1675, "wshxrefhse", "greygreensubuild_128", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(19380, 1181.728149, -1317.370117, 19.018949, 0.000000, 90.000000, 31.999998, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 1675, "wshxrefhse", "greygreensubuild_128", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(19380, 1177.965454, -1311.348144, 19.019947, 0.000000, 90.000000, 31.999998, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 1675, "wshxrefhse", "greygreensubuild_128", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(19380, 1172.976684, -1337.795532, 19.018953, 0.000000, 90.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 1675, "wshxrefhse", "greygreensubuild_128", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(19380, 1172.976684, -1328.156127, 19.018953, 0.000000, 90.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 1675, "wshxrefhse", "greygreensubuild_128", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(19380, 1172.976684, -1323.486694, 19.019952, 0.000000, 90.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 1675, "wshxrefhse", "greygreensubuild_128", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(19380, 1172.976684, -1313.867797, 19.017953, 0.000000, 90.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 1675, "wshxrefhse", "greygreensubuild_128", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(19380, 1172.976684, -1306.497558, 19.017953, 0.000000, 90.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 1675, "wshxrefhse", "greygreensubuild_128", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(19380, 1177.965454, -1311.348144, 18.009941, 0.000003, 90.000007, 31.999994, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 1675, "wshxrefhse", "greygreensubuild_128", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(19380, 1172.976684, -1337.795532, 18.008945, 0.000000, 90.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 1675, "wshxrefhse", "greygreensubuild_128", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(19380, 1172.976684, -1328.156127, 18.008945, 0.000000, 90.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 1675, "wshxrefhse", "greygreensubuild_128", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(19380, 1172.976684, -1323.486694, 18.009944, 0.000000, 90.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 1675, "wshxrefhse", "greygreensubuild_128", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(19380, 1172.976684, -1313.867797, 18.007946, 0.000000, 90.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 1675, "wshxrefhse", "greygreensubuild_128", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(19380, 1172.976684, -1306.497558, 18.007946, 0.000000, 90.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 1675, "wshxrefhse", "greygreensubuild_128", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18766, 1178.461791, -1323.757812, 18.413499, 89.999992, 180.000000, -89.999961, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 18022, "genintintfasta", "ceilingtile1_128", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18766, 1183.461059, -1323.757812, 18.412500, 89.999992, 180.000000, -89.999961, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 18022, "genintintfasta", "ceilingtile1_128", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18766, 1180.409912, -1310.413330, 18.413499, 89.999992, 209.791961, -87.791893, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 18022, "genintintfasta", "ceilingtile1_128", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18766, 1183.461059, -1333.759033, 18.412500, 89.999992, 180.000000, -89.999961, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 18022, "genintintfasta", "ceilingtile1_128", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18766, 1186.071777, -1323.757812, 18.413499, 89.999992, 180.000015, -89.999961, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 18022, "genintintfasta", "ceilingtile1_128", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18766, 1183.801513, -1315.840332, 18.414499, 89.999992, 209.791961, -87.791893, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 18022, "genintintfasta", "ceilingtile1_128", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18766, 1186.071777, -1333.759033, 18.414499, 89.999992, 180.000015, -89.999961, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 18022, "genintintfasta", "ceilingtile1_128", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18766, 1180.263305, -1315.887817, 18.411500, 89.999992, 180.000000, -89.999961, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 18022, "genintintfasta", "ceilingtile1_128", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18766, 1177.403320, -1309.847290, 18.412500, 89.999992, 180.000000, -89.999961, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 18022, "genintintfasta", "ceilingtile1_128", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18766, 1177.403320, -1319.837524, 18.414499, 89.999992, 180.000000, -89.999961, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 18022, "genintintfasta", "ceilingtile1_128", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18766, 1177.403320, -1329.827636, 18.412500, 89.999992, 180.000000, -89.999961, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 18022, "genintintfasta", "ceilingtile1_128", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18766, 1180.263305, -1333.767456, 18.413499, 89.999992, 180.000000, -89.999961, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 18022, "genintintfasta", "ceilingtile1_128", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18766, 1177.403320, -1333.767700, 18.411500, 89.999992, 180.000000, -89.999961, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 18022, "genintintfasta", "ceilingtile1_128", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18766, 1183.461059, -1337.449829, 18.403499, 89.999992, 180.000015, -89.999961, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 18022, "genintintfasta", "ceilingtile1_128", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18766, 1186.071777, -1337.449829, 18.413499, 89.999992, 180.000030, -89.999961, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 18022, "genintintfasta", "ceilingtile1_128", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18766, 1180.263305, -1337.458251, 18.414499, 89.999992, 180.000015, -89.999961, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 18022, "genintintfasta", "ceilingtile1_128", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18766, 1177.403320, -1337.458496, 18.412500, 89.999992, 180.000015, -89.999961, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 18022, "genintintfasta", "ceilingtile1_128", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18766, 1172.403442, -1309.847290, 18.413499, 89.999992, 180.000015, -89.999961, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 18022, "genintintfasta", "ceilingtile1_128", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18766, 1172.403442, -1319.837524, 18.403499, 90.151000, 180.000015, -89.999961, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 18022, "genintintfasta", "ceilingtile1_128", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18766, 1172.403442, -1329.827636, 18.413499, 89.999992, 180.000015, -89.999961, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 18022, "genintintfasta", "ceilingtile1_128", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18766, 1172.403442, -1337.458496, 18.414499, 89.999992, 180.000030, -89.999961, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 18022, "genintintfasta", "ceilingtile1_128", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18980, 1186.821655, -1323.395629, 6.532814, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 5708, "hospital_lawn", "hosp03b_law", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18980, 1186.821655, -1319.395629, 6.532814, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 5708, "hospital_lawn", "hosp03b_law", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18980, 1186.821655, -1327.395629, 6.532814, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 5708, "hospital_lawn", "hosp03b_law", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18980, 1186.821655, -1331.395629, 6.532814, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 5708, "hospital_lawn", "hosp03b_law", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18980, 1186.821655, -1340.826538, 6.532814, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 5708, "hospital_lawn", "hosp03b_law", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18980, 1179.573608, -1307.313720, 6.532814, 0.000000, 0.000000, 31.699979, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 5708, "hospital_lawn", "hosp03b_law", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(19380, 1172.818237, -1309.942260, 9.884581, 0.000000, -0.499998, -0.099999, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 5708, "hospital_lawn", "hosp03b_law", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18980, 1180.325561, -1308.530761, 17.452819, 90.000000, 0.000000, 31.699979, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 5708, "hospital_lawn", "hosp03b_law", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18980, 1180.325561, -1308.530761, 16.472827, 90.000000, 0.000000, 31.699979, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 5708, "hospital_lawn", "hosp03b_law", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18762, 1183.049560, -1312.934204, 16.476770, 89.999992, 89.999992, -58.300025, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(1368, 1188.193725, -1317.371337, 13.247803, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 14581, "ab_mafiasuitea", "cof_wood2", 0x00000000);
    // SetDynamicObjectMaterial(HSPTLXTA, 1, 16322, "a51_stores", "steel64", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(1368, 1188.193725, -1321.500244, 13.247803, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 14581, "ab_mafiasuitea", "cof_wood2", 0x00000000);
    // SetDynamicObjectMaterial(HSPTLXTA, 1, 16322, "a51_stores", "steel64", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(1368, 1188.193725, -1325.420410, 13.247803, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 14581, "ab_mafiasuitea", "cof_wood2", 0x00000000);
    // SetDynamicObjectMaterial(HSPTLXTA, 1, 16322, "a51_stores", "steel64", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(1368, 1188.193725, -1329.450561, 13.247803, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 14581, "ab_mafiasuitea", "cof_wood2", 0x00000000);
    // SetDynamicObjectMaterial(HSPTLXTA, 1, 16322, "a51_stores", "steel64", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18766, 1180.858642, -1356.909545, 12.800045, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 7555, "bballcpark1", "ws_carparknew2", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18766, 1180.858642, -1361.888916, 12.800045, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 7555, "bballcpark1", "ws_carparknew2", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18766, 1180.857666, -1364.969726, 12.799043, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 7555, "bballcpark1", "ws_carparknew2", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18766, 1170.859130, -1356.909545, 12.800045, 89.999992, 89.999992, -89.999992, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 7555, "bballcpark1", "ws_carparknew2", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18766, 1170.859130, -1361.888916, 12.800045, 89.999992, 89.999992, -89.999992, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 7555, "bballcpark1", "ws_carparknew2", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18766, 1170.858154, -1364.969726, 12.799043, 89.999992, 89.999992, -89.999992, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 7555, "bballcpark1", "ws_carparknew2", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18766, 1190.465942, -1356.910522, 11.388870, 73.399963, 90.000000, -89.999954, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 16640, "a51", "ws_carparkwall2", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18766, 1190.465942, -1361.889892, 11.388870, 73.399963, 90.000000, -89.999954, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 16640, "a51", "ws_carparkwall2", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18766, 1190.464721, -1364.970703, 11.388191, 73.399963, 90.000000, -89.999954, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 16640, "a51", "ws_carparkwall2", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(19380, 1162.069213, -1303.309692, 33.363845, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 4593, "buildblk55", "sl_plazatile01", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(3361, 1158.239257, -1311.270996, 31.374538, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 16640, "a51", "bluemetal", 0x00000000);
    // SetDynamicObjectMaterial(HSPTLXTA, 1, 16640, "a51", "Metal3_128", 0x00000000);
    // SetDynamicObjectMaterial(HSPTLXTA, 2, 5154, "dkcargoshp_las2", "boatrailing_128", 0x00000000);
    // SetDynamicObjectMaterial(HSPTLXTA, 3, 16640, "a51", "Metal3_128", 0x00000000);
    // SetDynamicObjectMaterial(HSPTLXTA, 4, 14776, "genintintcarint3", "ab_steelFrame", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18764, 1162.112670, -1303.536621, 30.809787, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(3361, 1165.879882, -1311.270996, 31.374538, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 16640, "a51", "bluemetal", 0x00000000);
    // SetDynamicObjectMaterial(HSPTLXTA, 1, 16640, "a51", "Metal3_128", 0x00000000);
    // SetDynamicObjectMaterial(HSPTLXTA, 2, 5154, "dkcargoshp_las2", "boatrailing_128", 0x00000000);
    // SetDynamicObjectMaterial(HSPTLXTA, 3, 16640, "a51", "Metal3_128", 0x00000000);
    // SetDynamicObjectMaterial(HSPTLXTA, 4, 14776, "genintintcarint3", "ab_steelFrame", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(16101, 1158.247436, -1299.340332, 33.424209, 0.000000, 180.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 1717, "cj_tv", "CJ_STEEL", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(16101, 1165.917602, -1299.340332, 33.424209, 0.000000, 180.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 1717, "cj_tv", "CJ_STEEL", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(16101, 1162.087646, -1299.340332, 33.424209, 0.000000, 180.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 1717, "cj_tv", "CJ_STEEL", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(16101, 1165.917602, -1303.600708, 33.424209, 0.000000, 180.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 1717, "cj_tv", "CJ_STEEL", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(16101, 1158.247436, -1303.560913, 33.424209, 0.000000, 180.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 1717, "cj_tv", "CJ_STEEL", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18980, 1172.313110, -1329.955688, 30.791885, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 4593, "buildblk55", "sl_plazatile01", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18980, 1172.313110, -1304.946289, 30.791885, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 4593, "buildblk55", "sl_plazatile01", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18762, 1170.313720, -1291.944335, 30.793054, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 4593, "buildblk55", "sl_plazatile01", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18762, 1165.324218, -1291.944335, 30.793054, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 4593, "buildblk55", "sl_plazatile01", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18762, 1160.323364, -1291.944335, 30.793054, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 4593, "buildblk55", "sl_plazatile01", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18762, 1155.333129, -1291.944335, 30.793054, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 4593, "buildblk55", "sl_plazatile01", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18762, 1152.512817, -1291.943359, 30.794054, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 4593, "buildblk55", "sl_plazatile01", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18980, 1150.512451, -1329.955688, 30.791885, 89.999992, 89.999992, -89.999992, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 4593, "buildblk55", "sl_plazatile01", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18980, 1150.512451, -1304.946289, 30.791885, 89.999992, 89.999992, -89.999992, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 4593, "buildblk55", "sl_plazatile01", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18762, 1170.313720, -1341.965698, 30.793054, 0.000000, 90.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 4593, "buildblk55", "sl_plazatile01", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18762, 1165.324218, -1341.965698, 30.793054, 0.000000, 90.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 4593, "buildblk55", "sl_plazatile01", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18762, 1160.323364, -1341.965698, 30.793054, 0.000000, 90.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 4593, "buildblk55", "sl_plazatile01", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18762, 1155.333129, -1341.965698, 30.793054, 0.000000, 90.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 4593, "buildblk55", "sl_plazatile01", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18762, 1152.512817, -1341.964721, 30.794054, 0.000000, 90.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 4593, "buildblk55", "sl_plazatile01", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(16101, 1162.007568, -1307.770141, 33.424209, 0.000000, 180.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 1717, "cj_tv", "CJ_STEEL", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(3658, 1154.135864, -1319.562866, 31.157075, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 16640, "a51", "airvent_gz", 0x00000000);
    // SetDynamicObjectMaterial(HSPTLXTA, 2, 16640, "a51", "vent01_64", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(3658, 1154.765991, -1322.153076, 31.157075, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 16640, "a51", "airvent_gz", 0x00000000);
    // SetDynamicObjectMaterial(HSPTLXTA, 2, 16640, "a51", "vent01_64", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(3658, 1157.396728, -1322.343261, 31.157075, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 16640, "a51", "airvent_gz", 0x00000000);
    // SetDynamicObjectMaterial(HSPTLXTA, 2, 16640, "a51", "vent01_64", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(3658, 1166.126464, -1327.394042, 31.157075, 0.000014, 0.000007, 89.999923, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 16640, "a51", "airvent_gz", 0x00000000);
    // SetDynamicObjectMaterial(HSPTLXTA, 2, 16640, "a51", "vent01_64", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(3658, 1168.716674, -1326.763916, 31.157075, 0.000007, -0.000014, 179.999862, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 16640, "a51", "airvent_gz", 0x00000000);
    // SetDynamicObjectMaterial(HSPTLXTA, 2, 16640, "a51", "vent01_64", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(3658, 1168.906860, -1324.133178, 31.157075, 0.000014, 0.000007, 89.999923, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 16640, "a51", "airvent_gz", 0x00000000);
    // SetDynamicObjectMaterial(HSPTLXTA, 2, 16640, "a51", "vent01_64", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18762, 1186.812133, -1328.479370, 17.423763, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 5708, "hospital_lawn", "hosp03b_law", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18762, 1186.812133, -1323.479614, 17.423763, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 5708, "hospital_lawn", "hosp03b_law", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18762, 1186.822143, -1321.897827, 17.424762, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 5708, "hospital_lawn", "hosp03b_law", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18762, 1186.812133, -1334.357788, 17.424762, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 5708, "hospital_lawn", "hosp03b_law", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18762, 1186.802124, -1338.039306, 17.423763, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 5708, "hospital_lawn", "hosp03b_law", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18762, 1186.812133, -1328.479370, 16.423770, 89.999992, 89.999992, -89.999992, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 5708, "hospital_lawn", "hosp03b_law", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18762, 1186.812133, -1321.897827, 16.424770, 89.999992, 89.999992, -89.999992, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 5708, "hospital_lawn", "hosp03b_law", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18762, 1186.812133, -1334.357788, 16.424770, 89.999992, 89.999992, -89.999992, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 5708, "hospital_lawn", "hosp03b_law", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18762, 1186.822143, -1338.039306, 16.423770, 89.999992, 89.999992, -89.999992, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 5708, "hospital_lawn", "hosp03b_law", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18762, 1186.822143, -1323.479614, 16.423751, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 5708, "hospital_lawn", "hosp03b_law", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(18762, 1183.049560, -1312.934204, 17.466766, 89.999992, 89.999992, -58.300025, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(2662, 1183.513549, -1312.680786, 17.502500, 0.000000, 0.000000, 121.800018, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterialText(HSPTLXTA, 0, "PILLBOX", 90, "Arial", 65, 1, 0xFF000000, 0x00000000, 1);
    // HSPTLXTA = CreateDynamicObject(2662, 1183.534423, -1312.714355, 16.782489, 0.000006, -0.000003, 121.799995, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterialText(HSPTLXTA, 0, "ARI", 90, "Arial", 100, 1, 0xFF000000, 0x00000000, 1);
    // HSPTLXTA = CreateDynamicObject(2662, 1182.817871, -1311.558471, 16.782489, 0.000006, -0.000003, 121.799995, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterialText(HSPTLXTA, 0, "VENA", 90, "Arial", 100, 1, 0xFF000000, 0x00000000, 1);
    // HSPTLXTA = CreateDynamicObject(2643, 1184.298095, -1314.125122, 16.784208, 0.000000, 0.000000, 122.000045, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 8480, "csrspalace01", "black32", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(2662, 1184.184448, -1313.742309, 16.320016, 0.000000, 0.000000, 122.700019, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterialText(HSPTLXTA, 0, "+", 50, "Arial", 100, 0, 0xFFFFFFFF, 0x00000000, 1);
    // HSPTLXTA = CreateDynamicObject(2662, 1184.844726, -1298.903686, 13.842385, -13.299990, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); // perubahan posisi
    // SetDynamicObjectMaterialText(HSPTLXTA, 0, "PILLBOX", 90, "Arial", 50, 1, 0xFF000000, 0x00000000, 1);
    // HSPTLXTA = CreateDynamicObject(2662, 1184.844482, -1297.392333, 13.842275, -13.399989, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); // perubahan posisi
    // SetDynamicObjectMaterialText(HSPTLXTA, 0, "ATHERLIFE", 90, "Arial", 50, 1, 0xFF000000, 0x00000000, 1);
    // HSPTLXTA = CreateDynamicObject(19428, 1183.012817, -1365.715576, 12.500041, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 10756, "airportroads_sfse", "ws_white_wall1", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(19428, 1185.773071, -1365.715576, 12.500041, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 10756, "airportroads_sfse", "ws_white_wall1", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(19428, 1177.309692, -1365.715576, 12.500041, 89.999992, 89.999992, -89.999992, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 10756, "airportroads_sfse", "ws_white_wall1", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(19428, 1180.069946, -1365.715576, 12.500041, 89.999992, 89.999992, -89.999992, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 10756, "airportroads_sfse", "ws_white_wall1", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(19428, 1179.220336, -1356.145141, 12.500041, -89.999992, 153.434936, 153.434967, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 10756, "airportroads_sfse", "ws_white_wall1", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(19428, 1181.980590, -1356.145141, 12.500041, -89.999992, 153.434936, 153.434967, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 10756, "airportroads_sfse", "ws_white_wall1", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(19428, 1173.679443, -1356.145141, 12.500041, -89.999992, 111.624618, 111.624610, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 10756, "airportroads_sfse", "ws_white_wall1", 0x00000000);
    // HSPTLXTA = CreateDynamicObject(19428, 1176.439697, -1356.145141, 12.500041, -89.999992, 111.624618, 111.624610, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(HSPTLXTA, 0, 10756, "airportroads_sfse", "ws_white_wall1", 0x00000000);
    // /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    // /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    // /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    // CreateDynamicObject(19353, 1184.989257, -1298.141723, 12.694959, 0.000000, -13.499993, 360.000000, 0, 0, -1, 200.00, 200.00); // tambahan
    // CreateDynamicObject(0, 0.000000, 0.000000, 0.000000, 0.000000, 0.000000, 0.000000, 0, 0, -1, 0.00, 0.00); 
    // CreateDynamicObject(3660, 1186.394287, -1324.221679, 14.658434, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(3657, 1176.299560, -1327.062866, 13.516349, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(3657, 1176.299560, -1320.401855, 13.516349, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(19425, 1187.033081, -1312.837036, 12.552500, 0.000000, 0.000000, 102.500015, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(19425, 1186.358032, -1309.790161, 12.553503, 0.000000, 0.000000, 102.500015, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(19425, 1185.682861, -1306.744873, 12.552501, 0.000000, 0.000000, 102.500015, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1359, 1188.094604, -1327.437133, 13.277644, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1359, 1188.094604, -1323.427001, 13.277644, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1359, 1188.094604, -1319.416992, 13.277644, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1359, 1188.094604, -1315.266479, 13.277644, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1886, 1172.935913, -1319.864501, 18.005115, 27.100000, 0.000000, 17.999998, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(19425, 1186.793090, -1338.687255, 12.552500, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(19425, 1186.793090, -1335.387451, 12.552500, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(3934, 1162.189208, -1303.292968, 33.442733, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1569, 1158.473388, -1330.529785, 30.498193, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1635, 1163.125488, -1329.962280, 32.120601, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1597, 1176.327270, -1308.577758, 14.688431, 0.000000, 0.000000, -23.600000, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1597, 1177.691894, -1339.500366, 14.558431, 0.000000, 0.000000, 47.499996, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(869, 1177.365234, -1315.637451, 13.553153, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(869, 1177.365234, -1332.137939, 13.553153, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(19121, 1184.366577, -1301.067504, 13.755238, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(19121, 1184.366577, -1295.076538, 13.755238, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(19121, 1183.935180, -1356.123535, 13.841050, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(634, 811.906005, -1461.199951, 12.500000, 0.000000, 0.000000, -41.762840, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(5812, 1230.890014, -1337.979980, 12.539094, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00);

    //RS KOTA ATHERLIFE
    HSPTLXTA = CreateDynamicObject(18981, 1776.218383, -1133.591430, 22.791318, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18981, 1751.240234, -1133.591430, 22.791318, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 6284, "bev_law2", "glass_fence_64hv", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18981, 1718.729248, -1133.611450, 22.791318, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18981, 1705.798583, -1121.562255, 22.791318, 0.000007, -0.000007, 179.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18981, 1743.677124, -1109.573364, 22.791318, 0.000000, 0.000000, -90.000007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18981, 1718.668945, -1109.573364, 22.791318, 0.000000, 0.000000, -90.000007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19926, 1731.517700, -1112.733642, 23.256353, 0.000012, 0.000006, -0.000144, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-10-percent", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 18646, "matcolours", "grey-80-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19926, 1731.519653, -1112.735595, 22.378456, 0.000012, 0.000006, -0.000144, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 18646, "matcolours", "grey-80-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19926, 1731.518676, -1113.735351, 23.257362, 0.000028, 0.000006, -0.000190, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-10-percent", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 18646, "matcolours", "grey-80-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19926, 1731.517700, -1113.734375, 22.376440, 0.000028, 0.000006, -0.000190, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 18646, "matcolours", "grey-80-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2166, 1730.597045, -1112.241333, 23.035377, -0.000020, 0.000000, -179.999893, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 18646, "matcolours", "grey-40-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19926, 1730.206909, -1114.255004, 23.256353, 0.000006, 0.000043, -90.000083, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-10-percent", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 18646, "matcolours", "grey-80-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19926, 1730.207885, -1114.255981, 22.377447, 0.000006, 0.000043, -90.000083, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 18646, "matcolours", "grey-80-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(11744, 1730.696166, -1113.323120, 23.846929, -0.000014, -0.000025, 115.000213, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19610, 1730.688354, -1113.327636, 23.975194, -89.999992, 469.968078, 134.967971, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18996, "mattextures", "sampblack", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2266, 1730.856323, -1113.740844, 23.893682, 10.000039, -0.000022, 24.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18996, "mattextures", "sampblack", 0xFFFFFFFF);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 18996, "mattextures", "sampblack", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2266, 1730.462036, -1112.848022, 24.065710, -10.000039, 0.000029, -154.999862, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18996, "mattextures", "sampblack", 0xFFFFFFFF);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 19655, "mattubes", "bluedirt1", 0x00000000);
    HSPTLXTA = CreateDynamicObject(11744, 1728.822631, -1113.377075, 23.846929, 0.000006, -0.000093, 79.999664, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19610, 1728.813354, -1113.376098, 23.975194, -89.999992, 505.165222, 135.164962, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18996, "mattextures", "sampblack", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2266, 1728.713745, -1113.810668, 23.893682, 10.000099, 0.000001, -10.000400, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18996, "mattextures", "sampblack", 0xFFFFFFFF);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 18996, "mattextures", "sampblack", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2266, 1728.902954, -1112.853271, 24.065710, -10.000102, 0.000003, 170.000244, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18996, "mattextures", "sampblack", 0xFFFFFFFF);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 19655, "mattubes", "bluedirt1", 0x00000000);
    HSPTLXTA = CreateDynamicObject(11728, 1731.044311, -1112.167480, 24.073064, -11.799996, 0.000022, -90.000030, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, -1, "none", "none", 0xFFFFFFFF);
    HSPTLXTA = CreateDynamicObject(19926, 1728.316040, -1114.255004, 23.256353, 0.000006, 0.000048, -90.000083, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-10-percent", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 18646, "matcolours", "grey-80-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19926, 1728.317016, -1114.255981, 22.377447, 0.000006, 0.000049, -90.000083, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 18646, "matcolours", "grey-80-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2166, 1728.666870, -1112.241333, 23.035377, -0.000020, 0.000000, -179.999893, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 18646, "matcolours", "grey-40-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19810, 1729.996459, -1113.032470, 23.825323, 89.999992, 405.000000, -45.000045, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 15040, "cuntcuts", "csnewspaper", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19810, 1729.657104, -1113.033447, 23.826328, 89.999992, 405.000000, -45.000045, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 15040, "cuntcuts", "csnewspaper", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2824, 1729.126342, -1114.136230, 24.195287, 0.000019, -0.000006, 19.499977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 4, 19480, "signsurf", "sign", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19874, 1729.847778, -1113.031494, 23.816350, 0.000000, 0.000020, -90.000038, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2824, 1731.449829, -1113.388916, 24.195287, 0.000000, -0.000020, 88.999786, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 4, 19480, "signsurf", "sign", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1726.867065, -1112.527099, 27.405994, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 14847, "mp_policesf", "mp_cop_tile", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19926, 1736.076171, -1112.733520, 23.256353, 0.000014, 0.000006, -0.000266, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-10-percent", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 18646, "matcolours", "grey-80-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19926, 1736.078125, -1112.735473, 22.378456, 0.000014, 0.000006, -0.000266, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 18646, "matcolours", "grey-80-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19926, 1736.077148, -1113.735229, 23.257362, 0.000028, 0.000006, -0.000311, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-10-percent", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 18646, "matcolours", "grey-80-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19926, 1736.076171, -1113.734252, 22.376440, 0.000028, 0.000006, -0.000311, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 18646, "matcolours", "grey-80-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2166, 1735.155517, -1112.241210, 23.035377, -0.000020, 0.000000, -179.999832, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 18646, "matcolours", "grey-40-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19926, 1734.765380, -1114.254882, 23.256353, 0.000006, 0.000043, -90.000160, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-10-percent", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 18646, "matcolours", "grey-80-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19926, 1734.766357, -1114.255859, 22.377447, 0.000006, 0.000043, -90.000160, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 18646, "matcolours", "grey-80-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(11744, 1735.254638, -1113.322998, 23.846929, -0.000014, -0.000025, 115.000167, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19610, 1735.246826, -1113.327514, 23.975194, -89.999992, 143.661041, 168.661010, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18996, "mattextures", "sampblack", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2266, 1735.414794, -1113.740722, 23.893682, 10.000035, -0.000022, 24.999769, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18996, "mattextures", "sampblack", 0xFFFFFFFF);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 18996, "mattextures", "sampblack", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2266, 1735.020507, -1112.847900, 24.065710, -10.000035, 0.000029, -154.999816, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18996, "mattextures", "sampblack", 0xFFFFFFFF);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 19655, "mattubes", "bluedirt1", 0x00000000);
    HSPTLXTA = CreateDynamicObject(11744, 1733.381103, -1113.376953, 23.846929, 0.000006, -0.000093, 79.999603, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19610, 1733.371826, -1113.375976, 23.975194, -89.999992, 178.844085, 168.843887, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18996, "mattextures", "sampblack", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2266, 1733.272216, -1113.810546, 23.893682, 10.000094, 0.000001, -10.000494, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18996, "mattextures", "sampblack", 0xFFFFFFFF);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 18996, "mattextures", "sampblack", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2266, 1733.461425, -1112.853149, 24.065710, -10.000099, 0.000003, 170.000137, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18996, "mattextures", "sampblack", 0xFFFFFFFF);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 19655, "mattubes", "bluedirt1", 0x00000000);
    HSPTLXTA = CreateDynamicObject(11728, 1735.602783, -1112.167358, 24.073064, -11.799991, 0.000022, -90.000106, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, -1, "none", "none", 0xFFFFFFFF);
    HSPTLXTA = CreateDynamicObject(19926, 1732.874511, -1114.254882, 23.256353, 0.000006, 0.000049, -90.000160, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-10-percent", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 18646, "matcolours", "grey-80-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19926, 1732.875488, -1114.255859, 22.377447, 0.000006, 0.000049, -90.000160, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 18646, "matcolours", "grey-80-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2166, 1733.225341, -1112.241210, 23.035377, -0.000020, 0.000000, -179.999832, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 18646, "matcolours", "grey-40-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19810, 1734.554931, -1113.032348, 23.825323, 89.999992, 731.309448, -11.309592, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 15040, "cuntcuts", "csnewspaper", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19810, 1734.215576, -1113.033325, 23.826328, 89.999992, 731.309448, -11.309592, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 15040, "cuntcuts", "csnewspaper", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2824, 1733.684814, -1114.136108, 24.195287, 0.000018, -0.000006, 19.500009, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 4, 19480, "signsurf", "sign", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19874, 1734.406250, -1113.031372, 23.816350, 0.000000, 0.000020, -90.000114, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2824, 1736.008300, -1113.388793, 24.195287, 0.000000, -0.000020, 88.999717, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 4, 19480, "signsurf", "sign", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1739.176879, -1115.066650, 21.215944, 0.000000, 180.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 4821, "union_las", "lasunion995", 0x00000000);
    HSPTLXTA = CreateDynamicObject(1713, 1711.331542, -1112.868652, 23.085935, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 2, 10988, "mission2_sfse", "ws_fancywallpink", 0x00000000);
    HSPTLXTA = CreateDynamicObject(1713, 1714.160766, -1112.868652, 23.085935, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 2, 10988, "mission2_sfse", "ws_fancywallpink", 0x00000000);
    HSPTLXTA = CreateDynamicObject(1713, 1711.331542, -1118.528686, 23.085935, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 2, 10988, "mission2_sfse", "ws_fancywallpink", 0x00000000);
    HSPTLXTA = CreateDynamicObject(1713, 1714.160766, -1118.528686, 23.085935, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 2, 10988, "mission2_sfse", "ws_fancywallpink", 0x00000000);
    HSPTLXTA = CreateDynamicObject(1713, 1717.002685, -1112.868652, 23.085935, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 2, 10988, "mission2_sfse", "ws_fancywallpink", 0x00000000);
    HSPTLXTA = CreateDynamicObject(1713, 1717.002685, -1118.528686, 23.085935, 0.000022, 0.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 2, 10988, "mission2_sfse", "ws_fancywallpink", 0x00000000);
    HSPTLXTA = CreateDynamicObject(1746, 1717.025756, -1114.916870, 23.027549, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 6985, "vgnfremnt2", "striplightspinky_256", 0x00000000);
    HSPTLXTA = CreateDynamicObject(1746, 1714.165039, -1114.916870, 23.027549, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 6985, "vgnfremnt2", "striplightspinky_256", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19926, 1722.353515, -1114.193725, 23.319946, -0.000000, 0.000000, -90.000068, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-10-percent", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 18646, "matcolours", "grey-80-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19926, 1722.351562, -1114.195678, 22.442049, -0.000000, 0.000000, -90.000068, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 18646, "matcolours", "grey-80-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19926, 1721.351806, -1114.194702, 23.320955, 0.000012, 0.000000, -90.000114, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-10-percent", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 18646, "matcolours", "grey-80-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19926, 1721.352783, -1114.193725, 22.440031, 0.000012, 0.000000, -90.000114, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 18646, "matcolours", "grey-80-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2166, 1722.845825, -1113.273071, 23.098968, -0.000006, 0.000007, 89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 18646, "matcolours", "grey-40-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19926, 1720.832153, -1112.882934, 23.319946, 0.000012, 0.000028, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-10-percent", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 18646, "matcolours", "grey-80-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19926, 1720.831176, -1112.883911, 22.441040, 0.000012, 0.000028, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 18646, "matcolours", "grey-80-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(11744, 1721.764038, -1113.372192, 23.910522, -0.000014, -0.000009, 25.000251, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19610, 1721.759521, -1113.364379, 24.038787, -89.999992, 528.356140, 103.355979, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18996, "mattextures", "sampblack", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2266, 1721.346313, -1113.532348, 23.957275, 10.000020, -0.000022, -65.000244, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18996, "mattextures", "sampblack", 0xFFFFFFFF);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 18996, "mattextures", "sampblack", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2266, 1722.239135, -1113.138061, 24.129301, -10.000020, 0.000029, 115.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18996, "mattextures", "sampblack", 0xFFFFFFFF);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 19655, "mattubes", "bluedirt1", 0x00000000);
    HSPTLXTA = CreateDynamicObject(11744, 1721.710083, -1111.498657, 23.910522, -0.000001, -0.000078, -10.000282, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19610, 1721.711059, -1111.489379, 24.038787, -89.999992, 563.433227, 103.432899, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18996, "mattextures", "sampblack", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2266, 1721.276489, -1111.389770, 23.957275, 10.000083, -0.000006, -100.000328, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18996, "mattextures", "sampblack", 0xFFFFFFFF);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 18996, "mattextures", "sampblack", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2266, 1722.233886, -1111.578979, 24.129301, -10.000085, 0.000012, 80.000274, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18996, "mattextures", "sampblack", 0xFFFFFFFF);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 19655, "mattubes", "bluedirt1", 0x00000000);
    HSPTLXTA = CreateDynamicObject(11728, 1722.919677, -1113.720336, 24.136657, -11.799985, 0.000007, 179.999801, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, -1, "none", "none", 0xFFFFFFFF);
    HSPTLXTA = CreateDynamicObject(19926, 1720.832153, -1110.992065, 23.319946, 0.000012, 0.000035, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-10-percent", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 18646, "matcolours", "grey-80-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19926, 1720.831176, -1110.993041, 22.441040, 0.000012, 0.000035, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 18646, "matcolours", "grey-80-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2166, 1722.845825, -1111.342895, 23.098968, -0.000006, 0.000007, 89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 18646, "matcolours", "grey-40-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19810, 1722.054687, -1112.672485, 23.888916, 89.999992, 346.631500, -76.631500, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 15040, "cuntcuts", "csnewspaper", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19810, 1722.053710, -1112.333129, 23.889921, 89.999992, 346.631500, -76.631500, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 15040, "cuntcuts", "csnewspaper", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2824, 1720.950927, -1111.802368, 24.258880, 0.000001, -0.000007, -70.500007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 4, 19480, "signsurf", "sign", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19874, 1722.055664, -1112.523803, 23.879943, 0.000007, 0.000006, 179.999786, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2824, 1721.698242, -1114.125854, 24.258880, -0.000007, -0.000006, -1.000157, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 4, 19480, "signsurf", "sign", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1726.867065, -1117.507568, 27.405994, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 14847, "mp_policesf", "mp_cop_tile", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1772.725097, -1110.826293, 24.685987, 0.000000, 180.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1712.888183, -1124.358520, 27.405994, 0.000000, 89.999992, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1717.068969, -1130.669555, 27.405994, -0.000007, 90.000000, -89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18981, 1756.650634, -1108.623901, 22.791315, 0.000014, 0.000000, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18981, 1788.222167, -1108.623901, 22.791315, 0.000014, 0.000000, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18981, 1769.641845, -1096.632202, 22.791315, 0.000014, 0.000000, 449.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 4821, "union_las", "lasunion995", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18980, 1769.648559, -1096.605102, 23.559108, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18980, 1782.638916, -1096.615112, 22.789121, 180.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18980, 1769.648559, -1096.625122, 34.799083, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18980, 1751.247070, -1133.595581, 34.799083, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18980, 1739.228027, -1133.595092, 22.789121, 180.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18980, 1751.248413, -1133.605102, 23.559108, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18980, 1751.248413, -1133.585083, 23.559108, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18980, 1751.247070, -1133.585571, 34.799083, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18980, 1739.228027, -1133.575073, 22.789121, 180.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18980, 1769.648559, -1096.635131, 34.799083, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18980, 1769.648559, -1096.645141, 23.559108, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18981, 1769.640380, -1108.623901, 22.591314, 0.000014, 90.000000, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18981, 1775.231689, -1108.623901, 22.591314, 0.000014, 90.000000, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18981, 1769.640380, -1120.586059, 22.601314, 0.000014, 89.999992, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18981, 1775.231689, -1120.586059, 22.601314, 0.000014, 89.999992, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18981, 1739.072875, -1121.596679, 22.601314, 0.000014, 89.999977, 179.999816, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18981, 1744.664184, -1121.596679, 22.601314, 0.000014, 89.999977, 179.999816, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18981, 1718.780517, -1121.596679, 22.601314, 0.000014, 89.999969, 179.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18981, 1724.371826, -1121.596679, 22.601314, 0.000014, 89.999969, 179.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    HSPTLXTA = CreateDynamicObject(14877, 1758.649291, -1113.755004, 25.111303, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(14877, 1786.232299, -1113.674926, 25.101303, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 9259, "presidio01_sfn", "sl_whitewood01", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18981, 1760.629882, -1114.813232, 22.791315, 0.000014, 0.000000, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18981, 1784.239746, -1114.813232, 22.791315, 0.000014, 0.000000, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18981, 1783.389282, -1109.613647, 22.791315, 0.000014, 0.000000, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19376, 1761.521362, -1101.938476, 27.103410, 0.000000, 90.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19376, 1761.521362, -1108.230224, 27.113410, 0.000000, 90.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19376, 1772.020263, -1108.230224, 27.113410, 0.000000, 90.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19376, 1777.711547, -1108.230224, 27.123411, 0.000000, 90.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19376, 1766.372436, -1117.859375, 27.123411, 0.000000, 90.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19376, 1772.020263, -1117.859375, 27.113410, 0.000000, 90.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19376, 1777.711547, -1117.859375, 27.123411, 0.000000, 90.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19376, 1761.521362, -1127.489624, 27.113410, 0.000000, 90.000030, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19376, 1772.020263, -1127.489624, 27.113410, 0.000000, 90.000030, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19376, 1777.711547, -1127.489624, 27.123411, 0.000000, 90.000030, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19376, 1782.473266, -1127.489624, 27.113410, 0.000000, 90.000030, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19376, 1782.473266, -1119.169189, 27.103410, 0.000000, 90.000030, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1740.207031, -1132.805664, 24.335979, 0.000014, 90.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1756.647460, -1132.805664, 24.335979, 0.000014, 90.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1751.146606, -1132.805664, 28.835992, 89.999992, 270.000000, -89.999984, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1744.725830, -1132.805664, 28.835992, 89.999992, 270.000000, -89.999984, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18981, 1788.221191, -1120.633544, 22.791315, 0.000014, 0.000000, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18981, 1756.650390, -1114.817016, 22.791315, 0.000014, 0.000000, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1756.647460, -1128.246459, 29.616027, 180.000000, 180.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1756.647460, -1128.246459, 32.796028, 180.000000, 180.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1766.126342, -1102.945068, 24.685987, 0.000000, 180.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1776.116333, -1102.945068, 24.685987, 0.000000, 180.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1777.906738, -1102.945068, 24.685987, 0.000000, 180.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1783.386596, -1127.114868, 24.685987, 0.000000, 180.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1783.386596, -1128.085815, 24.685987, 0.000000, 180.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18981, 1776.218383, -1132.791748, 14.861324, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1767.257080, -1102.084838, 24.685987, 0.000000, 180.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1762.017700, -1126.825927, 32.795982, 0.000000, 0.000014, -0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1762.017700, -1126.825927, 29.776027, 0.000000, 0.000014, -0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18762, 1787.229980, -1116.420654, 32.211425, 0.000068, 0.000000, 89.999794, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18762, 1787.229980, -1126.807373, 32.791328, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18762, 1786.230102, -1116.420654, 32.211425, 0.000068, 0.000000, 89.999794, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18762, 1785.239746, -1116.420654, 32.211425, 0.000068, 0.000000, 89.999794, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1785.249755, -1096.645019, 30.615959, 180.000000, 270.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1758.570678, -1123.065307, 32.015987, 0.000000, 90.000015, -0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1715.059448, -1124.348632, 27.405994, 0.000000, 89.999992, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(1499, 1717.040161, -1126.359741, 23.107059, 0.000007, 0.000037, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 3440, "airportpillar", "metalic_64", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 10713, "gayclub_sfs", "CJ_PIZZA_DOOR", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1717.068969, -1128.849121, 27.405994, -0.000007, 90.000000, -89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1717.068969, -1127.338745, 30.605997, -0.000007, 90.000000, -89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18762, 1759.629394, -1132.797119, 27.641321, 89.999992, 179.999984, -89.999984, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18762, 1761.259277, -1132.797119, 27.621322, 89.999992, 179.999984, -90.000007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1767.257080, -1115.084960, 24.685987, 0.000000, 180.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1767.257080, -1128.076416, 24.685987, 0.000000, 180.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(1499, 1767.297119, -1110.093505, 23.107053, 0.000022, 0.000051, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 3440, "airportpillar", "metalic_64", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 10713, "gayclub_sfs", "CJ_PIZZA_DOOR", 0x00000000);
    HSPTLXTA = CreateDynamicObject(1499, 1767.277099, -1107.083618, 23.107053, -0.000022, -0.000051, -90.000190, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 3440, "airportpillar", "metalic_64", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 10713, "gayclub_sfs", "CJ_PIZZA_DOOR", 0x00000000);
    HSPTLXTA = CreateDynamicObject(1499, 1767.297119, -1123.082885, 23.107053, 0.000029, 0.000051, 89.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 3440, "airportpillar", "metalic_64", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 10713, "gayclub_sfs", "CJ_PIZZA_DOOR", 0x00000000);
    HSPTLXTA = CreateDynamicObject(1499, 1767.277099, -1120.072998, 23.107053, -0.000029, -0.000051, -90.000167, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 3440, "airportpillar", "metalic_64", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 10713, "gayclub_sfs", "CJ_PIZZA_DOOR", 0x00000000);
    HSPTLXTA = CreateDynamicObject(1499, 1756.617553, -1130.312500, 23.107053, 0.000068, 0.000051, 89.999763, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 3440, "airportpillar", "metalic_64", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 1419, "break_fence3", "CJ_FRAME_Glass", 0x00000000);
    HSPTLXTA = CreateDynamicObject(1499, 1756.597534, -1127.302612, 23.107053, -0.000068, -0.000051, -90.000053, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 3440, "airportpillar", "metalic_64", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 1419, "break_fence3", "CJ_FRAME_Glass", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18762, 1767.259155, -1108.587402, 26.691316, 89.999992, 179.999984, -0.000007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18762, 1767.259155, -1121.566772, 26.701316, 89.999992, 179.999984, -0.000007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18762, 1756.648193, -1128.807006, 26.801319, 89.999992, 179.999984, -0.000007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19445, 1736.714233, -1129.299194, 23.021314, 0.000000, 90.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10988, "mission2_sfse", "ws_fancywallpink", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19445, 1733.214599, -1129.299194, 23.021314, 0.000000, 90.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10988, "mission2_sfse", "ws_fancywallpink", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19445, 1733.214599, -1119.669311, 23.021314, 0.000000, 90.000030, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10988, "mission2_sfse", "ws_fancywallpink", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19445, 1726.655517, -1130.759399, 23.021316, 0.000000, 90.000030, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10988, "mission2_sfse", "ws_fancywallpink", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19445, 1726.655517, -1123.809448, 23.021316, 0.000000, 90.000030, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10988, "mission2_sfse", "ws_fancywallpink", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19445, 1720.126586, -1124.249755, 23.021316, 0.000000, 90.000030, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10988, "mission2_sfse", "ws_fancywallpink", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19445, 1720.126586, -1114.639526, 23.021316, 0.000000, 90.000030, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10988, "mission2_sfse", "ws_fancywallpink", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19445, 1713.587158, -1121.389160, 23.021316, 0.000000, 90.000030, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10988, "mission2_sfse", "ws_fancywallpink", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19445, 1708.046142, -1118.319702, 23.031316, 0.000000, 90.000030, 360.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10988, "mission2_sfse", "ws_fancywallpink", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19445, 1708.046142, -1114.878051, 23.021316, 0.000000, 90.000030, 360.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10988, "mission2_sfse", "ws_fancywallpink", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19445, 1723.194702, -1130.759399, 23.031316, 0.000000, 90.000030, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10988, "mission2_sfse", "ws_fancywallpink", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19445, 1743.263549, -1128.819946, 23.031316, 0.000000, 90.000030, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10988, "mission2_sfse", "ws_fancywallpink", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19445, 1752.313964, -1128.819946, 23.041315, 0.000000, 90.000030, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10988, "mission2_sfse", "ws_fancywallpink", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19445, 1755.314208, -1128.819946, 23.031316, 0.000000, 90.000030, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18757, "vcinteriors", "dts_elevator_carpet3", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19445, 1763.924560, -1126.479858, 23.031316, 0.000000, 90.000030, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18757, "vcinteriors", "dts_elevator_carpet3", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19445, 1763.924560, -1116.860229, 23.031316, 0.000000, 90.000030, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18757, "vcinteriors", "dts_elevator_carpet3", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19445, 1763.924560, -1109.279296, 23.041315, 0.000000, 90.000030, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18757, "vcinteriors", "dts_elevator_carpet3", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19445, 1759.374267, -1127.791259, 23.041315, 0.000000, 90.000022, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18757, "vcinteriors", "dts_elevator_carpet3", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19445, 1759.374267, -1118.131591, 23.041315, 0.000000, 90.000022, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18757, "vcinteriors", "dts_elevator_carpet3", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19445, 1758.524169, -1118.131591, 23.061317, 0.000000, 90.000022, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18757, "vcinteriors", "dts_elevator_carpet3", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19445, 1758.524169, -1122.262573, 23.051317, 0.000000, 90.000022, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18757, "vcinteriors", "dts_elevator_carpet3", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1708.796630, -1124.358520, 27.405994, 0.000000, 89.999984, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1710.967895, -1124.348632, 27.405994, 0.000000, 89.999984, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1777.914916, -1110.826293, 24.685987, 0.000000, 180.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18762, 1786.230102, -1126.807373, 32.791328, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18762, 1785.239746, -1126.807373, 32.791328, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19375, 1726.667846, -1127.253295, 28.356979, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10973, "mall_sfse", "ws_grilleshade", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1783.390502, -1124.605590, 30.295951, 180.000000, 270.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1783.390502, -1124.815795, 30.295951, 180.000000, 270.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19376, 1761.521362, -1101.938476, 35.173416, 0.000000, 90.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19376, 1761.521362, -1108.230224, 35.183418, 0.000000, 90.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19376, 1772.020263, -1108.230224, 35.183418, 0.000000, 90.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19376, 1777.711547, -1108.230224, 35.193420, 0.000000, 90.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19376, 1766.372436, -1117.859375, 35.193420, 0.000000, 90.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19376, 1772.020263, -1117.859375, 35.183418, 0.000000, 90.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19376, 1777.711547, -1117.859375, 35.193420, 0.000000, 90.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19376, 1761.521362, -1127.489624, 35.183418, 0.000000, 90.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19376, 1772.020263, -1127.489624, 35.183418, 0.000000, 90.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19376, 1777.711547, -1127.489624, 35.193420, 0.000000, 90.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19376, 1782.473266, -1127.489624, 35.183418, 0.000000, 90.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19376, 1782.473266, -1119.169189, 35.173416, 0.000000, 90.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19376, 1782.473266, -1109.627075, 35.173416, 0.000000, 90.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19376, 1782.473266, -1101.934936, 35.173416, 0.000000, 90.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19376, 1772.003295, -1101.934936, 35.173416, 0.000000, 90.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19376, 1762.354614, -1117.864257, 35.173416, 0.000000, 90.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(1499, 1757.137329, -1126.732177, 23.107051, -0.000068, -0.000044, -0.000052, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 3440, "airportpillar", "metalic_64", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 1419, "break_fence3", "CJ_FRAME_Glass", 0x00000000);
    HSPTLXTA = CreateDynamicObject(1499, 1760.148193, -1126.702148, 23.107051, -0.000067, -0.000059, 179.999893, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 3440, "airportpillar", "metalic_64", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 1419, "break_fence3", "CJ_FRAME_Glass", 0x00000000);
    HSPTLXTA = CreateDynamicObject(1499, 1757.137329, -1110.992431, 27.197076, -0.000068, -0.000020, -0.000052, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 3440, "airportpillar", "metalic_64", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 1419, "break_fence3", "CJ_FRAME_Glass", 0x00000000);
    HSPTLXTA = CreateDynamicObject(1499, 1760.148193, -1110.972412, 27.197076, -0.000067, -0.000081, 179.999755, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 3440, "airportpillar", "metalic_64", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 1419, "break_fence3", "CJ_FRAME_Glass", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18762, 1758.628540, -1126.816040, 26.801319, 89.999992, 179.999984, 89.999992, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(1499, 1784.727783, -1116.442138, 27.197076, -0.000068, -0.000006, -0.000052, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 3440, "airportpillar", "metalic_64", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 1419, "break_fence3", "CJ_FRAME_Glass", 0x00000000);
    HSPTLXTA = CreateDynamicObject(1499, 1787.738647, -1116.422119, 27.197076, -0.000067, -0.000096, 179.999664, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 3440, "airportpillar", "metalic_64", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 1419, "break_fence3", "CJ_FRAME_Glass", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18762, 1759.639160, -1110.960937, 32.211425, 0.000082, 0.000000, 89.999748, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18762, 1758.639282, -1110.960937, 32.211425, 0.000082, 0.000000, 89.999748, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18762, 1757.648925, -1110.960937, 32.211425, 0.000082, 0.000000, 89.999748, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18762, 1759.629394, -1132.797119, 35.591339, 89.999992, 179.999984, -89.999984, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18980, 1774.637817, -1132.794677, 35.579154, 180.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18980, 1775.218383, -1132.794677, 35.579154, 180.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18762, 1738.464233, -1133.626586, 23.115936, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18762, 1731.473999, -1133.626586, 23.115936, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18762, 1733.474487, -1133.626586, 26.105947, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18762, 1736.465698, -1133.626586, 26.105947, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19376, 1750.940917, -1114.870239, 35.043411, 0.000000, 90.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19376, 1750.940917, -1124.499389, 35.043411, 0.000000, 90.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19376, 1750.940917, -1128.310913, 35.043411, 0.000000, 90.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19376, 1743.964477, -1114.870239, 35.033420, 0.000000, 90.000030, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19376, 1743.964477, -1124.499389, 35.033420, 0.000000, 90.000030, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19376, 1743.964477, -1128.310913, 35.033420, 0.000000, 90.000030, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19376, 1729.951660, -1114.870239, 32.433441, 0.000000, 90.000030, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19376, 1729.951660, -1124.499389, 32.433441, 0.000000, 90.000030, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19376, 1729.951660, -1128.310913, 32.433441, 0.000000, 90.000030, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19376, 1719.502197, -1114.870239, 32.433441, 0.000000, 90.000038, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19376, 1719.502197, -1124.499389, 32.433441, 0.000000, 90.000038, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19376, 1719.502197, -1128.310913, 32.433441, 0.000000, 90.000038, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19376, 1711.460815, -1114.870239, 32.433441, 0.000000, 90.000045, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19376, 1711.460815, -1124.499389, 32.433441, 0.000000, 90.000045, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19376, 1711.460815, -1128.310913, 32.433441, 0.000000, 90.000045, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19376, 1733.523559, -1114.870239, 32.433441, 0.000000, 90.000038, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19376, 1733.523559, -1124.499389, 32.433441, 0.000000, 90.000038, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19376, 1733.523559, -1128.310913, 32.433441, 0.000000, 90.000038, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1739.176879, -1115.066650, 32.465976, 0.000007, 180.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 4821, "union_las", "lasunion995", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1739.186889, -1125.066528, 32.475975, 0.000007, 180.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 4821, "union_las", "lasunion995", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1739.176879, -1128.096923, 32.465976, 0.000007, 180.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 4821, "union_las", "lasunion995", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1733.706054, -1133.587768, 32.465976, 0.000000, 180.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 4821, "union_las", "lasunion995", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1733.706054, -1133.577758, 29.085964, 0.000000, 180.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 4821, "union_las", "lasunion995", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19375, 1739.178344, -1115.072875, 28.516977, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 2755, "ab_dojowall", "ab_trellis", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18980, 1767.257324, -1102.874877, 22.789121, -0.000007, 179.999984, -89.999984, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 3820, "boxhses_sfsx", "stonewall_la", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18980, 1782.398437, -1102.914916, 22.789121, -0.000014, 179.999984, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 3820, "boxhses_sfsx", "stonewall_la", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18980, 1767.247680, -1097.632812, 22.789121, 180.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 3820, "boxhses_sfsx", "stonewall_la", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18980, 1782.368164, -1097.632812, 22.789121, 180.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 3820, "boxhses_sfsx", "stonewall_la", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18980, 1774.546752, -1102.914916, 22.789121, -0.000014, 179.999984, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 3820, "boxhses_sfsx", "stonewall_la", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1780.437500, -1126.828979, 32.139354, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1772.883544, -1099.792358, 22.603719, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 4829, "airport_las", "Grass_128HV", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1777.734741, -1099.792358, 22.613719, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 4829, "airport_las", "Grass_128HV", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1782.715698, -1099.792358, 25.983728, 180.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 1677, "wshxrefhse2", "tilestone256", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1755.657470, -1132.805664, 24.335979, 0.000014, 90.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19375, 1711.528686, -1129.230224, 23.031320, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 12853, "cunte_gas01", "sw_floor1", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19375, 1711.528686, -1129.230224, 28.961313, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1731.387329, -1109.937866, 27.405994, 180.000000, 180.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1735.607788, -1109.897827, 24.366050, 180.000000, -108.400062, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10988, "mission2_sfse", "ws_fancywallpink", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1728.758300, -1133.549682, 27.445989, 0.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 3629, "arprtxxref_las", "grn_window2_16", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1728.758300, -1133.629760, 27.445989, 0.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 3629, "arprtxxref_las", "grn_window2_16", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1721.758300, -1133.629760, 27.445989, 0.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 3629, "arprtxxref_las", "grn_window2_16", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1714.758300, -1133.629760, 27.445989, 0.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 3629, "arprtxxref_las", "grn_window2_16", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1707.758300, -1133.629760, 27.445989, 0.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 3629, "arprtxxref_las", "grn_window2_16", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1721.758300, -1133.549926, 27.445989, 0.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 3629, "arprtxxref_las", "grn_window2_16", 0x00000000);
    HSPTLXTA = CreateDynamicObject(970, 1771.934448, -1102.933837, 27.759349, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 17555, "eastbeach3c_lae2", "compfence3_LAe", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 3440, "airportpillar", "metalic_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(970, 1769.843994, -1102.953857, 27.759349, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 17555, "eastbeach3c_lae2", "compfence3_LAe", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 3440, "airportpillar", "metalic_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(970, 1767.213867, -1100.273559, 27.759349, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 17555, "eastbeach3c_lae2", "compfence3_LAe", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 3440, "airportpillar", "metalic_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(970, 1777.154541, -1102.933837, 27.759349, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 17555, "eastbeach3c_lae2", "compfence3_LAe", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 3440, "airportpillar", "metalic_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(970, 1779.734985, -1102.923828, 27.759349, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 17555, "eastbeach3c_lae2", "compfence3_LAe", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 3440, "airportpillar", "metalic_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19482, 1747.695190, -1135.353027, 28.822149, 0.000000, 0.000159, 269.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(HSPTLXTA, 0, "RS KOTA ATHERLIFE", 130, "Palatino Linotype", 70, 1, 0xFFFFFFFF, 0x00000000, 1);
    HSPTLXTA = CreateDynamicObject(19482, 1744.434936, -1135.353027, 28.922151, 0.000000, 0.000159, 269.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(HSPTLXTA, 0, "+", 130, "Palatino Linotype", 150, 1, 0xFFFF0000, 0x00000000, 1);
    HSPTLXTA = CreateDynamicObject(19482, 1750.974731, -1135.353027, 28.922151, 0.000000, 0.000159, 269.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(HSPTLXTA, 0, "+", 130, "Palatino Linotype", 150, 1, 0xFFFF0000, 0x00000000, 1);
    HSPTLXTA = CreateDynamicObject(18762, 1734.910888, -1134.062011, 30.651468, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 1975, "texttest", "kb_red", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18762, 1734.910888, -1134.062011, 30.651468, 0.000000, 90.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 1975, "texttest", "kb_red", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18762, 1734.910888, -1133.121215, 29.481454, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 1975, "texttest", "kb_red", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18762, 1734.910888, -1133.121215, 29.481454, 0.000000, 90.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 1975, "texttest", "kb_red", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19482, 1731.874389, -1114.703491, 23.612199, 0.000000, 0.000159, 269.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(HSPTLXTA, 0, "RESEPSIONIS", 130, "Arial", 50, 1, 0xFFFFFFFF, 0x00000000, 1);
    HSPTLXTA = CreateDynamicObject(19482, 1729.174072, -1110.472412, 27.442205, -0.000007, 0.000157, -90.000038, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(HSPTLXTA, 0, "+", 130, "Arial", 199, 1, 0xFFFF0000, 0x00000000, 1);
    HSPTLXTA = CreateDynamicObject(19482, 1732.243896, -1110.472412, 27.692203, -0.000007, 0.000157, -90.000038, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(HSPTLXTA, 0, "RUMAH SAKIT", 130, "Arial", 70, 1, 0xFFFFFFFF, 0x00000000, 1);
    HSPTLXTA = CreateDynamicObject(19482, 1732.294311, -1110.472412, 26.942216, -0.000007, 0.000157, -90.000038, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(HSPTLXTA, 0, "ATHERLIFE", 130, "Arial", 80, 1, 0xFFFFFFFF, 0x00000000, 1);
    HSPTLXTA = CreateDynamicObject(19482, 1735.283935, -1110.472412, 27.442205, -0.000007, 0.000157, -90.000038, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(HSPTLXTA, 0, "+", 130, "Arial", 199, 1, 0xFFFF0000, 0x00000000, 1);
    HSPTLXTA = CreateDynamicObject(19482, 1734.873535, -1110.472412, 28.342214, -0.000007, 0.000157, -90.000038, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(HSPTLXTA, 0, "+", 130, "Arial", 150, 1, 0xFFFF0000, 0x00000000, 1);
    HSPTLXTA = CreateDynamicObject(19482, 1729.633178, -1110.472412, 28.342214, -0.000007, 0.000157, -90.000038, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(HSPTLXTA, 0, "+", 130, "Arial", 150, 1, 0xFFFF0000, 0x00000000, 1);
    HSPTLXTA = CreateDynamicObject(19482, 1729.442993, -1110.472412, 26.412210, -0.000007, 0.000157, -90.000038, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(HSPTLXTA, 0, "+", 130, "Arial", 100, 1, 0xFFFF0000, 0x00000000, 1);
    HSPTLXTA = CreateDynamicObject(19482, 1735.263671, -1110.472412, 26.412210, -0.000007, 0.000157, -90.000038, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(HSPTLXTA, 0, "+", 130, "Arial", 100, 1, 0xFFFF0000, 0x00000000, 1);
    HSPTLXTA = CreateDynamicObject(19482, 1720.382080, -1112.353759, 23.782203, 0.000000, 0.000159, 539.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(HSPTLXTA, 0, "LOKET LAYANAN", 130, "Arial", 35, 1, 0xFFFFFFFF, 0x00000000, 1);
    HSPTLXTA = CreateDynamicObject(19482, 1720.382080, -1112.353759, 23.472198, 0.000000, 0.000159, 539.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(HSPTLXTA, 0, "MOHON BERSABAR!", 130, "Arial", 25, 1, 0xFFF00000, 0x00000000, 1);
    HSPTLXTA = CreateDynamicObject(18766, 1741.107543, -1117.507568, 27.405994, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1741.107543, -1112.507202, 27.405994, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1741.107543, -1117.507568, 30.126007, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1741.107543, -1112.507202, 30.126007, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1746.587768, -1124.510131, 32.525978, 0.000000, 180.000000, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1751.189819, -1124.510131, 32.525978, 0.000000, 180.000000, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1741.107543, -1122.506958, 27.405994, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1741.107543, -1122.506958, 30.126007, 0.000014, 90.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2011, 1731.212890, -1127.741210, 23.107252, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 2, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2011, 1722.172241, -1127.741210, 23.107252, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 2, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2011, 1731.212890, -1126.790893, 23.107252, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 2, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2011, 1722.172241, -1126.790893, 23.107252, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 2, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19375, 1746.396484, -1124.903808, 28.336984, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 17588, "lae2coast_alpha", "plainglass", 0x00000000);
    HSPTLXTA = CreateDynamicObject(1499, 1755.193115, -1124.886474, 23.107053, 0.000075, 0.000029, 179.999572, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 3440, "airportpillar", "metalic_64", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 1419, "break_fence3", "CJ_FRAME_Glass", 0x00000000);
    HSPTLXTA = CreateDynamicObject(1499, 1752.183227, -1124.906494, 23.107053, -0.000075, -0.000029, -0.000060, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 3440, "airportpillar", "metalic_64", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 1419, "break_fence3", "CJ_FRAME_Glass", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18762, 1751.712402, -1124.914306, 23.117244, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18762, 1755.662719, -1124.914306, 23.117244, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18762, 1753.713134, -1124.914306, 26.117254, 0.000000, 90.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19354, 1753.640136, -1124.901489, 28.341316, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 17588, "lae2coast_alpha", "plainglass", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18762, 1751.712402, -1124.914306, 27.527271, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18762, 1755.662719, -1124.914306, 27.527271, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18762, 1753.713134, -1124.914306, 29.547258, 0.000000, 90.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18762, 1748.753662, -1124.914306, 29.547258, 0.000000, 90.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18762, 1744.102783, -1124.914306, 29.547258, 0.000000, 90.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18762, 1748.753662, -1124.914306, 23.577238, 0.000000, 90.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18762, 1744.102783, -1124.914306, 23.577238, 0.000000, 90.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18762, 1742.100708, -1124.914306, 23.117244, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18762, 1742.100708, -1124.914306, 27.527271, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19426, 1708.410156, -1132.830078, 24.857250, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 16639, "a51_labs", "dam_terazzo", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19426, 1708.410156, -1125.159912, 24.857250, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 16639, "a51_labs", "dam_terazzo", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19426, 1707.079956, -1130.419555, 24.857250, 0.000000, 0.000007, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 16639, "a51_labs", "dam_terazzo", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19426, 1707.079956, -1127.877563, 24.857250, 0.000000, 0.000007, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 16639, "a51_labs", "dam_terazzo", 0x00000000);
    HSPTLXTA = CreateDynamicObject(11729, 1716.315307, -1127.973999, 23.117258, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 3440, "airportpillar", "metalic_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(11729, 1716.315307, -1128.643920, 23.117258, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 3440, "airportpillar", "metalic_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(11729, 1716.315307, -1129.304077, 23.117258, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 3440, "airportpillar", "metalic_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(11729, 1716.315307, -1129.964721, 23.117258, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 3440, "airportpillar", "metalic_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(11729, 1716.315307, -1130.624877, 23.117258, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 3440, "airportpillar", "metalic_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(11729, 1716.315307, -1131.285278, 23.117258, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 3440, "airportpillar", "metalic_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(11729, 1716.315307, -1131.944946, 23.117258, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 3440, "airportpillar", "metalic_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19353, 1712.336547, -1133.187866, 25.627254, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19353, 1712.336547, -1124.778198, 25.627254, 90.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19940, 1711.579956, -1124.832641, 24.017265, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19940, 1713.090087, -1124.832641, 24.017265, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19940, 1711.579956, -1124.832641, 27.217273, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19940, 1713.090087, -1124.832641, 27.217273, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19940, 1710.583496, -1124.832641, 26.232278, 89.999992, 154.471191, -64.471221, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19940, 1710.583496, -1124.832641, 25.002147, 89.999992, 154.471191, -64.471221, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19940, 1714.094970, -1124.832641, 26.232278, 89.999992, 173.225967, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19940, 1714.094970, -1124.832641, 25.002147, 89.999992, 173.225967, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19940, 1711.579956, -1133.144165, 24.017265, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19940, 1713.090087, -1133.144165, 24.017265, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19940, 1711.579956, -1133.144165, 27.217273, 0.000022, 0.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19940, 1713.090087, -1133.144165, 27.217273, 0.000022, 0.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19940, 1710.583496, -1133.144165, 26.232278, 89.999992, 173.225967, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19940, 1710.583496, -1133.144165, 25.002147, 89.999992, 173.225967, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19940, 1714.094970, -1133.144165, 26.232278, 89.999992, 178.299102, -88.299072, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19940, 1714.094970, -1133.144165, 25.002147, 89.999992, 178.299102, -88.299072, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2773, 1722.090820, -1115.792480, 23.627258, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 1, 10988, "mission2_sfse", "ws_fancywallpink", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2773, 1722.090820, -1118.322387, 23.627258, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 1, 10988, "mission2_sfse", "ws_fancywallpink", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2773, 1722.090820, -1120.842773, 23.627258, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 1, 10988, "mission2_sfse", "ws_fancywallpink", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2773, 1723.651611, -1121.822875, 23.627258, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 1, 10988, "mission2_sfse", "ws_fancywallpink", 0x00000000);
    HSPTLXTA = CreateDynamicObject(1209, 1713.346801, -1123.550903, 23.107254, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 2, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19375, 1750.425659, -1115.923461, 28.326978, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 17588, "lae2coast_alpha", "plainglass", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1746.567382, -1121.547119, 30.615978, 89.999992, 270.000000, -90.000022, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1746.567382, -1116.546752, 30.615978, 89.999992, 270.000000, -90.000022, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1746.567382, -1112.536376, 30.615978, 89.999992, 270.000000, -90.000022, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1751.378540, -1121.547119, 30.615978, 89.999992, 270.000000, -89.999992, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1751.378540, -1116.546752, 30.615978, 89.999992, 270.000000, -89.999992, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1751.378540, -1112.536376, 30.615978, 89.999992, 270.000000, -89.999992, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2662, 1717.599487, -1125.637573, 26.127265, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(HSPTLXTA, 0, "TOILET", 130, "Arial", 65, 1, 0xFFFFFFFF, 0xFF000000, 1);
    HSPTLXTA = CreateDynamicObject(2773, 1729.810668, -1117.082397, 23.627258, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 1, 10988, "mission2_sfse", "ws_fancywallpink", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2773, 1734.361450, -1117.082397, 23.627258, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 1, 10988, "mission2_sfse", "ws_fancywallpink", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2773, 1732.081420, -1117.082397, 23.627258, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 1, 10988, "mission2_sfse", "ws_fancywallpink", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1775.447875, -1126.828979, 32.139354, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1780.437500, -1108.829711, 32.139354, 0.000000, 90.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1775.447875, -1108.829711, 32.139354, 0.000000, 90.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1773.437011, -1122.338256, 32.629356, 0.000000, 180.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1773.437011, -1114.248291, 32.629356, 0.000000, 180.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19385, 1773.256958, -1124.870849, 28.869377, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19385, 1766.727050, -1124.870849, 28.869377, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18762, 1773.459716, -1122.851318, 29.579359, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19446, 1773.465820, -1117.599121, 28.959344, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 17588, "lae2coast_alpha", "plainglass", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19354, 1773.462524, -1111.194824, 28.959352, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 17588, "lae2coast_alpha", "plainglass", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19376, 1743.964477, -1128.310913, 35.033420, 0.000000, 90.000030, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18762, 1773.459716, -1109.090209, 29.579359, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18762, 1734.973999, -1133.626586, 23.115936, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18762, 1766.519165, -1122.850830, 29.579359, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18762, 1766.519165, -1109.089721, 29.579359, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19446, 1766.515136, -1117.599121, 28.959344, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 17588, "lae2coast_alpha", "plainglass", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19354, 1766.511840, -1111.194824, 28.959352, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 17588, "lae2coast_alpha", "plainglass", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1766.526000, -1122.338256, 32.629356, 0.000014, 180.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1766.526000, -1114.248291, 32.629356, 0.000014, 180.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1764.517944, -1108.829711, 32.139354, 0.000000, 90.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1763.637207, -1108.829711, 32.139354, 0.000000, 90.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18762, 1767.259277, -1108.591308, 26.129360, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18762, 1767.259277, -1121.571655, 26.129360, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18762, 1783.628662, -1096.611938, 23.339353, 0.000000, 180.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18762, 1784.219238, -1096.611938, 23.339353, 0.000000, 180.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2662, 1753.670166, -1125.437377, 26.087265, 0.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(HSPTLXTA, 0, "LF", 130, "Arial", 65, 1, 0xFF000000, 0xFFFFFFFF, 1);
    HSPTLXTA = CreateDynamicObject(2662, 1753.670166, -1125.447387, 25.887264, 0.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(HSPTLXTA, 0, "(LABORATORIUM & FARMASI)", 130, "Arial", 35, 1, 0xFF000000, 0x00000000, 1);
    HSPTLXTA = CreateDynamicObject(2662, 1766.686523, -1121.582641, 26.187267, -0.000014, 0.000014, -89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(HSPTLXTA, 0, "IGD", 130, "Arial", 65, 1, 0xFFFF0000, 0xFF000000, 1);
    HSPTLXTA = CreateDynamicObject(2662, 1766.676513, -1121.582641, 25.987266, -0.000014, 0.000014, -89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(HSPTLXTA, 0, "(INSTALASI GAWAT DARURAT)", 130, "Arial", 35, 1, 0xFFFFFFFF, 0x00000000, 1);
    HSPTLXTA = CreateDynamicObject(2662, 1766.686523, -1108.611572, 26.187267, -0.000022, 0.000014, -89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(HSPTLXTA, 0, "RO", 130, "Arial", 65, 1, 0xFFFF0000, 0xFF000000, 1);
    HSPTLXTA = CreateDynamicObject(2662, 1766.676513, -1108.611572, 25.987266, -0.000022, 0.000014, -89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(HSPTLXTA, 0, "(RUANG OPERASI)", 130, "Arial", 35, 1, 0xFFFFFFFF, 0x00000000, 1);
    HSPTLXTA = CreateDynamicObject(2662, 1772.897094, -1124.853271, 30.687273, -0.000051, 0.000014, -89.999839, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(HSPTLXTA, 0, "RAP", 130, "Arial", 65, 1, 0xFFFF0000, 0xFF000000, 1);
    HSPTLXTA = CreateDynamicObject(2662, 1772.887084, -1124.853271, 30.487272, -0.000051, 0.000014, -89.999839, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(HSPTLXTA, 0, "(RUANG ANGGOTA PARAMEDIS)", 130, "Arial", 35, 1, 0xFFFFFFFF, 0x00000000, 1);
    HSPTLXTA = CreateDynamicObject(2662, 1767.067993, -1124.853271, 30.687273, -0.000037, 0.000014, 90.000053, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(HSPTLXTA, 0, "RUI", 130, "Arial", 65, 1, 0xFFFF0000, 0xFF000000, 1);
    HSPTLXTA = CreateDynamicObject(2662, 1767.078002, -1124.853271, 30.487272, -0.000037, 0.000014, 90.000053, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(HSPTLXTA, 0, "(RUANG RAWAT INAP)", 130, "Arial", 35, 1, 0xFFFFFFFF, 0x00000000, 1);
    HSPTLXTA = CreateDynamicObject(2662, 1758.595947, -1111.491821, 29.887266, -0.000014, 0.000014, 0.000045, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(HSPTLXTA, 0, "LANTAI 2", 130, "Monotype Corsiva", 50, 1, 0xFF000000, 0x00000000, 1);
    HSPTLXTA = CreateDynamicObject(2662, 1786.246582, -1115.881713, 29.887266, -0.000014, 0.000014, 180.000045, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(HSPTLXTA, 0, "EMERGENCY", 130, "Monotype Corsiva", 50, 1, 0xFF000000, 0x00000000, 1);
    HSPTLXTA = CreateDynamicObject(2662, 1786.216552, -1097.230834, 25.817249, -0.000014, 0.000014, 360.000061, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(HSPTLXTA, 0, "AMBULANS", 130, "Monotype Corsiva", 50, 1, 0xFF000000, 0x00000000, 1);
    HSPTLXTA = CreateDynamicObject(2558, 1771.415161, -1108.844726, 23.601306, -0.000014, 0.000000, -89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 3440, "airportpillar", "metalic_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2558, 1771.415161, -1106.955200, 23.601306, -0.000014, 0.000000, -89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 3440, "airportpillar", "metalic_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2558, 1771.415161, -1105.075805, 23.601306, -0.000014, 0.000000, -89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 3440, "airportpillar", "metalic_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2558, 1772.205932, -1106.086669, 23.601306, 0.000022, 0.000000, 89.999870, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 3440, "airportpillar", "metalic_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2558, 1772.205932, -1107.976196, 23.601306, 0.000022, 0.000000, 89.999870, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 3440, "airportpillar", "metalic_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2558, 1772.205932, -1109.855590, 23.601306, 0.000022, 0.000000, 89.999870, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 3440, "airportpillar", "metalic_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(16101, 1771.806884, -1104.612670, 16.261318, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(16101, 1771.756835, -1110.283325, 16.261318, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19089, 1771.809692, -1103.356933, 24.927244, 0.000007, 90.000045, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19089, 1771.809692, -1103.356933, 26.647256, 0.000007, 90.000045, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19089, 1771.809692, -1103.356933, 25.757251, 0.000007, 90.000045, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(16101, 1771.806884, -1103.501708, 16.261318, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19919, 1782.501586, -1116.290527, 23.101312, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19919, 1782.501586, -1119.300537, 23.101312, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19919, 1782.501586, -1113.299438, 23.101312, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19919, 1782.501586, -1122.299438, 23.101312, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19919, 1782.501586, -1125.299072, 23.101312, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19919, 1782.501586, -1128.298461, 23.101312, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19919, 1782.501586, -1131.298583, 23.101312, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2161, 1782.928833, -1115.838500, 27.209346, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2161, 1782.938842, -1115.838500, 28.529342, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2191, 1782.385131, -1117.144775, 27.209348, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2164, 1780.592041, -1126.288818, 27.209348, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 2, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2164, 1782.912963, -1123.278564, 27.209348, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 2, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2611, 1782.844970, -1120.388671, 28.919349, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 3440, "airportpillar", "metalic_64", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 3, 10765, "airportgnd_sfse", "white", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19893, 1780.156005, -1119.773925, 27.995727, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 1, 8035, "vgsshospshop", "hosp_sign01c", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19893, 1777.005859, -1114.505126, 27.995727, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 1, 8035, "vgsshospshop", "hosp_sign01c", 0x00000000);
    HSPTLXTA = CreateDynamicObject(1726, 1764.311523, -1125.781982, 27.209348, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 1, 10988, "mission2_sfse", "ws_fancywallpink", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19919, 1761.931152, -1121.780883, 27.221309, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19919, 1761.931152, -1117.290771, 27.221309, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19919, 1761.931152, -1112.790405, 27.221309, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2011, 1765.548828, -1122.838867, 27.213623, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 2, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(1713, 1761.472778, -1127.818359, 27.165927, 0.000022, 0.000000, 359.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 2, 10988, "mission2_sfse", "ws_fancywallpink", 0x00000000);
    HSPTLXTA = CreateDynamicObject(1713, 1763.123168, -1131.779296, 27.165927, 0.000022, 0.000000, 539.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 2, 10988, "mission2_sfse", "ws_fancywallpink", 0x00000000);
    HSPTLXTA = CreateDynamicObject(1713, 1757.652221, -1130.639282, 27.165927, 0.000022, 0.000000, 809.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 2, 10988, "mission2_sfse", "ws_fancywallpink", 0x00000000);
    HSPTLXTA = CreateDynamicObject(1209, 1761.430297, -1105.005126, 27.199348, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 2, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2094, 1775.856079, -1130.784057, 23.101316, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 1355, "break_s_bins", "CJ_WOOD_DARK", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2094, 1774.835571, -1112.826416, 23.101316, 0.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 1355, "break_s_bins", "CJ_WOOD_DARK", 0x00000000);
    HSPTLXTA = CreateDynamicObject(1820, 1773.235473, -1132.247436, 23.101316, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 14650, "ab_trukstpc", "sa_wood08_128", 0x00000000);
    HSPTLXTA = CreateDynamicObject(1820, 1776.455688, -1112.391235, 23.101316, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 14650, "ab_trukstpc", "sa_wood08_128", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19445, 1775.235473, -1126.479858, 23.031320, 0.000000, 90.000030, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18757, "vcinteriors", "dts_elevator_carpet3", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19445, 1775.235473, -1117.201049, 23.041320, 0.000000, 90.000030, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18757, "vcinteriors", "dts_elevator_carpet3", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19445, 1768.673950, -1121.581420, 23.041320, 0.000000, 90.000030, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18757, "vcinteriors", "dts_elevator_carpet3", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2011, 1760.624633, -1127.741210, 23.107252, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 2, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2662, 1756.055541, -1128.732788, 26.877267, -0.000022, 0.000014, -90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(HSPTLXTA, 0, "PERHATIAN!", 130, "Arial", 65, 1, 0xFFFF0000, 0xFF000000, 1);
    HSPTLXTA = CreateDynamicObject(2662, 1756.045532, -1128.732788, 26.677267, -0.000022, 0.000014, -90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(HSPTLXTA, 0, "DILARANG BERISIK", 130, "Arial", 35, 1, 0xFFFFFFFF, 0x00000000, 1);
    HSPTLXTA = CreateDynamicObject(18981, 1776.221313, -1108.623901, 35.791339, 180.000015, 90.000000, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18981, 1768.651489, -1108.623901, 35.791339, 180.000015, 90.000000, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18981, 1768.651489, -1121.605224, 35.791339, 180.000015, 90.000000, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18981, 1776.211914, -1121.605224, 35.791339, 180.000015, 90.000000, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18981, 1753.031738, -1121.605224, 35.791339, 180.000015, 90.000000, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18981, 1728.052734, -1121.605224, 35.791339, 180.000015, 90.000000, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18981, 1717.813354, -1121.605224, 35.791339, 180.000015, 90.000000, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18981, 1776.221313, -1108.623901, 36.621334, -0.000014, -89.999984, -0.000075, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18981, 1768.651489, -1108.623901, 36.621334, -0.000014, -89.999984, -0.000075, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18981, 1768.651489, -1121.605224, 36.621334, -0.000014, -89.999984, -0.000075, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18981, 1776.211914, -1121.605224, 36.621334, -0.000014, -89.999984, -0.000075, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18981, 1753.031738, -1121.605224, 36.621334, -0.000014, -89.999984, -0.000075, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18981, 1728.052734, -1121.605224, 36.621334, -0.000014, -89.999984, -0.000075, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18981, 1717.813354, -1121.605224, 36.621334, -0.000014, -89.999984, -0.000075, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 8130, "vgsschurch", "vgschapelwall01_64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(3934, 1771.768676, -1121.565795, 37.132274, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18764, 1769.157104, -1103.475341, 39.583515, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 4821, "union_las", "lasunion995", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18764, 1774.136962, -1103.475341, 39.583515, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 4821, "union_las", "lasunion995", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19353, 1774.705078, -1105.897827, 38.837341, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 6522, "cuntclub_law2", "marinawindow1_256", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19353, 1772.274536, -1105.887817, 38.837341, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 6522, "cuntclub_law2", "marinawindow1_256", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19353, 1774.224609, -1101.057739, 38.837341, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 6522, "cuntclub_law2", "marinawindow1_256", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19353, 1769.083862, -1101.057739, 38.837341, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 6522, "cuntclub_law2", "marinawindow1_256", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2773, 1767.230712, -1107.291992, 37.637268, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 1, 10988, "mission2_sfse", "ws_fancywallpink", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2773, 1770.620849, -1107.291992, 37.637268, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 1, 10988, "mission2_sfse", "ws_fancywallpink", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2773, 1759.652954, -1098.331909, 27.697250, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 1, 10988, "mission2_sfse", "ws_fancywallpink", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2773, 1764.112182, -1098.331909, 27.697250, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 1, 10988, "mission2_sfse", "ws_fancywallpink", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2662, 1761.870239, -1097.157104, 30.667276, 0.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(HSPTLXTA, 0, "ELEVATOR", 130, "Arial", 65, 1, 0xFF000000, 0xFFFFFFFF, 1);
    HSPTLXTA = CreateDynamicObject(2662, 1761.870239, -1097.177124, 30.477272, 0.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(HSPTLXTA, 0, "(HELIPAD)", 130, "Arial", 45, 1, 0xFF000000, 0x00000000, 1);
    HSPTLXTA = CreateDynamicObject(18981, 1801.212280, -1103.202392, 22.591327, 180.000015, 90.000000, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18800, "mroadhelix1", "road1-3", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18981, 1801.212280, -1121.593505, 22.601327, 180.000015, 90.000000, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18800, "mroadhelix1", "road1-3", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18765, 1791.210327, -1115.881713, 23.101324, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 4821, "union_las", "lasunion995", 0x00000000);
    HSPTLXTA = CreateDynamicObject(17951, 1793.686645, -1115.888549, 24.831319, 0.000000, 0.000000, -0.499998, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 9515, "bigboxtemp1", "garagedoor5_law", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18980, 1813.214111, -1121.595703, 23.561325, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18980, 1801.203002, -1133.596313, 23.561325, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18980, 1813.214111, -1103.194824, 23.561325, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18762, 1793.879638, -1119.465454, 24.131328, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18762, 1793.879638, -1112.295410, 24.131328, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18762, 1793.879638, -1114.295898, 27.111326, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18762, 1793.879638, -1117.466430, 27.111326, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19445, 1808.902709, -1098.329711, 21.371318, 0.000012, 0.000006, 65.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19445, 1808.902709, -1105.329711, 21.371318, 0.000012, 0.000006, 65.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19445, 1808.902709, -1112.329711, 21.371318, 0.000012, 0.000006, 65.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19445, 1808.902709, -1119.329711, 21.371318, 0.000012, 0.000006, 65.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19445, 1808.902709, -1126.329711, 21.371318, 0.000012, 0.000006, 65.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19445, 1788.755004, -1106.078857, 21.351318, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19445, 1788.755004, -1101.908325, 21.351318, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19445, 1792.575317, -1097.178100, 21.351318, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19445, 1792.575317, -1110.798217, 21.351318, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19445, 1792.575317, -1104.107910, 21.351318, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1735.031616, -1137.527587, 22.585247, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18800, "mroadhelix1", "road1-3", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1727.531860, -1140.027954, 22.585247, 90.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18800, "mroadhelix1", "road1-3", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1717.532348, -1140.027954, 22.585247, 90.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18800, "mroadhelix1", "road1-3", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1708.552368, -1140.037841, 22.595247, 90.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18800, "mroadhelix1", "road1-3", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19482, 1795.784423, -1107.427246, 23.101350, 0.000000, 270.000152, -0.000074, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(HSPTLXTA, 0, "NO PARKING", 130, "Arial", 55, 1, 0xFFFFFFFF, 0x00000000, 1);
    HSPTLXTA = CreateDynamicObject(19482, 1795.784423, -1100.676635, 23.101350, 0.000000, 270.000152, -0.000074, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(HSPTLXTA, 0, "NO PARKING", 130, "Arial", 55, 1, 0xFFFFFFFF, 0x00000000, 1);
    HSPTLXTA = CreateDynamicObject(19384, 1744.039062, -1115.933471, 24.831308, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 17588, "lae2coast_alpha", "plainglass", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18980, 1755.673583, -1115.985961, 23.101312, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18980, 1742.103271, -1115.985961, 23.101312, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19354, 1744.019531, -1115.924316, 28.341316, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 17588, "lae2coast_alpha", "plainglass", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19926, 1744.296630, -1123.466186, 23.087444, 0.000006, 0.000048, -0.000082, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 18646, "matcolours", "grey-80-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19926, 1744.296630, -1121.545776, 23.087444, 0.000006, 0.000048, -0.000082, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 18646, "matcolours", "grey-80-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19926, 1744.296630, -1119.635986, 23.087444, 0.000006, 0.000048, -0.000082, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 18646, "matcolours", "grey-80-percent", 0x00000000);
    HSPTLXTA = CreateDynamicObject(18766, 1741.298461, -1120.479003, 25.163099, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 4821, "union_las", "lasunion995", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19482, 1744.734985, -1120.783325, 23.522182, 0.000000, 0.000159, 359.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(HSPTLXTA, 0, "FARMASI", 130, "Arial", 50, 1, 0xFFFFFFFF, 0x00000000, 1);
    HSPTLXTA = CreateDynamicObject(19893, 1744.197753, -1121.546630, 24.000730, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 1, 19894, "laptopsamp1", "laptopscreen2", 0x00000000);
    HSPTLXTA = CreateDynamicObject(19893, 1744.197753, -1119.646118, 24.000730, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 1, 19894, "laptopsamp1", "laptopscreen2", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2773, 1745.871582, -1122.422851, 23.627258, 0.000000, 0.000007, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 1, 10988, "mission2_sfse", "ws_fancywallpink", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2773, 1745.871582, -1120.622802, 23.627258, 0.000000, 0.000007, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 1, 10988, "mission2_sfse", "ws_fancywallpink", 0x00000000);
    HSPTLXTA = CreateDynamicObject(2773, 1745.871582, -1118.662109, 23.627258, 0.000000, 0.000007, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 1, 10988, "mission2_sfse", "ws_fancywallpink", 0x00000000);
    HSPTLXTA = CreateDynamicObject(1713, 1755.603393, -1118.008544, 23.085939, 0.000022, 0.000000, 269.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 2, 10988, "mission2_sfse", "ws_fancywallpink", 0x00000000);
    HSPTLXTA = CreateDynamicObject(1713, 1755.603393, -1121.268432, 23.085939, 0.000022, 0.000000, 269.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(HSPTLXTA, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(HSPTLXTA, 2, 10988, "mission2_sfse", "ws_fancywallpink", 0x00000000);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(19808, 1730.219604, -1113.094970, 23.856388, -0.000020, 0.000000, -179.999893, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2813, 1729.778198, -1113.471923, 23.816381, 0.000020, 0.000000, -0.000114, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2854, 1728.061157, -1113.281250, 23.855352, 0.000020, 0.000000, -0.000114, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1546, 1730.487670, -1114.355712, 24.275396, 0.000000, 0.000020, -90.000038, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1806, 1727.932495, -1111.645385, 23.035377, -0.000020, 0.000000, -179.999893, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1806, 1729.712524, -1111.645385, 23.035377, -0.000020, 0.000000, -179.999893, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19808, 1734.778076, -1113.094848, 23.856388, -0.000020, 0.000000, -179.999832, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2813, 1734.336669, -1113.471801, 23.816381, 0.000020, 0.000000, -0.000236, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2854, 1732.619628, -1113.281127, 23.855352, 0.000020, 0.000000, -0.000236, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1546, 1735.046142, -1114.355590, 24.275396, 0.000000, 0.000020, -90.000114, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1806, 1732.490966, -1111.645263, 23.035377, -0.000020, 0.000000, -179.999832, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1806, 1734.270996, -1111.645263, 23.035377, -0.000020, 0.000000, -179.999832, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2528, 1706.805419, -1131.555664, 23.111825, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2528, 1706.805419, -1129.136108, 23.111825, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2001, 1722.828369, -1110.684326, 22.539100, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2001, 1715.628662, -1110.684326, 23.029146, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2001, 1707.889160, -1110.684326, 23.029146, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19808, 1721.992187, -1112.895629, 23.919982, -0.000006, 0.000007, 89.999961, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2813, 1721.615234, -1112.454223, 23.879974, 0.000006, -0.000007, -90.000038, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2854, 1721.805908, -1110.737182, 23.918945, 0.000006, -0.000007, -90.000038, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1546, 1720.731445, -1113.163696, 24.338989, 0.000007, 0.000006, 179.999786, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1806, 1723.441772, -1110.608520, 23.098968, -0.000006, 0.000007, 89.999961, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1806, 1723.441772, -1112.388549, 23.098968, -0.000006, 0.000007, 89.999961, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18981, 1788.221191, -1120.633544, 22.791315, 0.000014, 0.000000, 179.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2528, 1707.055664, -1126.735839, 23.111825, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19604, 1782.201538, -1099.805053, 25.349330, 270.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(747, 1769.835327, -1098.860351, 22.828176, 0.000000, 0.000000, 45.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(747, 1780.516479, -1100.452148, 22.828176, 0.000000, 0.000000, 135.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(747, 1774.466430, -1099.754394, 22.828176, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19604, 1777.211425, -1099.805053, 23.129327, 360.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(673, 1770.912231, -1099.738159, 21.801052, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(673, 1778.212402, -1099.738159, 22.791061, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1609, 1775.020141, -1099.710083, 24.419073, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19466, 1708.400390, -1132.652709, 23.677249, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19466, 1708.400390, -1132.652709, 25.607244, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19466, 1708.400390, -1125.331420, 23.677249, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19466, 1708.400390, -1125.331420, 25.607244, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2518, 1711.191528, -1125.388183, 23.277257, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2518, 1711.821411, -1125.388183, 23.277257, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2518, 1712.451538, -1125.388183, 23.277257, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2518, 1713.482543, -1132.579589, 23.277257, 0.000000, 0.000000, 179.999847, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2518, 1712.852661, -1132.579589, 23.277257, 0.000000, 0.000000, 179.999847, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2518, 1712.222534, -1132.579589, 23.277257, 0.000000, 0.000000, 179.999847, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1430, 1716.167724, -1127.002929, 23.437253, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(948, 1710.310546, -1125.117065, 23.117258, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(948, 1714.350097, -1125.117065, 23.117258, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(948, 1710.310546, -1132.868041, 23.117258, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(948, 1714.350097, -1132.868041, 23.117258, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(16779, 1711.390380, -1129.063842, 28.867252, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2074, 1711.387817, -1129.055053, 28.167264, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(957, 1715.505371, -1131.949340, 28.827274, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(957, 1707.405639, -1131.949340, 28.827274, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(957, 1715.505371, -1125.878784, 28.827274, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(957, 1707.405639, -1125.878784, 28.827274, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2164, 1726.338500, -1112.196044, 23.101314, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2174, 1725.882812, -1113.956542, 23.101312, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2002, 1725.837280, -1117.631225, 23.101314, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2007, 1725.887695, -1111.200439, 23.101312, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2007, 1725.887695, -1115.960449, 23.101312, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1727, 1725.871337, -1118.419555, 23.101314, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 1728.986206, -1126.673339, 23.597263, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 1724.515869, -1126.673339, 23.597263, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 1724.515747, -1127.824218, 23.597263, 0.000000, 0.000000, -0.000014, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 1728.986083, -1127.824218, 23.597263, 0.000000, 0.000000, -0.000014, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1776, 1710.322631, -1123.338867, 24.187252, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 1710.965087, -1117.023315, 32.400279, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 1722.005493, -1117.023315, 32.400279, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 1732.875488, -1117.023315, 32.400279, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 1724.014160, -1128.403564, 32.400279, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3383, 1777.250366, -1109.451904, 22.981306, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 1761.556762, -1121.772216, 23.597263, 0.000000, 0.000000, 450.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 1761.556762, -1108.701538, 23.597263, 0.000000, 0.000000, 450.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 1732.524047, -1128.403564, 32.400279, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 1748.383300, -1129.173706, 35.010276, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 1775.243652, -1127.533447, 27.100282, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 1775.243652, -1115.592895, 27.100282, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 1775.243408, -1106.882812, 27.100282, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 1778.483398, -1117.742309, 35.150291, 0.000000, 0.000000, 540.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 1763.070922, -1102.721557, 35.150291, 0.000000, 0.000000, 630.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 1776.110961, -1102.721557, 35.150291, 0.000000, 0.000000, 630.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14680, 1771.894653, -1113.184326, 33.477245, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14680, 1771.894653, -1128.644409, 33.477245, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14680, 1771.894653, -1120.704101, 33.477245, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19325, 1756.603271, -1128.864990, 27.668893, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19325, 1758.672485, -1126.734252, 28.928892, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 1760.421020, -1132.722778, 23.807249, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 1751.550415, -1132.722778, 23.807249, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 1744.519897, -1132.722778, 23.807249, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1430, 1748.032714, -1132.653564, 23.437253, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19325, 1758.672485, -1126.734252, 28.928892, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11710, 1758.641113, -1110.438842, 29.926799, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11710, 1786.231811, -1116.959106, 29.946802, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1997, 1768.823120, -1115.566406, 23.101308, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1997, 1768.823120, -1118.566406, 23.101308, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1997, 1768.823120, -1130.566406, 23.101308, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1997, 1768.823120, -1124.566406, 23.101308, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1997, 1768.823120, -1127.566406, 23.101308, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1997, 1768.823120, -1112.566406, 23.101308, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1997, 1781.804199, -1130.566406, 23.101308, 0.000007, 0.000000, 269.999969, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1997, 1781.804199, -1127.566406, 23.101308, 0.000007, 0.000000, 269.999969, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1997, 1781.804199, -1124.566406, 23.101308, 0.000007, 0.000000, 269.999969, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1997, 1781.804199, -1121.566406, 23.101308, 0.000007, 0.000000, 269.999969, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1997, 1781.804199, -1118.566406, 23.101308, 0.000007, 0.000000, 269.999969, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1997, 1781.804199, -1115.566406, 23.101308, 0.000007, 0.000000, 269.999969, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1997, 1781.804199, -1112.566406, 23.101308, 0.000007, 0.000000, 269.999969, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1997, 1781.855468, -1106.556518, 23.101308, 0.000007, 0.000000, 269.999969, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3393, 1776.813598, -1104.074829, 23.091314, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, 1781.974609, -1105.254028, 21.661321, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3389, 1782.408569, -1108.632080, 23.091314, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3388, 1779.091064, -1103.959594, 23.091314, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2804, 1777.277832, -1109.431030, 24.081314, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11716, 1776.885253, -1109.176269, 24.061321, 0.000000, 0.000000, -18.100000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11716, 1777.521972, -1109.385009, 24.061321, 0.000000, 0.000000, 15.600003, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11738, 1777.676269, -1109.950927, 24.101308, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11738, 1776.225830, -1109.950927, 24.101308, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11736, 1776.786621, -1109.674682, 24.071308, 0.000000, 0.000000, -63.500022, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2007, 1768.291381, -1103.942749, 23.091314, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2007, 1768.291381, -1104.432495, 23.091314, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(932, 1768.138183, -1105.404418, 23.091314, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(932, 1768.138183, -1106.154418, 23.091314, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(927, 1782.881347, -1107.245239, 24.361297, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(927, 1776.870849, -1103.444702, 24.931291, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2007, 1774.231933, -1109.762817, 23.091314, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11738, 1774.225708, -1110.000976, 24.531307, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(5171, 1778.196411, -1116.971557, 27.649337, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2146, 1784.311645, -1099.779174, 23.571313, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14680, 1765.454711, -1120.704101, 33.477245, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14680, 1765.454711, -1125.584228, 33.477245, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14680, 1765.454711, -1115.593994, 33.477245, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2146, 1765.343383, -1131.815063, 23.597255, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2146, 1778.964233, -1127.768432, 27.699340, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2575, 1763.606201, -1121.978393, 27.569347, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2575, 1763.606201, -1117.478393, 27.569347, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2575, 1763.606201, -1112.978393, 27.569347, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14867, 1778.303833, -1109.783325, 28.749343, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2380, 1782.816650, -1112.700927, 28.949344, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2007, 1777.043090, -1125.833129, 27.209350, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1726, 1774.190795, -1121.781494, 27.209348, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1726, 1775.311035, -1118.381347, 27.209348, 0.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(948, 1774.270019, -1122.860473, 27.209348, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1827, 1776.322631, -1120.763671, 27.159339, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11738, 1777.042114, -1126.100219, 28.661737, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2854, 1776.298461, -1120.765136, 27.581689, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2002, 1764.837036, -1109.829101, 27.199348, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2002, 1776.046997, -1125.819335, 27.199348, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 1777.866455, -1107.901489, 27.707273, 0.000000, 0.000000, 540.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 1764.066650, -1107.901489, 27.707273, 0.000000, 0.000000, 540.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 1778.966308, -1131.841552, 27.727272, 0.000000, 0.000000, 540.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(932, 1787.188232, -1131.817749, 27.199346, 0.000000, 0.000000, 225.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1776, 1782.500244, -1105.598999, 28.299345, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2002, 1773.897705, -1111.833984, 23.101312, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2002, 1776.848266, -1131.774902, 23.101312, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2821, 1776.919921, -1111.927246, 23.601310, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2821, 1773.739135, -1131.746459, 23.601310, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2252, 1775.376708, -1131.936523, 24.348182, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2252, 1775.376708, -1111.634887, 24.348182, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1430, 1761.571655, -1115.502319, 23.437253, 0.000000, 0.000000, 450.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1569, 1767.459350, -1105.931396, 37.121337, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1569, 1770.459594, -1105.921386, 37.121337, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 1773.515136, -1106.336059, 37.811340, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18757, 1761.880126, -1095.111450, 29.079338, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18756, 1761.874877, -1095.129638, 29.079359, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(994, 1806.429199, -1090.877563, 23.134824, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(994, 1788.758544, -1090.877563, 23.134824, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(994, 1788.828613, -1097.097167, 23.134824, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 1789.069335, -1130.154296, 23.807249, 0.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 1789.069335, -1123.824096, 23.807249, 0.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14680, 1763.464965, -1128.063720, 33.477245, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14680, 1778.215698, -1128.063720, 33.477245, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14680, 1785.585937, -1128.063720, 33.477245, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14680, 1784.395751, -1118.853393, 33.477245, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14680, 1784.395751, -1107.082885, 33.477245, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14680, 1784.395751, -1100.412841, 33.477245, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14680, 1756.796386, -1114.612915, 33.477245, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14680, 1762.026367, -1111.893554, 25.427251, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14680, 1762.026367, -1123.473388, 25.427251, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14680, 1758.686035, -1131.763549, 25.427251, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3394, 1742.149291, -1112.610351, 23.101312, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3386, 1744.422241, -1110.583251, 23.101312, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3395, 1747.980590, -1110.665649, 23.101312, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2007, 1751.682006, -1110.734008, 23.071308, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2007, 1752.682128, -1110.734008, 23.071308, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2007, 1753.682250, -1110.734008, 23.071308, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2007, 1751.682006, -1110.734008, 24.461299, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2007, 1752.682128, -1110.734008, 24.461299, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2007, 1753.682250, -1110.734008, 24.461299, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3394, 1755.559814, -1113.080322, 23.101312, 0.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2066, 1742.118530, -1123.622192, 23.101316, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2066, 1742.118530, -1117.232055, 23.101316, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2163, 1741.817749, -1121.822265, 25.041320, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2163, 1741.817749, -1120.042358, 25.041320, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 1748.764526, -1117.252685, 30.170276, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
}