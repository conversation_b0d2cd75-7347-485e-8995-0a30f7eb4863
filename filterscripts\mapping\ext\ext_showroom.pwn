RemoveShowroomBuilding(playerid)
{
    RemoveBuildingForPlayer(playerid, 673, 404.976, -1329.099, 13.703, 0.250);
    RemoveBuildingForPlayer(playerid, 673, 393.070, -1326.969, 13.703, 0.250);
    RemoveBuildingForPlayer(playerid, 620, 384.992, -1317.030, 13.234, 0.250);
    RemoveBuildingForPlayer(playerid, 673, 372.960, -1308.760, 13.703, 0.250);
    RemoveBuildingForPlayer(playerid, 673, 363.523, -1311.209, 13.703, 0.250);
    RemoveBuildingForPlayer(playerid, 620, 367.820, -1315.410, 13.234, 0.250);
    RemoveBuildingForPlayer(playerid, 620, 371.984, -1318.510, 13.234, 0.250);
    RemoveBuildingForPlayer(playerid, 673, 371.539, -1326.910, 13.703, 0.250);
    RemoveBuildingForPlayer(playerid, 673, 385.906, -1336.520, 13.703, 0.250);
    RemoveBuildingForPlayer(playerid, 620, 395.859, -1323.760, 13.000, 0.250);
    RemoveBuildingForPlayer(playerid, 1308, 399.015, -1334.209, 13.953, 0.250);
    RemoveBuildingForPlayer(playerid, 1267, 378.632, -1330.939, 28.382, 0.250);
    RemoveBuildingForPlayer(playerid, 1261, 378.632, -1330.939, 28.382, 0.250);
    RemoveBuildingForPlayer(playerid, 620, 394.117, -1317.880, 13.234, 0.250);
    RemoveBuildingForPlayer(playerid, 6363, 428.101, -1348.810, 29.257, 0.250);
}

CreateShowroomExt()
{
    new STREAMER_TAG_OBJECT:showroomxts;
    showroomxts = CreateDynamicObject(6356, 381.279998, -1323.170043, 24.493200, 0.000000, 0.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 9514, "711_sfw", "ws_carpark2", 0x00000000);
    showroomxts = CreateDynamicObject(6363, 428.101989, -1348.810058, 29.257799, 0.000000, 0.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 4, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(showroomxts, 5, 19297, "matlights", "invisible", 0x00000000);
    showroomxts = CreateDynamicObject(975, 387.758331, -1341.173217, 13.738912, 0.000003, 270.000000, 30.999998, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    SetDynamicObjectMaterial(showroomxts, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x90FFFFFF);
    SetDynamicObjectMaterial(showroomxts, 2, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(showroomxts, 3, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(showroomxts, 4, 19297, "matlights", "invisible", 0x00000000);
    showroomxts = CreateDynamicObject(975, 385.092437, -1342.775024, 13.738912, 0.000003, 270.000000, 30.999998, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    SetDynamicObjectMaterial(showroomxts, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x90FFFFFF);
    SetDynamicObjectMaterial(showroomxts, 2, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(showroomxts, 3, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(showroomxts, 4, 19297, "matlights", "invisible", 0x00000000);
    showroomxts = CreateDynamicObject(975, 382.156005, -1343.636718, 13.738912, -0.000006, 270.000000, 1.000005, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    SetDynamicObjectMaterial(showroomxts, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x90FFFFFF);
    SetDynamicObjectMaterial(showroomxts, 2, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(showroomxts, 3, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(showroomxts, 4, 19297, "matlights", "invisible", 0x00000000);
    showroomxts = CreateDynamicObject(975, 379.217437, -1342.934814, 13.738912, -0.000015, 270.000000, -28.999982, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    SetDynamicObjectMaterial(showroomxts, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x90FFFFFF);
    SetDynamicObjectMaterial(showroomxts, 2, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(showroomxts, 3, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(showroomxts, 4, 19297, "matlights", "invisible", 0x00000000);
    showroomxts = CreateDynamicObject(975, 376.991821, -1340.842773, 13.738912, -0.000020, 270.000000, -58.999954, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    SetDynamicObjectMaterial(showroomxts, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x90FFFFFF);
    SetDynamicObjectMaterial(showroomxts, 2, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(showroomxts, 3, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(showroomxts, 4, 19297, "matlights", "invisible", 0x00000000);
    showroomxts = CreateDynamicObject(18880, 376.058044, -1339.292358, 18.201099, -0.000003, 179.999984, -148.999938, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 2, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(18880, 389.148681, -1340.341308, 18.201099, -0.000003, 179.999984, -148.999938, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 2, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(18766, 373.436096, -1334.888305, 15.671091, 0.000020, -0.000003, 120.999931, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 7488, "vegasdwntwn1", "villainnwall02_128", 0x00000000);
    showroomxts = CreateDynamicObject(18766, 374.466522, -1327.865722, 15.671091, -0.000003, -0.000020, -149.000030, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 7488, "vegasdwntwn1", "villainnwall02_128", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 390.591979, -1339.421264, 16.411108, -0.000006, 179.999984, -59.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 9583, "bigshap_sfw", "boatfunnel1_128", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 390.591979, -1339.421264, 12.911108, 0.000006, -0.000003, 120.999954, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 9583, "bigshap_sfw", "boatfunnel1_128", 0x00000000);
    showroomxts = CreateDynamicObject(1499, 392.000488, -1338.627441, 13.811102, 0.000003, 0.000014, 30.999998, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    SetDynamicObjectMaterial(showroomxts, 1, 19297, "matlights", "invisible", 0x00000000);
    showroomxts = CreateDynamicObject(18880, 391.900878, -1338.687622, 18.201099, -0.000003, 179.999984, -148.999938, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 2, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    showroomxts = CreateDynamicObject(1499, 394.571411, -1337.061035, 13.811102, -0.000003, -0.000014, -148.999938, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    SetDynamicObjectMaterial(showroomxts, 1, 19297, "matlights", "invisible", 0x00000000);
    showroomxts = CreateDynamicObject(18880, 394.678314, -1337.018798, 18.201099, -0.000003, 179.999984, -148.999938, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 2, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 396.077239, -1336.125488, 16.411108, -0.000014, 179.999984, -58.999984, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 9583, "bigshap_sfw", "boatfunnel1_128", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 396.077239, -1336.125488, 12.911108, 0.000014, -0.000003, 120.999931, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 9583, "bigshap_sfw", "boatfunnel1_128", 0x00000000);
    showroomxts = CreateDynamicObject(975, 398.910095, -1334.472656, 13.738912, 0.000003, 270.000000, 30.999998, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    SetDynamicObjectMaterial(showroomxts, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x90FFFFFF);
    SetDynamicObjectMaterial(showroomxts, 2, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(showroomxts, 3, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(showroomxts, 4, 19297, "matlights", "invisible", 0x00000000);
    showroomxts = CreateDynamicObject(18880, 397.377868, -1335.396728, 18.201099, 0.000003, -179.999984, 31.000030, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 2, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(18880, 400.309234, -1333.635375, 18.201099, -0.000003, 179.999984, -148.999938, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 2, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 401.589080, -1332.813598, 16.411108, -0.000028, 179.999984, -58.999946, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 9583, "bigshap_sfw", "boatfunnel1_128", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 401.589080, -1332.813598, 12.911107, 0.000028, -0.000003, 120.999885, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 9583, "bigshap_sfw", "boatfunnel1_128", 0x00000000);
    showroomxts = CreateDynamicObject(19445, 400.419677, -1327.916381, 12.911107, 0.000003, -0.000020, -149.000091, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 9583, "bigshap_sfw", "boatfunnel1_128", 0x00000000);
    showroomxts = CreateDynamicObject(19445, 389.490081, -1318.887939, 12.911106, -0.000020, 0.000003, -58.999992, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 9583, "bigshap_sfw", "boatfunnel1_128", 0x00000000);
    showroomxts = CreateDynamicObject(19445, 381.235534, -1323.847778, 12.911106, -0.000020, 0.000003, -58.999992, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 9583, "bigshap_sfw", "boatfunnel1_128", 0x00000000);
    showroomxts = CreateDynamicObject(19426, 393.994262, -1317.204223, 12.911107, 0.000003, 0.000020, 30.999954, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 9583, "bigshap_sfw", "boatfunnel1_128", 0x00000000);
    showroomxts = CreateDynamicObject(19445, 400.419677, -1327.916381, 16.411108, 0.000003, -179.999984, 30.999923, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 9583, "bigshap_sfw", "boatfunnel1_128", 0x00000000);
    showroomxts = CreateDynamicObject(19426, 393.994262, -1317.204223, 16.411106, -0.000003, 179.999984, -149.000030, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 9583, "bigshap_sfw", "boatfunnel1_128", 0x00000000);
    showroomxts = CreateDynamicObject(19445, 389.490081, -1318.887939, 16.411106, -0.000014, 179.999969, -58.999946, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 9583, "bigshap_sfw", "boatfunnel1_128", 0x00000000);
    showroomxts = CreateDynamicObject(19445, 381.235534, -1323.847778, 16.411106, -0.000014, 179.999969, -58.999946, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 9583, "bigshap_sfw", "boatfunnel1_128", 0x00000000);
    showroomxts = CreateDynamicObject(18880, 398.005432, -1323.877685, 18.201099, -0.000003, 179.999984, -148.999938, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 2, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(18880, 394.466918, -1317.988647, 18.201099, -0.000003, 179.999984, -148.999938, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 2, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(1508, 396.271606, -1320.922241, 15.390272, 0.000003, 0.000014, 30.999998, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 9515, "bigboxtemp1", "ws_garagedoor3_white", 0x00000000);
    showroomxts = CreateDynamicObject(19866, 394.236694, -1318.332641, 15.671110, 89.999992, 194.440979, -73.440971, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    showroomxts = CreateDynamicObject(19866, 397.584747, -1323.904785, 15.671110, 89.999992, 194.440979, -73.440971, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    showroomxts = CreateDynamicObject(19866, 396.214080, -1320.166748, 18.081104, 0.000003, -89.999969, 30.999971, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    showroomxts = CreateDynamicObject(19866, 396.890014, -1321.289672, 18.082105, 0.000003, -89.999969, 30.999971, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    showroomxts = CreateDynamicObject(19426, 395.477752, -1319.731445, 17.901111, 89.999992, 104.440963, -73.440979, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    showroomxts = CreateDynamicObject(19426, 396.931030, -1322.148315, 17.901111, 89.999992, 104.440963, -73.440979, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    showroomxts = CreateDynamicObject(19426, 387.971313, -1341.367797, 18.257551, 0.000003, 90.000015, 30.999998, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 3306, "cunte_house1", "des_ntwnwall1", 0x00000000);
    showroomxts = CreateDynamicObject(19426, 384.970520, -1343.170776, 18.257551, 0.000003, 90.000015, 30.999998, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 3306, "cunte_house1", "des_ntwnwall1", 0x00000000);
    showroomxts = CreateDynamicObject(19426, 382.123962, -1343.981079, 18.258552, -0.000003, 90.000015, 0.999997, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 3306, "cunte_house1", "des_ntwnwall1", 0x00000000);
    showroomxts = CreateDynamicObject(19426, 381.824584, -1343.985839, 18.259552, -0.000003, 90.000015, 0.999997, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 3306, "cunte_house1", "des_ntwnwall1", 0x00000000);
    showroomxts = CreateDynamicObject(19426, 378.949249, -1343.272705, 18.260553, -0.000009, 90.000007, -28.999990, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 3306, "cunte_house1", "des_ntwnwall1", 0x00000000);
    showroomxts = CreateDynamicObject(19426, 376.820770, -1341.226196, 18.261552, -0.000014, 90.000007, -58.999977, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 3306, "cunte_house1", "des_ntwnwall1", 0x00000000);
    showroomxts = CreateDynamicObject(19426, 376.440246, -1340.590942, 18.262552, -0.000014, 90.000007, -58.999977, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 3306, "cunte_house1", "des_ntwnwall1", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 388.338714, -1341.979248, 19.887554, 89.999992, 194.440994, -73.440971, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 3306, "cunte_house1", "des_ntwnwall1", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 385.337982, -1343.782348, 19.887544, 89.999992, 194.441009, -73.440963, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 3306, "cunte_house1", "des_ntwnwall1", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 382.136199, -1344.691406, 19.887556, 89.999992, 169.128890, -78.128822, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 3306, "cunte_house1", "des_ntwnwall1", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 381.836822, -1344.696044, 19.888559, 89.999992, 173.354629, -82.354598, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 3306, "cunte_house1", "des_ntwnwall1", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 378.604064, -1343.896484, 19.890560, 89.999992, 133.404434, -72.404403, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 3306, "cunte_house1", "des_ntwnwall1", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 376.208007, -1341.589843, 19.890562, 89.999992, 104.441001, -73.440963, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 3306, "cunte_house1", "des_ntwnwall1", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 375.827362, -1340.954467, 19.890562, 89.999992, 104.441001, -73.440963, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 3306, "cunte_house1", "des_ntwnwall1", 0x00000000);
    showroomxts = CreateDynamicObject(19426, 387.971313, -1341.367797, 21.399572, 0.000003, 90.000030, 30.999998, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 3306, "cunte_house1", "des_ntwnwall1", 0x00000000);
    showroomxts = CreateDynamicObject(19426, 384.970520, -1343.170776, 21.399572, 0.000003, 90.000030, 30.999998, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 3306, "cunte_house1", "des_ntwnwall1", 0x00000000);
    showroomxts = CreateDynamicObject(19426, 382.123962, -1343.981079, 21.400573, -0.000009, 90.000030, 1.000010, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 3306, "cunte_house1", "des_ntwnwall1", 0x00000000);
    showroomxts = CreateDynamicObject(19426, 381.824584, -1343.985839, 21.401573, -0.000009, 90.000030, 1.000010, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 3306, "cunte_house1", "des_ntwnwall1", 0x00000000);
    showroomxts = CreateDynamicObject(19426, 378.949249, -1343.272705, 21.402572, -0.000022, 90.000015, -28.999975, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 3306, "cunte_house1", "des_ntwnwall1", 0x00000000);
    showroomxts = CreateDynamicObject(19426, 376.820770, -1341.226196, 21.403575, -0.000028, 90.000007, -58.999946, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 3306, "cunte_house1", "des_ntwnwall1", 0x00000000);
    showroomxts = CreateDynamicObject(19426, 376.440246, -1340.590942, 21.404575, -0.000028, 90.000007, -58.999946, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 3306, "cunte_house1", "des_ntwnwall1", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 388.824645, -1339.563598, 19.887554, 89.999992, 284.440979, -73.440971, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 3306, "cunte_house1", "des_ntwnwall1", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 376.534057, -1338.597656, 19.890562, 89.999992, 194.440994, -73.440971, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 3306, "cunte_house1", "des_ntwnwall1", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 379.534149, -1336.794921, 19.890562, 89.999992, 194.440994, -73.440971, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 3306, "cunte_house1", "des_ntwnwall1", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 382.534240, -1334.992309, 19.890562, 89.999992, 194.440994, -73.440971, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 3306, "cunte_house1", "des_ntwnwall1", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 387.022003, -1336.563476, 19.887554, 89.999992, 284.440979, -73.440971, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 3306, "cunte_house1", "des_ntwnwall1", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 386.013366, -1334.882934, 19.888553, 89.999992, 284.440979, -73.440971, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 3306, "cunte_house1", "des_ntwnwall1", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 383.717041, -1334.280639, 19.891563, 89.999992, 194.440994, -73.440971, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 3306, "cunte_house1", "des_ntwnwall1", 0x00000000);
    showroomxts = CreateDynamicObject(19445, 383.875122, -1340.842407, 21.400592, 0.000020, 90.000000, 120.999931, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 3306, "cunte_house1", "des_ntwnwall1", 0x00000000);
    showroomxts = CreateDynamicObject(19445, 382.072479, -1337.842285, 21.400592, 0.000020, 90.000000, 120.999931, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 3306, "cunte_house1", "des_ntwnwall1", 0x00000000);
    showroomxts = CreateDynamicObject(19445, 381.881744, -1337.524902, 21.399593, 0.000020, 90.000000, 120.999931, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 3306, "cunte_house1", "des_ntwnwall1", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 379.722625, -1341.622314, 21.398593, 0.000028, 90.000000, 120.999908, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 3306, "cunte_house1", "des_ntwnwall1", 0x00000000);
    showroomxts = CreateDynamicObject(19445, 381.506195, -1338.182617, 21.399593, 0.000028, 90.000000, 120.999908, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 3306, "cunte_house1", "des_ntwnwall1", 0x00000000);
    showroomxts = CreateDynamicObject(19445, 381.315460, -1337.865234, 21.398593, 0.000028, 90.000000, 120.999908, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 3306, "cunte_house1", "des_ntwnwall1", 0x00000000);
    showroomxts = CreateDynamicObject(19445, 384.023468, -1340.753295, 18.248546, 0.000028, 90.000000, 120.999908, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 3306, "cunte_house1", "des_ntwnwall1", 0x00000000);
    showroomxts = CreateDynamicObject(19445, 382.221679, -1337.752685, 18.248546, 0.000028, 90.000000, 120.999908, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 3306, "cunte_house1", "des_ntwnwall1", 0x00000000);
    showroomxts = CreateDynamicObject(19445, 381.940185, -1337.286132, 18.247545, 0.000036, 90.000000, 120.999885, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 3306, "cunte_house1", "des_ntwnwall1", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 379.722625, -1341.622314, 18.247545, 0.000028, 90.000000, 120.999908, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 3306, "cunte_house1", "des_ntwnwall1", 0x00000000);
    showroomxts = CreateDynamicObject(19445, 381.506195, -1338.182617, 18.248546, 0.000028, 90.000000, 120.999908, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 3306, "cunte_house1", "des_ntwnwall1", 0x00000000);
    showroomxts = CreateDynamicObject(19445, 381.225555, -1337.715576, 18.246545, 0.000036, 90.000000, 120.999885, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 3306, "cunte_house1", "des_ntwnwall1", 0x00000000);
    showroomxts = CreateDynamicObject(18762, 391.443969, -1338.917114, 18.669242, 89.999992, 194.440963, -73.440979, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(18762, 395.661041, -1336.383300, 18.669242, 89.999992, 194.440963, -73.440979, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(18762, 399.938293, -1333.813232, 18.669242, 89.999992, 194.440963, -73.440979, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(18762, 400.829986, -1333.277465, 18.669242, 89.999992, 194.440963, -73.440979, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(18762, 401.900634, -1330.300537, 18.669242, 89.999992, -75.558990, -73.440956, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(18762, 399.402587, -1326.143066, 18.669242, 89.999992, -75.558990, -73.440956, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(18762, 396.889068, -1321.959960, 18.669242, 89.999992, -75.558990, -73.440956, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(18762, 394.807983, -1318.496459, 18.669242, 89.999992, -75.558990, -73.440956, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(18980, 383.293487, -1322.614990, 18.669242, 89.999992, 14.440992, -73.440971, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(18980, 380.935760, -1324.031616, 18.669242, 89.999992, 14.440992, -73.440971, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(18980, 383.292633, -1322.615478, 19.249237, 89.999992, 14.441003, -73.440963, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(18980, 380.934906, -1324.032104, 19.249237, 89.999992, 14.441003, -73.440963, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(18980, 383.292633, -1322.615478, 20.969263, 89.999992, 14.441021, -73.440948, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(18980, 380.934906, -1324.032104, 20.969263, 89.999992, 14.441021, -73.440948, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(18762, 372.194488, -1332.783935, 18.669242, 89.999992, 104.440979, -73.440971, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(18762, 374.744018, -1337.026977, 18.669242, 89.999992, 104.440979, -73.440971, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(19445, 397.249023, -1332.822265, 20.798942, 0.000029, 89.999992, 120.999908, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 14668, "711c", "gun_ceiling1128", 0x00000000);
    showroomxts = CreateDynamicObject(19445, 389.003265, -1337.776733, 20.798942, 0.000029, 89.999992, 120.999908, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 14668, "711c", "gun_ceiling1128", 0x00000000);
    showroomxts = CreateDynamicObject(19445, 390.168518, -1321.116333, 20.798942, -0.000029, 90.000007, -58.999946, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 14668, "711c", "gun_ceiling1128", 0x00000000);
    showroomxts = CreateDynamicObject(19445, 381.948608, -1326.055419, 20.798942, -0.000029, 90.000007, -58.999946, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 14668, "711c", "gun_ceiling1128", 0x00000000);
    showroomxts = CreateDynamicObject(19445, 376.402770, -1329.387695, 20.799942, -0.000029, 90.000007, -58.999946, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 14668, "711c", "gun_ceiling1128", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 398.164093, -1328.189331, 20.798942, 0.000029, 89.999992, 120.999908, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 14668, "711c", "gun_ceiling1128", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 396.361389, -1325.189208, 20.798942, 0.000029, 89.999992, 120.999908, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 14668, "711c", "gun_ceiling1128", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 394.707641, -1322.436889, 20.799942, 0.000029, 89.999992, 120.999908, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 14668, "711c", "gun_ceiling1128", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 375.437408, -1334.015625, 20.799942, 0.000029, 89.999992, 120.999908, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 14668, "711c", "gun_ceiling1128", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 377.240051, -1337.015747, 20.799942, 0.000029, 89.999992, 120.999908, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 14668, "711c", "gun_ceiling1128", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 378.180297, -1332.367431, 20.799942, 0.000029, 89.999992, 120.999908, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 14668, "711c", "gun_ceiling1128", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 379.982940, -1335.367553, 20.799942, 0.000029, 89.999992, 120.999908, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 14668, "711c", "gun_ceiling1128", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 380.923187, -1330.719360, 20.799942, 0.000029, 89.999992, 120.999908, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 14668, "711c", "gun_ceiling1128", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 382.725830, -1333.719482, 20.799942, 0.000029, 89.999992, 120.999908, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 14668, "711c", "gun_ceiling1128", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 383.666076, -1329.071289, 20.799942, 0.000029, 89.999992, 120.999908, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 14668, "711c", "gun_ceiling1128", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 385.468719, -1332.071411, 20.799942, 0.000029, 89.999992, 120.999908, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 14668, "711c", "gun_ceiling1128", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 387.220184, -1334.986328, 20.800941, 0.000029, 89.999992, 120.999908, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 14668, "711c", "gun_ceiling1128", 0x00000000);
    showroomxts = CreateDynamicObject(19866, 391.538665, -1334.218627, 20.921115, 0.000029, 89.999992, 120.999908, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    showroomxts = CreateDynamicObject(19866, 395.552032, -1331.807128, 20.921115, 0.000029, 89.999992, 120.999908, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    showroomxts = CreateDynamicObject(19866, 396.021392, -1327.733520, 20.921115, -0.000003, 89.999969, -149.000091, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    showroomxts = CreateDynamicObject(19866, 393.734344, -1323.927246, 20.921115, -0.000003, 89.999969, -149.000091, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    showroomxts = CreateDynamicObject(19866, 388.364227, -1332.334472, 20.921115, -0.000003, 89.999961, -149.000091, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    showroomxts = CreateDynamicObject(19866, 386.077178, -1328.528198, 20.921115, -0.000003, 89.999961, -149.000091, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    showroomxts = CreateDynamicObject(19866, 386.671264, -1326.117919, 20.922119, 0.000036, 89.999992, 120.999885, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    showroomxts = CreateDynamicObject(19866, 390.684631, -1323.706420, 20.922119, 0.000036, 89.999992, 120.999885, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    showroomxts = CreateDynamicObject(19379, 390.928405, -1328.692871, 21.081138, 0.000003, 90.000030, 30.999998, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 16639, "a51_labs", "ws_trainstationwin1", 0x00000000);
    showroomxts = CreateDynamicObject(18762, 391.443969, -1338.917114, 19.249233, 89.999992, 194.440979, -73.440971, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(18762, 395.661041, -1336.383300, 19.249233, 89.999992, 194.440979, -73.440971, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(18762, 399.938293, -1333.813232, 19.249233, 89.999992, 194.440979, -73.440971, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(18762, 400.829986, -1333.277465, 19.249233, 89.999992, 194.440979, -73.440971, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(18762, 401.900634, -1330.300537, 19.249233, 89.999992, -75.559013, -73.440971, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(18762, 399.402587, -1326.143066, 19.249233, 89.999992, -75.559013, -73.440971, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(18762, 396.889068, -1321.959960, 19.249233, 89.999992, -75.559013, -73.440971, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(18762, 394.807983, -1318.496459, 19.249233, 89.999992, -75.559013, -73.440971, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(18762, 391.443969, -1338.917114, 20.969261, 89.999992, 194.440979, -73.440963, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(18762, 395.661041, -1336.383300, 20.969261, 89.999992, 194.440979, -73.440963, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(18762, 399.938293, -1333.813232, 20.969261, 89.999992, 194.440979, -73.440963, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(18762, 400.829986, -1333.277465, 20.969261, 89.999992, 194.440979, -73.440963, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(18762, 401.900634, -1330.300537, 20.969261, 89.999992, -75.559005, -73.440971, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(18762, 399.402587, -1326.143066, 20.969261, 89.999992, -75.559005, -73.440971, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(18762, 372.194488, -1332.783935, 19.249233, 89.999992, 104.440986, -73.440971, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(18762, 374.744018, -1337.026977, 19.249233, 89.999992, 104.440986, -73.440971, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(19379, 395.342742, -1330.369506, 21.151130, 0.000003, 90.000022, 30.999998, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10101, "2notherbuildsfe", "ws_rooftarmac1", 0x00000000);
    showroomxts = CreateDynamicObject(19379, 391.340515, -1323.708740, 21.150131, 0.000003, 90.000022, 30.999998, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10101, "2notherbuildsfe", "ws_rooftarmac1", 0x00000000);
    showroomxts = CreateDynamicObject(19379, 386.342681, -1335.777221, 21.151130, 0.000003, 90.000030, 30.999998, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10101, "2notherbuildsfe", "ws_rooftarmac1", 0x00000000);
    showroomxts = CreateDynamicObject(19379, 382.340454, -1329.116455, 21.150131, 0.000003, 90.000030, 30.999998, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10101, "2notherbuildsfe", "ws_rooftarmac1", 0x00000000);
    showroomxts = CreateDynamicObject(19379, 378.087432, -1331.998901, 21.149126, 0.000003, 90.000030, 30.999998, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10101, "2notherbuildsfe", "ws_rooftarmac1", 0x00000000);
    showroomxts = CreateDynamicObject(19445, 400.413635, -1327.917724, 16.411106, 0.000003, -179.999984, 30.999923, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 6282, "beafron2_law2", "boardwalk2_la", 0x00000000);
    showroomxts = CreateDynamicObject(19445, 400.413635, -1327.917724, 12.911106, 0.000003, -179.999984, 30.999923, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 6282, "beafron2_law2", "boardwalk2_la", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 401.585388, -1332.811279, 16.411106, -0.000028, 179.999984, -58.999946, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 6282, "beafron2_law2", "boardwalk2_la", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 401.585388, -1332.811279, 12.911106, 0.000028, -0.000003, 120.999885, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 6282, "beafron2_law2", "boardwalk2_la", 0x00000000);
    showroomxts = CreateDynamicObject(19445, 389.484405, -1318.896118, 12.911106, -0.000020, 179.999969, -58.999938, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 1795, "cj_s_beds", "CJ_MATRESS3", 0x00000000);
    showroomxts = CreateDynamicObject(19445, 381.238464, -1323.850708, 12.911106, -0.000014, 179.999969, -58.999946, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 1795, "cj_s_beds", "CJ_MATRESS3", 0x00000000);
    showroomxts = CreateDynamicObject(19445, 389.484405, -1318.896118, 16.411106, -0.000020, 179.999969, -58.999938, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 1795, "cj_s_beds", "CJ_MATRESS3", 0x00000000);
    showroomxts = CreateDynamicObject(19445, 381.238464, -1323.850708, 16.411106, -0.000014, 179.999969, -58.999946, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 1795, "cj_s_beds", "CJ_MATRESS3", 0x00000000);
    showroomxts = CreateDynamicObject(19426, 393.991302, -1317.207153, 16.411106, -0.000003, 179.999984, -149.000030, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 1795, "cj_s_beds", "CJ_MATRESS3", 0x00000000);
    showroomxts = CreateDynamicObject(19426, 393.991302, -1317.207153, 12.911106, -0.000003, 179.999984, -149.000030, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 1795, "cj_s_beds", "CJ_MATRESS3", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 396.076019, -1336.121459, 16.411108, -0.000014, 179.999984, -58.999984, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 396.076019, -1336.121459, 12.911107, -0.000014, 179.999984, -58.999984, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 390.590728, -1339.417358, 16.411108, -0.000006, 179.999984, -59.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 390.590728, -1339.417358, 12.911107, -0.000006, 179.999984, -59.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 382.104675, -1343.622802, 12.938913, 0.000019, 180.000000, 91.299957, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 13691, "bevcunto2_lahills", "stonewall3_la", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 379.137908, -1342.898925, 12.938913, 0.000012, 180.000000, 61.299991, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 13691, "bevcunto2_lahills", "stonewall3_la", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 376.930145, -1340.788574, 12.938913, 0.000007, 180.000000, 31.300008, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 13691, "bevcunto2_lahills", "stonewall3_la", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 385.035797, -1342.765747, 12.938913, -0.000007, 180.000000, -58.700008, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 13691, "bevcunto2_lahills", "stonewall3_la", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 387.632629, -1341.185791, 12.938913, -0.000007, 180.000000, -58.700008, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 13691, "bevcunto2_lahills", "stonewall3_la", 0x00000000);
    showroomxts = CreateDynamicObject(18880, 376.083801, -1339.335327, 18.201099, -0.000003, 179.999984, -148.999938, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 2, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 377.497955, -1338.372070, 12.938913, 0.000017, 180.000000, 121.299964, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 14652, "ab_trukstpa", "CJ_WOOD6", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 380.231750, -1336.709594, 12.938913, 0.000017, 180.000000, 121.299964, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 14652, "ab_trukstpa", "CJ_WOOD6", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 382.966278, -1335.046752, 12.938913, 0.000017, 180.000000, 121.299964, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 14652, "ab_trukstpa", "CJ_WOOD6", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 388.295867, -1338.905395, 12.938913, -0.000000, 180.000000, -148.699981, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 14652, "ab_trukstpa", "CJ_WOOD6", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 386.628021, -1336.163085, 12.938913, -0.000000, 180.000000, -148.699981, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 14652, "ab_trukstpa", "CJ_WOOD6", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 386.005249, -1335.137207, 12.939911, -0.000000, 180.000000, -148.699981, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 14652, "ab_trukstpa", "CJ_WOOD6", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 383.846557, -1334.510375, 12.939911, 0.000017, 180.000000, 121.299964, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 14652, "ab_trukstpa", "CJ_WOOD6", 0x00000000);
    showroomxts = CreateDynamicObject(19445, 381.016510, -1338.217163, 14.461107, 0.000014, 89.999992, 120.999954, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 6102, "gazlaw1", "law_gazwhitefloor", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 382.079284, -1341.800903, 14.459108, 0.000014, 90.000000, 90.999954, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 6102, "gazlaw1", "law_gazwhitefloor", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 379.886047, -1341.340698, 14.460106, 0.000009, 90.000007, 60.999977, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 6102, "gazlaw1", "law_gazwhitefloor", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 382.813903, -1341.208496, 14.461107, 0.000014, 89.999992, 120.999954, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 6102, "gazlaw1", "law_gazwhitefloor", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 385.566009, -1339.554809, 14.461107, 0.000014, 89.999992, 120.999954, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 6102, "gazlaw1", "law_gazwhitefloor", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 385.916778, -1340.138671, 14.460106, 0.000014, 89.999992, 120.999954, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 6102, "gazlaw1", "law_gazwhitefloor", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 384.006469, -1341.286499, 14.459108, 0.000014, 89.999992, 120.999954, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 6102, "gazlaw1", "law_gazwhitefloor", 0x00000000);
    showroomxts = CreateDynamicObject(19445, 381.848480, -1337.717285, 14.460106, 0.000014, 89.999992, 120.999954, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 6102, "gazlaw1", "law_gazwhitefloor", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 386.449035, -1339.024291, 14.460106, 0.000014, 89.999992, 120.999954, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 6102, "gazlaw1", "law_gazwhitefloor", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 386.794464, -1339.599243, 14.459108, 0.000014, 89.999992, 120.999954, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 6102, "gazlaw1", "law_gazwhitefloor", 0x00000000);
    showroomxts = CreateDynamicObject(14394, 387.990295, -1336.328247, 13.818915, -0.000003, -0.000014, -148.999938, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    showroomxts = CreateDynamicObject(14394, 381.378295, -1334.702026, 13.818915, -0.000014, 0.000003, -58.999977, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    showroomxts = CreateDynamicObject(14394, 379.226470, -1335.994995, 13.818915, -0.000014, 0.000003, -58.999977, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    showroomxts = CreateDynamicObject(18762, 385.383331, -1332.640136, 12.268914, 0.000003, 0.000014, 30.999998, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 8618, "ceasersign", "ceaserspillar01_128", 0x00000000);
    showroomxts = CreateDynamicObject(3498, 385.388824, -1332.637939, 14.974659, 0.000003, 0.000014, 30.999998, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(showroomxts, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    showroomxts = CreateDynamicObject(19866, 385.766601, -1336.268554, 18.181612, 0.000003, 90.000022, 30.999998, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(19866, 387.137207, -1338.549560, 18.181612, 0.000003, 90.000022, 30.999998, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(19866, 383.309295, -1335.575927, 18.181612, 0.000014, 89.999992, 120.999954, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(19866, 379.700531, -1337.744384, 18.181612, 0.000014, 89.999992, 120.999954, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(19866, 378.551757, -1338.434570, 18.181612, 0.000014, 89.999992, 120.999954, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(18066, 376.830566, -1339.047607, 18.071109, 89.999992, 284.440979, -73.440979, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(showroomxts, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    showroomxts = CreateDynamicObject(18066, 378.013671, -1338.336669, 18.071109, 89.999992, 284.440979, -73.440979, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(showroomxts, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    showroomxts = CreateDynamicObject(18066, 379.204833, -1337.620971, 18.071109, 89.999992, 284.440979, -73.440979, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(showroomxts, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    showroomxts = CreateDynamicObject(18066, 380.404907, -1336.899902, 18.071109, 89.999992, 284.440979, -73.440979, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(showroomxts, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    showroomxts = CreateDynamicObject(18066, 381.596588, -1336.183837, 18.071109, 89.999992, 284.440979, -73.440979, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(showroomxts, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    showroomxts = CreateDynamicObject(18066, 382.693359, -1335.524902, 18.071109, 89.999992, 284.440979, -73.440979, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(showroomxts, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    showroomxts = CreateDynamicObject(18066, 383.842041, -1334.834716, 18.071109, 89.999992, 284.440979, -73.440979, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(showroomxts, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    showroomxts = CreateDynamicObject(18066, 384.442535, -1334.473876, 18.071109, 89.999992, 284.440979, -73.440979, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(showroomxts, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    showroomxts = CreateDynamicObject(18066, 385.229705, -1334.619384, 18.071109, 89.999992, 14.441008, -73.440956, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(showroomxts, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    showroomxts = CreateDynamicObject(18066, 385.956207, -1335.828491, 18.071109, 89.999992, 14.441008, -73.440956, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(showroomxts, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    showroomxts = CreateDynamicObject(18066, 386.661926, -1337.002929, 18.071109, 89.999992, 14.441008, -73.440956, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(showroomxts, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    showroomxts = CreateDynamicObject(18066, 387.367889, -1338.177856, 18.071109, 89.999992, 14.441008, -73.440956, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(showroomxts, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    showroomxts = CreateDynamicObject(18066, 388.094421, -1339.387084, 18.071109, 89.999992, 14.441008, -73.440956, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(showroomxts, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    showroomxts = CreateDynamicObject(18066, 388.419219, -1339.927612, 18.071109, 89.999992, 14.441008, -73.440956, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(showroomxts, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    showroomxts = CreateDynamicObject(1754, 377.042388, -1339.463256, 14.547045, 0.000014, -0.000003, 120.999954, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    showroomxts = CreateDynamicObject(1754, 377.542327, -1340.295288, 14.547045, 0.000014, -0.000003, 120.999954, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    showroomxts = CreateDynamicObject(1754, 378.047180, -1341.135498, 14.547045, 0.000014, -0.000003, 120.999954, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    showroomxts = CreateDynamicObject(1754, 378.558441, -1341.636474, 14.548046, 0.000009, -0.000009, 151.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    showroomxts = CreateDynamicObject(1754, 379.398803, -1342.102050, 14.548046, 0.000009, -0.000009, 151.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    showroomxts = CreateDynamicObject(1754, 380.239135, -1342.567749, 14.548046, 0.000009, -0.000009, 151.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    showroomxts = CreateDynamicObject(2110, 379.987762, -1340.259399, 14.227045, 0.000014, -0.000003, 120.999954, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 3922, "bistro", "Marble2", 0x00000000);
    showroomxts = CreateDynamicObject(1763, 386.230682, -1341.265869, 14.547045, -0.000003, -0.000014, -148.999938, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(showroomxts, 1, 14652, "ab_trukstpa", "CJ_WOOD6", 0x00000000);
    showroomxts = CreateDynamicObject(950, 387.693237, -1340.604614, 15.097050, 0.000003, 0.000014, 30.999998, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    showroomxts = CreateDynamicObject(2691, 391.197753, -1338.412109, 19.488914, -0.000003, -0.000014, -148.999938, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10375, "subshops_sfs", "ws_archangels_dirty", 0x00000000);
    showroomxts = CreateDynamicObject(2691, 375.178070, -1336.720581, 19.488914, 0.000014, -0.000003, 120.999954, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 2655, "cj_banner", "CJ_MERC_LOGO", 0x00000000);
    showroomxts = CreateDynamicObject(2732, 385.431823, -1332.215698, 15.498892, 0.000014, -0.100023, 170.799774, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    showroomxts = CreateDynamicObject(2732, 385.391296, -1332.401855, 15.498875, -0.000014, -0.099973, -9.200119, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    showroomxts = CreateDynamicObject(18244, 380.258575, -1331.283325, 13.590036, 0.000003, 0.000044, 211.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    showroomxts = CreateDynamicObject(2729, 385.431304, -1332.205322, 15.498903, 0.000009, -0.100017, 170.799774, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 14530, "estate2", "Auto_windsor", 0x00000000);
    showroomxts = CreateDynamicObject(2736, 382.751403, -1339.648193, 18.107042, 89.999992, 194.440994, -73.440971, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    showroomxts = CreateDynamicObject(2736, 382.897155, -1339.560668, 18.117042, 89.999992, 194.440994, -73.440971, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(2736, 382.995025, -1339.723632, 18.117042, 89.999992, 194.440994, -73.440971, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(2736, 382.686157, -1339.909179, 18.117042, 89.999992, 194.440994, -73.440971, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(2736, 382.505737, -1339.608886, 18.117042, 89.999992, 194.440994, -73.440971, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(2736, 382.814605, -1339.423339, 18.117042, 89.999992, 194.440994, -73.440971, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(19445, 397.789184, -1333.059814, 13.731101, 0.000014, 89.999992, 120.999954, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 19595, "lsappartments1", "ceilingtiles3-128x128", 0x00000000);
    showroomxts = CreateDynamicObject(19445, 389.534667, -1338.019653, 13.731101, 0.000014, 89.999992, 120.999954, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 19595, "lsappartments1", "ceilingtiles3-128x128", 0x00000000);
    showroomxts = CreateDynamicObject(19445, 395.986572, -1330.059692, 13.731101, 0.000014, 89.999992, 120.999954, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 19595, "lsappartments1", "ceilingtiles3-128x128", 0x00000000);
    showroomxts = CreateDynamicObject(19445, 387.732055, -1335.019531, 13.731101, 0.000014, 89.999992, 120.999954, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 19595, "lsappartments1", "ceilingtiles3-128x128", 0x00000000);
    showroomxts = CreateDynamicObject(19445, 394.183929, -1327.059570, 13.731101, 0.000014, 89.999992, 120.999954, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 19595, "lsappartments1", "ceilingtiles3-128x128", 0x00000000);
    showroomxts = CreateDynamicObject(19445, 385.929412, -1332.019409, 13.731101, 0.000014, 89.999992, 120.999954, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 19595, "lsappartments1", "ceilingtiles3-128x128", 0x00000000);
    showroomxts = CreateDynamicObject(19445, 392.381286, -1324.059448, 13.731101, 0.000014, 89.999992, 120.999954, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 19595, "lsappartments1", "ceilingtiles3-128x128", 0x00000000);
    showroomxts = CreateDynamicObject(19445, 384.126770, -1329.019287, 13.731101, 0.000014, 89.999992, 120.999954, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 19595, "lsappartments1", "ceilingtiles3-128x128", 0x00000000);
    showroomxts = CreateDynamicObject(19445, 390.578643, -1321.059448, 13.731101, 0.000014, 89.999992, 120.999954, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 19595, "lsappartments1", "ceilingtiles3-128x128", 0x00000000);
    showroomxts = CreateDynamicObject(19445, 382.324127, -1326.019287, 13.731101, 0.000014, 89.999992, 120.999954, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 19595, "lsappartments1", "ceilingtiles3-128x128", 0x00000000);
    showroomxts = CreateDynamicObject(19445, 390.238464, -1320.493286, 13.730101, 0.000020, 89.999992, 120.999931, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 19595, "lsappartments1", "ceilingtiles3-128x128", 0x00000000);
    showroomxts = CreateDynamicObject(19445, 381.983947, -1325.453125, 13.730101, 0.000020, 89.999992, 120.999931, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 19595, "lsappartments1", "ceilingtiles3-128x128", 0x00000000);
    showroomxts = CreateDynamicObject(19445, 375.702331, -1328.982299, 13.726099, 0.000020, 89.999992, 120.999931, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 19595, "lsappartments1", "ceilingtiles3-128x128", 0x00000000);
    showroomxts = CreateDynamicObject(19445, 377.504974, -1331.982299, 13.726099, 0.000020, 89.999992, 120.999931, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 19595, "lsappartments1", "ceilingtiles3-128x128", 0x00000000);
    showroomxts = CreateDynamicObject(19445, 379.307617, -1334.982421, 13.726099, 0.000020, 89.999992, 120.999931, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 19595, "lsappartments1", "ceilingtiles3-128x128", 0x00000000);
    showroomxts = CreateDynamicObject(18762, 396.889068, -1321.959960, 20.969261, 89.999992, -75.559005, -73.440971, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(18762, 394.807983, -1318.496459, 20.969261, 89.999992, -75.559005, -73.440971, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(2247, 397.259765, -1326.793823, 15.267040, 0.000003, 0.000014, 30.999998, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    showroomxts = CreateDynamicObject(2643, 398.959838, -1325.963623, 16.077035, -0.000020, 0.000003, -58.999954, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(2662, 398.887664, -1326.044067, 16.157043, -0.000020, 180.000000, -58.999954, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(showroomxts, 0, "V", 90, "Arial", 199, 1, 0xFFFFFFFF, 0x00000000, 1);
    showroomxts = CreateDynamicObject(19482, 400.035614, -1327.573242, 16.057037, -0.000003, -0.000020, -149.000030, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(showroomxts, 0, "ATHERLIFE", 130, "Arial", 50, 1, 0xFF000000, 0x00000000, 1);
    showroomxts = CreateDynamicObject(19482, 401.354400, -1329.767944, 16.057037, -0.000003, -0.000020, -149.000030, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(showroomxts, 0, "SHOWROOM", 130, "Arial", 50, 1, 0xFFFF0000, 0x00000000, 1);
    showroomxts = CreateDynamicObject(19482, 398.026550, -1328.616943, 14.447037, -0.000003, -0.000020, -149.000030, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(showroomxts, 0, "RECEPTIONIST", 130, "Arial", 50, 1, 0xFFFFFFFF, 0x00000000, 1);
    showroomxts = CreateDynamicObject(18762, 372.194488, -1332.783935, 20.969261, 89.999992, 104.440994, -73.440963, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(18762, 374.744018, -1337.026977, 20.969261, 89.999992, 104.440994, -73.440963, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(18762, 391.443969, -1338.917114, 19.969261, 89.999992, 194.440979, -73.440963, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(18762, 374.744018, -1337.026977, 19.969261, 89.999992, 104.440994, -73.440963, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(18244, 377.514739, -1332.935058, 13.591034, 0.000003, 0.000036, 211.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    showroomxts = CreateDynamicObject(18244, 376.401855, -1331.084716, 13.592036, 0.000003, 0.000029, 30.999998, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    showroomxts = CreateDynamicObject(18244, 379.145690, -1329.432983, 13.591034, 0.000003, 0.000036, 30.999998, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 381.443572, -1331.904418, 12.245034, 0.000050, -0.000003, 120.999839, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 1975, "texttest", "kb_red", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 378.699737, -1333.553100, 12.246033, 0.000043, -0.000003, 120.999862, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 1975, "texttest", "kb_red", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 377.439514, -1334.310302, 12.246033, 0.000043, -0.000003, 120.999862, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 1975, "texttest", "kb_red", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 376.386383, -1329.704833, 12.247035, 0.000036, -0.000003, 120.999885, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 1975, "texttest", "kb_red", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 375.126159, -1330.462158, 12.247035, 0.000036, -0.000003, 120.999885, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 1975, "texttest", "kb_red", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 379.130218, -1328.056274, 12.246033, 0.000043, -0.000003, 120.999862, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 1975, "texttest", "kb_red", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 380.670806, -1329.766113, 13.917710, 0.000020, 89.999992, 120.999931, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 3922, "bistro", "Marble2", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 377.927886, -1331.414184, 13.917710, 0.000020, 89.999992, 120.999931, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 3922, "bistro", "Marble2", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 375.981079, -1332.583862, 13.916708, 0.000020, 89.999992, 120.999931, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 3922, "bistro", "Marble2", 0x00000000);
    showroomxts = CreateDynamicObject(13646, 390.831695, -1328.550903, 13.467009, 0.000003, 0.000014, 30.999998, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(showroomxts, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    showroomxts = CreateDynamicObject(18880, 383.005737, -1328.322387, 15.147041, -0.000059, 179.999984, -58.999858, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    SetDynamicObjectMaterial(showroomxts, 2, 3922, "bistro", "Marble2", 0x00000000);
    showroomxts = CreateDynamicObject(2641, 383.183807, -1328.229980, 15.100399, -35.899925, 270.000000, 120.999794, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    SetDynamicObjectMaterial(showroomxts, 1, 14530, "estate2", "Auto_windsor", 0x00000000);
    showroomxts = CreateDynamicObject(2641, 383.145782, -1328.276245, 15.059055, 36.199939, 270.000000, -58.999828, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    SetDynamicObjectMaterial(showroomxts, 1, 14652, "ab_trukstpa", "CJ_WOOD6", 0x00000000);
    showroomxts = CreateDynamicObject(2256, 375.382080, -1337.140014, 16.289722, 0.000014, -0.000003, 120.999954, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 2464, "rc_shop_acc", "CJ_RC_1", 0x00000000);
    showroomxts = CreateDynamicObject(2256, 372.168426, -1331.791625, 16.289722, 0.000014, -0.000003, 120.999954, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 2464, "rc_shop_acc", "CJ_RC_3", 0x00000000);
    showroomxts = CreateDynamicObject(2256, 373.760192, -1334.440673, 16.289722, 0.000014, -0.000003, 120.999954, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 2464, "rc_shop_acc", "CJ_RC_4", 0x00000000);
    showroomxts = CreateDynamicObject(19866, 371.555236, -1330.089355, 15.389713, 89.999992, 194.440979, -73.440971, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(19866, 378.036407, -1326.195068, 15.389713, 89.999992, 194.440979, -73.440971, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(19866, 374.795867, -1328.142211, 15.389713, 89.999992, 194.440979, -73.440971, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(19866, 373.141387, -1329.136352, 15.389713, 89.999992, 194.440979, -73.440971, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(19866, 376.484466, -1327.127563, 15.389713, 89.999992, 194.440979, -73.440971, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(19866, 375.652526, -1327.627441, 15.389713, 89.999992, 194.440979, -73.440971, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(19866, 373.972290, -1328.637084, 15.389713, 89.999992, 194.440979, -73.440971, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(19866, 372.352233, -1329.610473, 15.389713, 89.999992, 194.440979, -73.440971, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(19866, 377.255645, -1326.664184, 15.389713, 89.999992, 194.440979, -73.440971, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 399.200927, -1325.958984, 15.987041, 0.000003, 0.000014, 30.999998, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 3922, "bistro", "Marble2", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 400.854064, -1328.710205, 15.987041, 0.000003, 0.000014, 30.999998, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 3922, "bistro", "Marble2", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 401.680969, -1330.080688, 15.987041, 0.000003, 0.000014, 30.999998, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 3922, "bistro", "Marble2", 0x00000000);
    showroomxts = CreateDynamicObject(1753, 379.706085, -1325.707641, 13.818915, 0.000003, 0.000014, 30.999998, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 14415, "carter_block_2", "mp_motel_carpet1", 0x00000000);
    showroomxts = CreateDynamicObject(19445, 381.036499, -1324.007080, 15.695143, -14.599998, 179.999969, -58.999946, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 3922, "bistro", "Marble2", 0x00000000);
    showroomxts = CreateDynamicObject(19445, 389.024719, -1319.207275, 13.267716, -14.599998, 179.999969, -58.999946, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 3922, "bistro", "Marble2", 0x00000000);
    showroomxts = CreateDynamicObject(19445, 380.279876, -1324.461791, 12.308174, -14.599998, 179.999969, -58.999946, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 3922, "bistro", "Marble2", 0x00000000);
    showroomxts = CreateDynamicObject(19445, 381.058563, -1323.982177, 15.811264, -14.599998, 179.999969, -58.999946, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 1975, "texttest", "kb_red", 0x00000000);
    showroomxts = CreateDynamicObject(19445, 389.038421, -1319.187500, 13.386358, -14.599998, 179.999969, -58.999946, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 1975, "texttest", "kb_red", 0x00000000);
    showroomxts = CreateDynamicObject(2267, 384.210296, -1322.231201, 15.577040, 0.000003, 0.000014, 30.999998, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 1, 10375, "subshops_sfs", "ws_archangels_dirty", 0x00000000);
    showroomxts = CreateDynamicObject(13646, 390.939971, -1328.731079, 13.447009, 0.000003, 0.000014, 30.999998, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 1975, "texttest", "kb_red", 0x00000000);
    SetDynamicObjectMaterial(showroomxts, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    showroomxts = CreateDynamicObject(2256, 395.977844, -1336.047363, 16.437042, -0.000003, -0.000014, -148.999938, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 14530, "estate2", "Auto_hustler", 0x00000000);
    showroomxts = CreateDynamicObject(2256, 390.517181, -1339.328491, 16.437042, -0.000003, -0.000014, -148.999938, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 14530, "estate2", "Auto_monstera", 0x00000000);
    showroomxts = CreateDynamicObject(19426, 395.108673, -1336.836791, 20.128141, 89.999992, 194.440963, -73.440979, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 16639, "a51_labs", "ws_trainstationwin1", 0x00000000);
    showroomxts = CreateDynamicObject(19426, 398.082611, -1335.049926, 20.128141, 89.999992, 194.440963, -73.440979, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 16639, "a51_labs", "ws_trainstationwin1", 0x00000000);
    showroomxts = CreateDynamicObject(19426, 401.074523, -1333.252197, 20.128141, 89.999992, 194.440963, -73.440979, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 16639, "a51_labs", "ws_trainstationwin1", 0x00000000);
    showroomxts = CreateDynamicObject(19426, 400.468872, -1327.584106, 20.128141, 89.999992, -75.558990, -73.440956, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 16639, "a51_labs", "ws_trainstationwin1", 0x00000000);
    showroomxts = CreateDynamicObject(19426, 401.691802, -1332.880126, 20.128141, 89.999992, 194.440963, -73.440979, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 16639, "a51_labs", "ws_trainstationwin1", 0x00000000);
    showroomxts = CreateDynamicObject(19426, 402.271484, -1330.584228, 20.128141, 89.999992, -75.558990, -73.440956, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 16639, "a51_labs", "ws_trainstationwin1", 0x00000000);
    showroomxts = CreateDynamicObject(19426, 398.666229, -1324.584106, 20.128141, 89.999992, -75.558990, -73.440956, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 16639, "a51_labs", "ws_trainstationwin1", 0x00000000);
    showroomxts = CreateDynamicObject(19426, 396.863586, -1321.583984, 20.128141, 89.999992, -75.558990, -73.440956, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 16639, "a51_labs", "ws_trainstationwin1", 0x00000000);
    showroomxts = CreateDynamicObject(19426, 395.060943, -1318.583862, 20.128141, 89.999992, -75.558990, -73.440956, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 16639, "a51_labs", "ws_trainstationwin1", 0x00000000);
    showroomxts = CreateDynamicObject(19426, 392.205596, -1317.055664, 20.128141, 89.999992, 14.440992, -73.440971, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 16639, "a51_labs", "ws_trainstationwin1", 0x00000000);
    showroomxts = CreateDynamicObject(19426, 389.205505, -1318.858276, 20.128141, 89.999992, 14.440992, -73.440971, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 16639, "a51_labs", "ws_trainstationwin1", 0x00000000);
    showroomxts = CreateDynamicObject(19426, 386.205413, -1320.660888, 20.128141, 89.999992, 14.440992, -73.440971, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 16639, "a51_labs", "ws_trainstationwin1", 0x00000000);
    showroomxts = CreateDynamicObject(19426, 383.205322, -1322.463500, 20.128141, 89.999992, 14.440992, -73.440971, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 16639, "a51_labs", "ws_trainstationwin1", 0x00000000);
    showroomxts = CreateDynamicObject(19426, 380.205230, -1324.266235, 20.128141, 89.999992, 14.440992, -73.440971, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 16639, "a51_labs", "ws_trainstationwin1", 0x00000000);
    showroomxts = CreateDynamicObject(19426, 377.205169, -1326.068847, 20.128141, 89.999992, 14.440992, -73.440971, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 16639, "a51_labs", "ws_trainstationwin1", 0x00000000);
    showroomxts = CreateDynamicObject(19426, 374.205078, -1327.871459, 20.128141, 89.999992, 14.440992, -73.440971, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 16639, "a51_labs", "ws_trainstationwin1", 0x00000000);
    showroomxts = CreateDynamicObject(19426, 371.924591, -1329.240600, 20.128141, 89.999992, 14.440992, -73.440971, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 16639, "a51_labs", "ws_trainstationwin1", 0x00000000);
    showroomxts = CreateDynamicObject(19426, 371.215515, -1331.614624, 20.128141, 89.999992, 104.440979, -73.440971, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 16639, "a51_labs", "ws_trainstationwin1", 0x00000000);
    showroomxts = CreateDynamicObject(19426, 373.007507, -1334.597045, 20.128141, 89.999992, 104.440979, -73.440971, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 16639, "a51_labs", "ws_trainstationwin1", 0x00000000);
    showroomxts = CreateDynamicObject(19426, 394.577575, -1317.777465, 20.128141, 89.999992, -75.558990, -73.440956, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 16639, "a51_labs", "ws_trainstationwin1", 0x00000000);
    showroomxts = CreateDynamicObject(18762, 385.383331, -1332.640136, 18.738929, 0.000003, 0.000014, 30.999998, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 8618, "ceasersign", "ceaserspillar01_128", 0x00000000);
    showroomxts = CreateDynamicObject(13646, 390.702880, -1328.336425, 13.447009, 0.000003, 0.000014, 30.999998, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 1975, "texttest", "kb_red", 0x00000000);
    SetDynamicObjectMaterial(showroomxts, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    showroomxts = CreateDynamicObject(13646, 390.972351, -1328.454467, 13.447009, 0.000003, 0.000014, 30.999998, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 1975, "texttest", "kb_red", 0x00000000);
    SetDynamicObjectMaterial(showroomxts, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    showroomxts = CreateDynamicObject(13646, 390.629577, -1328.660522, 13.447009, 0.000003, 0.000014, 30.999998, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 1975, "texttest", "kb_red", 0x00000000);
    SetDynamicObjectMaterial(showroomxts, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    showroomxts = CreateDynamicObject(3053, 391.591888, -1339.243286, 19.919694, -89.999992, 75.559036, 106.558975, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(showroomxts, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    showroomxts = CreateDynamicObject(2643, 391.700378, -1339.428466, 19.891893, 0.000003, 0.000029, 30.999998, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    showroomxts = CreateDynamicObject(1897, 389.955902, -1340.333007, 19.874977, -0.000003, -0.000014, -148.999938, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 1975, "texttest", "kb_red", 0x00000000);
    showroomxts = CreateDynamicObject(1897, 390.384460, -1340.075439, 19.874977, -0.000003, -0.000014, -148.999938, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 1975, "texttest", "kb_red", 0x00000000);
    showroomxts = CreateDynamicObject(1897, 390.813049, -1339.817993, 19.874977, -0.000003, -0.000014, -148.999938, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 1975, "texttest", "kb_red", 0x00000000);
    showroomxts = CreateDynamicObject(1897, 391.241638, -1339.560424, 19.874977, -0.000003, -0.000014, -148.999938, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 1975, "texttest", "kb_red", 0x00000000);
    showroomxts = CreateDynamicObject(1897, 391.670227, -1339.302978, 19.874977, -0.000003, -0.000014, -148.999938, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 1975, "texttest", "kb_red", 0x00000000);
    showroomxts = CreateDynamicObject(1897, 392.098815, -1339.045410, 19.874977, -0.000003, -0.000014, -148.999938, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 1975, "texttest", "kb_red", 0x00000000);
    showroomxts = CreateDynamicObject(1897, 392.527404, -1338.787841, 19.874977, -0.000003, -0.000014, -148.999938, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 1975, "texttest", "kb_red", 0x00000000);
    showroomxts = CreateDynamicObject(1897, 392.955963, -1338.530395, 19.874977, -0.000003, -0.000014, -148.999938, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 1975, "texttest", "kb_red", 0x00000000);
    showroomxts = CreateDynamicObject(1897, 393.384552, -1338.272827, 19.874977, -0.000003, -0.000014, -148.999938, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 1975, "texttest", "kb_red", 0x00000000);
    showroomxts = CreateDynamicObject(2662, 391.787475, -1339.500366, 19.981100, 0.000003, 180.000000, 30.999998, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(showroomxts, 0, "V", 90, "Arial", 199, 1, 0xFFFFFFFF, 0x00000000, 1);
    showroomxts = CreateDynamicObject(975, 393.270172, -1337.861450, 17.768907, 0.000003, 360.000000, 30.999998, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(showroomxts, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x90FFFFFF);
    SetDynamicObjectMaterial(showroomxts, 2, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(showroomxts, 3, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(showroomxts, 4, 19297, "matlights", "invisible", 0x00000000);
    showroomxts = CreateDynamicObject(1897, 392.738281, -1338.147949, 16.264976, 89.999992, 14.440992, -73.440971, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    showroomxts = CreateDynamicObject(1897, 393.813903, -1337.529541, 16.264976, 89.999992, 194.440963, -73.440979, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    showroomxts = CreateDynamicObject(1897, 392.738281, -1338.147949, 18.024980, 89.999992, 14.441003, -73.440963, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    showroomxts = CreateDynamicObject(1897, 393.813903, -1337.529541, 18.024980, 89.999992, 194.440979, -73.440971, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    showroomxts = CreateDynamicObject(1897, 392.738281, -1338.147949, 17.604976, 89.999992, 14.441021, -73.440948, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    showroomxts = CreateDynamicObject(1897, 393.813903, -1337.529541, 17.604976, 89.999992, 194.440979, -73.440963, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    showroomxts = CreateDynamicObject(1897, 392.094970, -1338.534423, 17.484973, 0.000006, 180.000000, 121.000007, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    showroomxts = CreateDynamicObject(1897, 394.615112, -1337.020263, 17.484973, 0.000006, 180.000000, 121.000007, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    showroomxts = CreateDynamicObject(19445, 397.878540, -1321.058837, 12.111387, 0.000000, 0.000000, 31.199989, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 16640, "a51", "concreteyellow256 copy", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 396.635192, -1316.072143, 12.111387, 0.000000, 0.000000, 121.199989, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 16640, "a51", "concreteyellow256 copy", 0x00000000);
    showroomxts = CreateDynamicObject(19353, 401.721954, -1324.472045, 12.111387, 0.000000, 0.000000, 121.199989, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 16640, "a51", "concreteyellow256 copy", 0x00000000);
    showroomxts = CreateDynamicObject(19445, 401.830291, -1318.665527, 12.111387, 0.000000, 0.000000, 31.199989, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 16640, "a51", "concreteyellow256 copy", 0x00000000);
    showroomxts = CreateDynamicObject(19426, 403.766357, -1323.233398, 12.111387, 0.000000, 0.000000, 121.199989, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 16640, "a51", "concreteyellow256 copy", 0x00000000);
    showroomxts = CreateDynamicObject(19426, 398.670989, -1314.839477, 12.111387, 0.000000, 0.000000, 121.199989, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 16640, "a51", "concreteyellow256 copy", 0x00000000);
    showroomxts = CreateDynamicObject(19445, 399.443908, -1320.110473, 13.771389, 0.000000, 90.000000, 31.199989, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 3474, "freightcrane", "yellowcabchev_256", 0x00000000);
    showroomxts = CreateDynamicObject(19445, 400.256408, -1319.619384, 13.770388, 0.000000, 90.000000, 31.199989, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 3474, "freightcrane", "yellowcabchev_256", 0x00000000);
    showroomxts = CreateDynamicObject(19445, 419.684783, -1320.085449, 12.226403, 0.500000, 0.000000, -55.700008, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    showroomxts = CreateDynamicObject(19445, 411.384246, -1307.916870, 12.206404, 0.400000, 0.000000, -55.700008, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    showroomxts = CreateDynamicObject(19445, 415.723724, -1314.277954, 12.226407, 0.500000, 0.000000, -55.700008, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    showroomxts = CreateDynamicObject(19445, 413.615753, -1311.188232, 12.216404, 0.499999, 0.000000, -55.700008, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    showroomxts = CreateDynamicObject(19445, 417.729522, -1317.218872, 12.206404, 0.299999, 0.000000, -55.700008, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    showroomxts = CreateDynamicObject(1483, 416.177917, -1318.597167, 17.392986, 0.000000, 0.000000, 212.600006, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18065, "ab_sfammumain", "shelf_glas", 0x00000000);
    SetDynamicObjectMaterial(showroomxts, 1, 14652, "ab_trukstpa", "CJ_WOOD6", 0x00000000);
    showroomxts = CreateDynamicObject(1483, 412.659820, -1313.096313, 17.393985, 0.000000, 0.000000, 212.600006, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18065, "ab_sfammumain", "shelf_glas", 0x00000000);
    SetDynamicObjectMaterial(showroomxts, 1, 14652, "ab_trukstpa", "CJ_WOOD6", 0x00000000);
    showroomxts = CreateDynamicObject(1483, 411.776062, -1311.714965, 17.394985, 0.000000, 0.000000, 212.600006, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 18065, "ab_sfammumain", "shelf_glas", 0x00000000);
    SetDynamicObjectMaterial(showroomxts, 1, 14652, "ab_trukstpa", "CJ_WOOD6", 0x00000000);
    showroomxts = CreateDynamicObject(19445, 400.915222, -1319.218627, 12.111387, 0.000000, 0.000000, 31.199989, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(showroomxts, 0, 16640, "a51", "concreteyellow256 copy", 0x00000000);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(638, 382.242675, -1342.901611, 15.177042, 0.000014, 0.000003, 90.999954, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1828, 379.655029, -1339.812866, 14.547045, 0.000014, -0.000003, 120.999954, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2010, 384.180847, -1342.576171, 14.547045, 0.000003, 0.000014, 30.999998, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1514, 396.978607, -1326.241455, 15.117037, -0.000014, 0.000003, -58.999977, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2435, 396.614715, -1325.633544, 13.817040, -0.000020, 0.000003, -58.999954, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2435, 397.093414, -1326.430175, 13.817040, -0.000020, 0.000003, -58.999954, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2435, 397.572753, -1327.227905, 13.817040, -0.000020, 0.000003, -58.999954, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2435, 398.052032, -1328.025512, 13.817040, -0.000020, 0.000003, -58.999954, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2435, 398.530975, -1328.822631, 13.817040, -0.000020, 0.000003, -58.999954, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2435, 399.010223, -1329.620239, 13.817040, -0.000020, 0.000003, -58.999954, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2435, 399.489379, -1330.417724, 13.817040, -0.000020, 0.000003, -58.999954, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2435, 399.968078, -1331.214355, 13.817040, -0.000020, 0.000003, -58.999954, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2309, 396.827545, -1335.279785, 13.817040, 0.000003, 0.000014, 30.999998, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2309, 394.907196, -1336.433715, 13.817040, 0.000003, 0.000014, 30.999998, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2309, 395.541503, -1336.052490, 13.817040, 0.000003, 0.000014, 30.999998, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2309, 396.184570, -1335.666137, 13.817040, 0.000003, 0.000014, 30.999998, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2010, 382.562316, -1324.375000, 13.817040, 0.000003, 0.000014, 30.999998, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2257, 380.284027, -1324.604125, 16.227041, 0.000003, 0.000014, 30.999998, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 384.627014, -1322.861083, 14.297039, 0.000003, 0.000014, 30.999998, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 388.543914, -1320.507568, 14.297039, 0.000003, 0.000014, 30.999998, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(948, 390.529357, -1319.030151, 13.817040, 0.000003, 0.000014, 30.999998, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2656, 386.164520, -1321.084350, 14.837053, 0.000003, 0.000014, 30.999998, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2010, 390.840148, -1338.563232, 13.818915, 0.000003, 0.000014, 30.999998, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1708, 399.316833, -1333.316406, 13.817038, -0.000003, -0.000014, -148.999938, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2010, 397.809234, -1334.424438, 13.817038, 0.000003, 0.000014, 30.999998, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(638, 399.098266, -1335.047729, 14.518913, -0.000014, 0.000003, -58.999977, -1, 0, -1, 200.00, 200.00); 
}