CreateIkeaInt()
{
    new STREAMER_TAG_OBJECT:kwdstx;
    kwdstx = CreateDynamicObject(18981, 1941.817993, -2457.840820, -5.713109, 0.000000, 630.000000, -90.000030, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(18981, 1966.818847, -2457.840820, -5.713109, -0.000007, 630.000000, -90.000007, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(18981, 1966.818847, -2482.829589, -5.713102, -0.000007, 630.000000, -90.000007, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(18981, 1991.789428, -2457.840820, -5.713109, -0.000014, 630.000000, -89.999984, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(18981, 1991.789428, -2482.829589, -5.713101, -0.000014, 630.000000, -89.999984, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(18981, 1971.308227, -2459.832763, -23.193075, -0.000014, 630.000000, -89.999984, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    kwdstx = CreateDynamicObject(18981, 1971.308227, -2484.832275, -23.193071, -0.000014, 630.000000, -89.999984, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    kwdstx = CreateDynamicObject(18981, 1946.318481, -2484.832275, -23.183069, -0.000014, 630.000000, -89.999984, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    kwdstx = CreateDynamicObject(18981, 1946.307495, -2459.832763, -23.193075, -0.000014, 630.000000, -89.999984, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    kwdstx = CreateDynamicObject(18981, 1921.310180, -2484.832275, -23.193069, -0.000022, 630.000000, -89.999961, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    kwdstx = CreateDynamicObject(18981, 1921.299194, -2459.832763, -23.193075, -0.000022, 630.000000, -89.999961, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    kwdstx = CreateDynamicObject(18981, 1933.557739, -2491.886474, -16.633148, -0.000014, 630.000000, -89.999984, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 9583, "bigshap_sfw", "shipfloor_sfw", 0x00000000);
    kwdstx = CreateDynamicObject(18981, 1980.389892, -2491.375976, -16.633148, -0.000029, 630.000000, -89.999938, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 9583, "bigshap_sfw", "shipfloor_sfw", 0x00000000);
    kwdstx = CreateDynamicObject(18981, 1980.389892, -2466.386718, -16.633148, -0.000029, 630.000000, -89.999938, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 9583, "bigshap_sfw", "shipfloor_sfw", 0x00000000);
    kwdstx = CreateDynamicObject(18981, 1923.847534, -2441.908203, -16.553146, -0.000014, 630.000000, -89.999984, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 9583, "bigshap_sfw", "shipfloor_sfw", 0x00000000);
    kwdstx = CreateDynamicObject(18981, 1990.228149, -2441.387939, -16.543151, -0.000014, 630.000000, -89.999984, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 9583, "bigshap_sfw", "shipfloor_sfw", 0x00000000);
    kwdstx = CreateDynamicObject(18981, 1984.296264, -2484.498046, -11.533096, 0.000014, -0.000022, 179.999786, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    kwdstx = CreateDynamicObject(18981, 1928.946655, -2457.797363, -11.533104, 0.000014, -0.000007, 179.999877, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    kwdstx = CreateDynamicObject(18981, 1946.359497, -2446.869384, -11.533075, 0.000044, 0.000000, 89.999862, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 15034, "genhotelsave", "walp57S", 0x00000000);
    kwdstx = CreateDynamicObject(18981, 1921.389892, -2446.869384, -11.533057, 0.000044, 0.000000, 89.999862, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 15034, "genhotelsave", "walp57S", 0x00000000);
    kwdstx = CreateDynamicObject(18981, 1975.478515, -2495.754150, -11.533117, 0.000022, 0.000000, 89.999931, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 15034, "genhotelsave", "walp57S", 0x00000000);
    kwdstx = CreateDynamicObject(18981, 1956.460205, -2503.554931, -16.623144, -0.000029, 630.000000, -89.999938, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 9583, "bigshap_sfw", "shipfloor_sfw", 0x00000000);
    kwdstx = CreateDynamicObject(18981, 1955.359252, -2495.794189, -3.703104, 0.000022, 0.000000, 89.999931, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 15034, "genhotelsave", "walp57S", 0x00000000);
    kwdstx = CreateDynamicObject(19482, 1940.505859, -2476.943603, -12.813097, 0.000000, 0.000000, 359.999938, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(kwdstx, 0, "TANDAS", 90, "Arial", 50, 0, 0xFF000000, 0x00000000, 1);
    kwdstx = CreateDynamicObject(18980, 1968.378051, -2466.358398, -16.600326, 0.000000, 90.000000, 90.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(18980, 1968.378051, -2478.568359, -16.600326, 0.000000, 90.000000, 90.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(18980, 1956.857177, -2491.538330, -16.590326, 0.000000, 90.000000, 180.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(18980, 1945.587158, -2478.668457, -16.600326, 0.000000, 90.000000, 90.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(18980, 1945.587158, -2466.867675, -16.600326, 0.000000, 90.000000, 90.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(18762, 1942.589477, -2454.885498, -16.605295, 0.000000, 90.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(18762, 1938.478637, -2454.885498, -16.605295, 0.000000, 90.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(18762, 1971.348999, -2454.364990, -16.605295, 0.000000, 90.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(18762, 1976.000122, -2454.364990, -16.605295, 0.000000, 90.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(970, 1939.375732, -2454.916015, -15.553151, -0.000007, 0.000007, 360.000061, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18996, "mattextures", "policeshieldgls", 0x00000000);
    SetDynamicObjectMaterial(kwdstx, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(18981, 1933.557739, -2466.897216, -16.633148, -0.000014, 630.000000, -89.999984, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 9583, "bigshap_sfw", "shipfloor_sfw", 0x00000000);
    kwdstx = CreateDynamicObject(970, 1943.545898, -2454.916015, -15.553151, -0.000007, 0.000007, 360.000061, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18996, "mattextures", "policeshieldgls", 0x00000000);
    SetDynamicObjectMaterial(kwdstx, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(970, 1945.655761, -2457.457275, -15.553151, -0.000007, 0.000007, 450.000061, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18996, "mattextures", "policeshieldgls", 0x00000000);
    SetDynamicObjectMaterial(kwdstx, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(970, 1945.655761, -2462.206787, -15.553151, -0.000007, 0.000007, 450.000061, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18996, "mattextures", "policeshieldgls", 0x00000000);
    SetDynamicObjectMaterial(kwdstx, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(970, 1945.655761, -2467.017333, -15.553151, 0.000000, 0.000006, 90.000038, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18996, "mattextures", "policeshieldgls", 0x00000000);
    SetDynamicObjectMaterial(kwdstx, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(970, 1945.655761, -2471.766845, -15.553151, 0.000000, 0.000006, 90.000038, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18996, "mattextures", "policeshieldgls", 0x00000000);
    SetDynamicObjectMaterial(kwdstx, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(970, 1945.655761, -2476.497070, -15.553151, 0.000007, 0.000006, 90.000015, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18996, "mattextures", "policeshieldgls", 0x00000000);
    SetDynamicObjectMaterial(kwdstx, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(970, 1945.655761, -2481.246582, -15.553151, 0.000007, 0.000006, 90.000015, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18996, "mattextures", "policeshieldgls", 0x00000000);
    SetDynamicObjectMaterial(kwdstx, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(970, 1945.655761, -2485.387207, -15.553151, 0.000014, 0.000006, 89.999992, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18996, "mattextures", "policeshieldgls", 0x00000000);
    SetDynamicObjectMaterial(kwdstx, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(970, 1945.655761, -2489.556396, -15.553151, 0.000014, 0.000006, 89.999992, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18996, "mattextures", "policeshieldgls", 0x00000000);
    SetDynamicObjectMaterial(kwdstx, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(18981, 1952.418457, -2495.774169, -34.253101, 0.000022, 0.000000, 89.999931, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    kwdstx = CreateDynamicObject(18981, 1937.478515, -2495.754150, -11.533102, 0.000022, 0.000000, 89.999931, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 15034, "genhotelsave", "walp57S", 0x00000000);
    kwdstx = CreateDynamicObject(970, 1968.426757, -2457.457275, -15.553151, 0.000000, 0.000006, 90.000038, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18996, "mattextures", "policeshieldgls", 0x00000000);
    SetDynamicObjectMaterial(kwdstx, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(970, 1968.426757, -2462.206787, -15.553151, 0.000000, 0.000006, 90.000038, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18996, "mattextures", "policeshieldgls", 0x00000000);
    SetDynamicObjectMaterial(kwdstx, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(18981, 1928.946655, -2482.795898, -11.533102, 0.000014, -0.000007, 179.999877, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    kwdstx = CreateDynamicObject(970, 1968.426757, -2467.017333, -15.553151, 0.000007, 0.000006, 90.000015, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18996, "mattextures", "policeshieldgls", 0x00000000);
    SetDynamicObjectMaterial(kwdstx, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(18981, 1971.338623, -2446.869384, -11.533094, 0.000044, 0.000000, 89.999862, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 15034, "genhotelsave", "walp57S", 0x00000000);
    kwdstx = CreateDynamicObject(970, 1968.426757, -2471.766845, -15.553151, 0.000007, 0.000006, 90.000015, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18996, "mattextures", "policeshieldgls", 0x00000000);
    SetDynamicObjectMaterial(kwdstx, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(970, 1968.426757, -2476.497070, -15.553151, 0.000014, 0.000006, 89.999992, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18996, "mattextures", "policeshieldgls", 0x00000000);
    SetDynamicObjectMaterial(kwdstx, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(970, 1968.426757, -2481.246582, -15.553151, 0.000014, 0.000006, 89.999992, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18996, "mattextures", "policeshieldgls", 0x00000000);
    SetDynamicObjectMaterial(kwdstx, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(970, 1968.426757, -2485.387207, -15.553151, 0.000023, 0.000006, 89.999969, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18996, "mattextures", "policeshieldgls", 0x00000000);
    SetDynamicObjectMaterial(kwdstx, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(970, 1968.426757, -2489.556396, -15.553151, 0.000023, 0.000006, 89.999969, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18996, "mattextures", "policeshieldgls", 0x00000000);
    SetDynamicObjectMaterial(kwdstx, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(18981, 1984.296264, -2459.577880, -11.533102, 0.000014, -0.000022, 179.999786, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    kwdstx = CreateDynamicObject(970, 1971.026611, -2454.405517, -15.553151, -0.000006, 0.000014, 0.000060, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18996, "mattextures", "policeshieldgls", 0x00000000);
    SetDynamicObjectMaterial(kwdstx, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(970, 1975.196777, -2454.405517, -15.553151, -0.000006, 0.000014, 0.000060, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18996, "mattextures", "policeshieldgls", 0x00000000);
    SetDynamicObjectMaterial(kwdstx, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(18981, 1941.817993, -2482.829589, -5.713102, 0.000000, 630.000000, -90.000030, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(970, 1965.286499, -2491.637695, -15.553151, 0.000023, 0.000000, 179.999923, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18996, "mattextures", "policeshieldgls", 0x00000000);
    SetDynamicObjectMaterial(kwdstx, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(970, 1961.126586, -2491.637695, -15.553151, 0.000023, 0.000000, 179.999923, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18996, "mattextures", "policeshieldgls", 0x00000000);
    SetDynamicObjectMaterial(kwdstx, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(970, 1956.956176, -2491.637695, -15.553151, 0.000023, -0.000007, 179.999877, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18996, "mattextures", "policeshieldgls", 0x00000000);
    SetDynamicObjectMaterial(kwdstx, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(970, 1952.786254, -2491.637695, -15.553151, 0.000023, -0.000007, 179.999877, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18996, "mattextures", "policeshieldgls", 0x00000000);
    SetDynamicObjectMaterial(kwdstx, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(970, 1948.616455, -2491.637695, -15.553151, 0.000023, -0.000007, 179.999877, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18996, "mattextures", "policeshieldgls", 0x00000000);
    SetDynamicObjectMaterial(kwdstx, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(19441, 1974.665405, -2469.532470, -18.883087, 0.000000, 0.000029, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    kwdstx = CreateDynamicObject(18762, 1977.276123, -2447.005371, -16.488834, 89.999992, 179.999984, 180.000030, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(19379, 1974.672485, -2491.108642, -22.273096, 0.000000, 0.000022, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 17588, "lae2coast_alpha", "plainglass", 0x00000000);
    kwdstx = CreateDynamicObject(19441, 1974.665405, -2485.466308, -21.133089, 0.000000, 0.000022, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    kwdstx = CreateDynamicObject(19441, 1974.665405, -2485.466308, -18.763086, 0.000000, 0.000022, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    kwdstx = CreateDynamicObject(19441, 1974.665405, -2484.455322, -17.823078, 89.999992, 90.000000, -89.999969, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    kwdstx = CreateDynamicObject(19441, 1974.665405, -2481.696044, -17.823078, 89.999992, 90.000000, -89.999969, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    kwdstx = CreateDynamicObject(19441, 1974.665405, -2480.753906, -21.133089, 0.000000, 0.000029, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    kwdstx = CreateDynamicObject(19441, 1974.665405, -2480.753906, -18.763086, 0.000000, 0.000029, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    kwdstx = CreateDynamicObject(19441, 1974.665405, -2484.455322, -18.233081, 89.999992, 90.000015, -89.999961, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    kwdstx = CreateDynamicObject(19441, 1974.665405, -2481.696044, -18.233081, 89.999992, 90.000015, -89.999961, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    kwdstx = CreateDynamicObject(19379, 1979.434448, -2480.017578, -22.273096, 0.000014, 0.000000, 89.999954, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18646, "matcolours", "grey", 0x00000000);
    kwdstx = CreateDynamicObject(19379, 1974.672485, -2475.174804, -22.273096, 0.000000, 0.000029, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 17588, "lae2coast_alpha", "plainglass", 0x00000000);
    kwdstx = CreateDynamicObject(19441, 1974.665405, -2469.532470, -21.133089, 0.000000, 0.000029, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    kwdstx = CreateDynamicObject(19441, 1974.665405, -2468.521484, -17.823078, 89.999992, 90.000015, -89.999961, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    kwdstx = CreateDynamicObject(19441, 1974.665405, -2465.762207, -17.823078, 89.999992, 90.000015, -89.999961, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    kwdstx = CreateDynamicObject(19441, 1974.665405, -2464.820068, -21.133089, 0.000000, 0.000037, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    kwdstx = CreateDynamicObject(19441, 1974.665405, -2464.820068, -18.763086, 0.000000, 0.000037, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    kwdstx = CreateDynamicObject(19441, 1974.665405, -2468.521484, -18.233081, 89.999992, 90.000030, -89.999961, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    kwdstx = CreateDynamicObject(19441, 1974.665405, -2465.762207, -18.233081, 89.999992, 90.000030, -89.999961, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    kwdstx = CreateDynamicObject(19379, 1979.394287, -2464.083740, -22.273096, 0.000029, 0.000000, 89.999908, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18646, "matcolours", "grey", 0x00000000);
    kwdstx = CreateDynamicObject(970, 1977.257812, -2447.396728, -15.453148, -0.000007, 0.000000, 270.000061, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18996, "mattextures", "policeshieldgls", 0x00000000);
    SetDynamicObjectMaterial(kwdstx, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(18762, 1936.395874, -2445.234863, -16.488834, 89.999992, 134.999984, -134.999938, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(970, 1936.335937, -2443.905273, -15.453148, -0.000007, 0.000007, 270.000061, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18996, "mattextures", "policeshieldgls", 0x00000000);
    SetDynamicObjectMaterial(kwdstx, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(970, 1977.257812, -2443.255859, -15.453148, -0.000007, 0.000000, 270.000061, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18996, "mattextures", "policeshieldgls", 0x00000000);
    SetDynamicObjectMaterial(kwdstx, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(18762, 1936.395874, -2447.724853, -16.488834, 89.999992, 134.999984, -134.999938, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(970, 1936.335937, -2447.985107, -15.453148, -0.000007, 0.000007, 270.000061, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18996, "mattextures", "policeshieldgls", 0x00000000);
    SetDynamicObjectMaterial(kwdstx, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(19379, 1936.463134, -2463.824951, -22.273096, 0.000014, 0.000000, -90.000015, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18646, "matcolours", "grey", 0x00000000);
    kwdstx = CreateDynamicObject(19379, 1927.662841, -2463.824951, -22.273096, 0.000014, 0.000000, -90.000015, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18646, "matcolours", "grey", 0x00000000);
    kwdstx = CreateDynamicObject(19379, 1941.184936, -2468.736083, -22.273096, 0.000000, 0.000007, 179.999755, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 17588, "lae2coast_alpha", "plainglass", 0x00000000);
    kwdstx = CreateDynamicObject(19441, 1941.192016, -2474.378417, -21.133089, 0.000000, 0.000007, 179.999755, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    kwdstx = CreateDynamicObject(19441, 1941.192016, -2474.378417, -18.763086, 0.000000, 0.000007, 179.999755, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    kwdstx = CreateDynamicObject(19441, 1941.192016, -2475.389404, -17.823078, 89.999992, 256.592864, -76.592872, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    kwdstx = CreateDynamicObject(19441, 1941.192016, -2478.148681, -17.823078, 89.999992, 256.592864, -76.592872, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    kwdstx = CreateDynamicObject(19441, 1941.192016, -2479.090820, -21.133089, 0.000000, 0.000014, 179.999755, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    kwdstx = CreateDynamicObject(19441, 1941.192016, -2479.090820, -18.763086, 0.000000, 0.000014, 179.999755, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    kwdstx = CreateDynamicObject(19441, 1941.192016, -2475.389404, -18.233081, 89.999992, 256.592895, -76.592872, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    kwdstx = CreateDynamicObject(19441, 1941.192016, -2478.148681, -18.233081, 89.999992, 256.592895, -76.592872, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    kwdstx = CreateDynamicObject(19379, 1936.463134, -2479.827148, -22.273096, 0.000007, 0.000000, -89.999992, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18646, "matcolours", "grey", 0x00000000);
    kwdstx = CreateDynamicObject(19379, 1926.833007, -2479.827148, -22.273096, 0.000014, 0.000000, -90.000015, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18646, "matcolours", "grey", 0x00000000);
    kwdstx = CreateDynamicObject(19379, 1941.184936, -2484.600585, -22.273096, 0.000000, 0.000000, 179.999710, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 17588, "lae2coast_alpha", "plainglass", 0x00000000);
    kwdstx = CreateDynamicObject(19441, 1941.192016, -2490.242919, -21.133089, 0.000000, 0.000000, 179.999710, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    kwdstx = CreateDynamicObject(19441, 1941.192016, -2490.242919, -18.763086, 0.000000, 0.000000, 179.999710, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    kwdstx = CreateDynamicObject(19441, 1941.192016, -2491.253906, -17.823078, 89.999992, 263.205902, -83.205886, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    kwdstx = CreateDynamicObject(19441, 1941.192016, -2494.013183, -17.823078, 89.999992, 263.205902, -83.205886, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    kwdstx = CreateDynamicObject(19441, 1941.192016, -2494.955322, -21.133089, 0.000000, 0.000007, 179.999710, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    kwdstx = CreateDynamicObject(19441, 1941.192016, -2494.955322, -18.763086, 0.000000, 0.000007, 179.999710, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    kwdstx = CreateDynamicObject(19441, 1941.192016, -2491.253906, -18.233081, 89.999992, 263.205932, -83.205886, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    kwdstx = CreateDynamicObject(19441, 1941.192016, -2494.013183, -18.233081, 89.999992, 263.205932, -83.205886, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    kwdstx = CreateDynamicObject(19482, 1974.525756, -2483.163330, -18.073101, 0.000000, 0.000000, 179.999954, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(kwdstx, 0, "TAMBANG", 90, "Arial", 50, 0, 0xFFFFFFFF, 0x00000000, 1);
    kwdstx = CreateDynamicObject(19379, 1975.363037, -2491.262939, -11.053101, 0.000000, 0.000014, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 17588, "lae2coast_alpha", "plainglass", 0x00000000);
    kwdstx = CreateDynamicObject(19441, 1975.355957, -2485.620605, -14.573095, 0.000000, 0.000014, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    kwdstx = CreateDynamicObject(19441, 1975.355957, -2485.620605, -12.203089, 0.000000, 0.000014, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    kwdstx = CreateDynamicObject(19441, 1975.355957, -2484.609619, -11.263081, 89.999992, 89.999992, -89.999977, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    kwdstx = CreateDynamicObject(19441, 1975.355957, -2481.850341, -11.263081, 89.999992, 89.999992, -89.999977, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    kwdstx = CreateDynamicObject(19441, 1975.355957, -2480.908203, -14.573095, 0.000000, 0.000022, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    kwdstx = CreateDynamicObject(19441, 1975.355957, -2480.908203, -12.203089, 0.000000, 0.000022, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    kwdstx = CreateDynamicObject(19441, 1975.355957, -2484.609619, -11.673086, 89.999992, 90.000000, -89.999969, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    kwdstx = CreateDynamicObject(19441, 1975.355957, -2481.850341, -11.673086, 89.999992, 90.000000, -89.999969, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    kwdstx = CreateDynamicObject(19379, 1980.084838, -2480.171875, -11.213091, 0.000029, 0.000000, 89.999908, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18646, "matcolours", "grey", 0x00000000);
    kwdstx = CreateDynamicObject(19441, 1975.325927, -2484.609619, -9.923081, 89.999992, 90.000000, -89.999969, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    kwdstx = CreateDynamicObject(19441, 1975.325927, -2481.850341, -9.923081, 89.999992, 90.000000, -89.999969, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    kwdstx = CreateDynamicObject(19458, 1980.042114, -2464.473144, -14.573148, 0.000007, 0.000000, 89.999977, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 1273, "icons3", "greengrad32", 0x00000000);
    kwdstx = CreateDynamicObject(19359, 1975.361083, -2466.118164, -14.393147, 0.000000, 0.000014, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 17588, "lae2coast_alpha", "plainglass", 0x00000000);
    kwdstx = CreateDynamicObject(19359, 1975.361083, -2469.318115, -14.393147, 0.000000, 0.000014, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 17588, "lae2coast_alpha", "plainglass", 0x00000000);
    kwdstx = CreateDynamicObject(1499, 1975.327270, -2470.933349, -16.133148, -0.000007, 0.000000, -89.999977, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(kwdstx, 1, 17588, "lae2coast_alpha", "plainglass", 0x00000000);
    kwdstx = CreateDynamicObject(1499, 1975.327270, -2473.933349, -16.133148, 0.000007, 0.000000, 89.999977, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(kwdstx, 1, 17588, "lae2coast_alpha", "plainglass", 0x00000000);
    kwdstx = CreateDynamicObject(19359, 1975.361083, -2475.539062, -14.403152, 0.000000, 0.000007, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 17588, "lae2coast_alpha", "plainglass", 0x00000000);
    kwdstx = CreateDynamicObject(19359, 1975.361083, -2478.749023, -14.603148, 0.000000, 0.000007, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    kwdstx = CreateDynamicObject(19438, 1975.310180, -2472.389160, -12.823148, 90.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 1273, "icons3", "greengrad32", 0x00000000);
    kwdstx = CreateDynamicObject(18766, 1980.105468, -2466.875976, -12.453145, 90.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(18766, 1980.105468, -2471.536132, -12.453145, 90.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(18766, 1980.105468, -2476.406738, -12.453145, 90.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(18766, 1980.105468, -2477.596923, -12.453145, 90.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(2435, 1975.908569, -2477.364501, -16.133148, 0.000000, 0.000000, 180.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 1, 1273, "icons3", "greengrad32", 0x00000000);
    kwdstx = CreateDynamicObject(2163, 1955.860473, -2460.586914, -22.693109, 0.000000, 0.000007, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    SetDynamicObjectMaterial(kwdstx, 1, 18646, "matcolours", "grey-50-percent", 0x00000000);
    kwdstx = CreateDynamicObject(2163, 1957.620727, -2460.586914, -22.693109, 0.000000, 0.000007, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    SetDynamicObjectMaterial(kwdstx, 1, 18646, "matcolours", "grey-50-percent", 0x00000000);
    kwdstx = CreateDynamicObject(19359, 1956.973999, -2460.481445, -23.483110, 0.000014, 0.000000, 89.999954, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    kwdstx = CreateDynamicObject(19359, 1957.294067, -2460.481445, -23.483110, 0.000014, 0.000000, 89.999954, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    kwdstx = CreateDynamicObject(18764, 1955.900390, -2462.894287, -25.165615, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 14652, "ab_trukstpa", "CJ_WOOD6", 0x00000000);
    kwdstx = CreateDynamicObject(18764, 1958.501220, -2462.894287, -25.144113, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 14652, "ab_trukstpa", "CJ_WOOD6", 0x00000000);
    kwdstx = CreateDynamicObject(18764, 1955.900390, -2465.676025, -25.143114, 0.000000, 0.000007, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 14652, "ab_trukstpa", "CJ_WOOD6", 0x00000000);
    kwdstx = CreateDynamicObject(18764, 1958.501220, -2465.676025, -25.154113, 0.000000, 0.000007, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 14652, "ab_trukstpa", "CJ_WOOD6", 0x00000000);
    kwdstx = CreateDynamicObject(2423, 1954.916015, -2460.856201, -22.835617, 0.000000, 0.000000, 180.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    SetDynamicObjectMaterial(kwdstx, 1, 18646, "matcolours", "grey-80-percent", 0x00000000);
    SetDynamicObjectMaterial(kwdstx, 2, 18646, "matcolours", "grey-80-percent", 0x00000000);
    SetDynamicObjectMaterial(kwdstx, 3, 18646, "matcolours", "grey-80-percent", 0x00000000);
    SetDynamicObjectMaterial(kwdstx, 4, 18646, "matcolours", "grey-80-percent", 0x00000000);
    kwdstx = CreateDynamicObject(2423, 1959.456420, -2460.936279, -22.835617, 0.000000, 0.000000, 450.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    SetDynamicObjectMaterial(kwdstx, 1, 18646, "matcolours", "grey-80-percent", 0x00000000);
    SetDynamicObjectMaterial(kwdstx, 2, 18646, "matcolours", "grey-80-percent", 0x00000000);
    SetDynamicObjectMaterial(kwdstx, 3, 18646, "matcolours", "grey-80-percent", 0x00000000);
    SetDynamicObjectMaterial(kwdstx, 4, 18646, "matcolours", "grey-80-percent", 0x00000000);
    kwdstx = CreateDynamicObject(19482, 1957.107666, -2460.377441, -22.243112, 0.000000, 0.000000, 90.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(kwdstx, 0, "INFORMASI", 120, "Arial", 25, 0, 0xFFFFFFFF, 0x00000000, 1);
    kwdstx = CreateDynamicObject(3498, 1957.163208, -2465.574707, -22.903110, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18646, "matcolours", "orange", 0x00000000);
    SetDynamicObjectMaterial(kwdstx, 1, 13007, "sw_bankint", "woodfloor1", 0x00000000);
    kwdstx = CreateDynamicObject(2162, 1958.862915, -2464.537353, -22.644113, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 1, 18646, "matcolours", "grey-80-percent", 0x00000000);
    kwdstx = CreateDynamicObject(2204, 1957.751464, -2465.077636, -22.643114, 0.000000, 0.000000, 180.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    kwdstx = CreateDynamicObject(3498, 1956.472778, -2465.574707, -23.763113, 0.000000, 0.000007, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18646, "matcolours", "orange", 0x00000000);
    SetDynamicObjectMaterial(kwdstx, 1, 13007, "sw_bankint", "woodfloor1", 0x00000000);
    kwdstx = CreateDynamicObject(3498, 1957.853027, -2465.574707, -23.763113, 0.000000, 0.000007, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18646, "matcolours", "orange", 0x00000000);
    SetDynamicObjectMaterial(kwdstx, 1, 13007, "sw_bankint", "woodfloor1", 0x00000000);
    kwdstx = CreateDynamicObject(3498, 1958.523193, -2465.574707, -24.463113, 0.000000, 0.000014, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18646, "matcolours", "orange", 0x00000000);
    SetDynamicObjectMaterial(kwdstx, 1, 13007, "sw_bankint", "woodfloor1", 0x00000000);
    kwdstx = CreateDynamicObject(3498, 1955.763183, -2465.574707, -24.463113, 0.000000, 0.000014, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18646, "matcolours", "orange", 0x00000000);
    SetDynamicObjectMaterial(kwdstx, 1, 13007, "sw_bankint", "woodfloor1", 0x00000000);
    kwdstx = CreateDynamicObject(3498, 1955.052612, -2465.574707, -25.233116, 0.000000, 0.000014, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18646, "matcolours", "orange", 0x00000000);
    SetDynamicObjectMaterial(kwdstx, 1, 13007, "sw_bankint", "woodfloor1", 0x00000000);
    kwdstx = CreateDynamicObject(3498, 1959.242553, -2465.574707, -25.233116, 0.000000, 0.000014, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18646, "matcolours", "orange", 0x00000000);
    SetDynamicObjectMaterial(kwdstx, 1, 13007, "sw_bankint", "woodfloor1", 0x00000000);
    kwdstx = CreateDynamicObject(3498, 1960.032714, -2465.574707, -25.873119, 0.000000, 0.000014, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18646, "matcolours", "orange", 0x00000000);
    SetDynamicObjectMaterial(kwdstx, 1, 13007, "sw_bankint", "woodfloor1", 0x00000000);
    kwdstx = CreateDynamicObject(3498, 1954.232543, -2465.574707, -25.873119, 0.000000, 0.000014, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18646, "matcolours", "orange", 0x00000000);
    SetDynamicObjectMaterial(kwdstx, 1, 13007, "sw_bankint", "woodfloor1", 0x00000000);
    kwdstx = CreateDynamicObject(19379, 1935.666748, -2463.743408, -11.213090, 0.000045, 0.000000, -90.000106, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18646, "matcolours", "grey", 0x00000000);
    kwdstx = CreateDynamicObject(19379, 1935.666748, -2480.526367, -11.213090, 0.000045, 0.000000, -90.000106, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18646, "matcolours", "grey", 0x00000000);
    kwdstx = CreateDynamicObject(18766, 1952.756347, -2470.863769, -24.033105, 0.000000, 0.000000, 90.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    kwdstx = CreateDynamicObject(18766, 1961.767700, -2470.863769, -24.033105, 0.000000, 0.000000, 90.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    kwdstx = CreateDynamicObject(19463, 1936.640747, -2472.338623, -14.533147, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(19379, 1926.035156, -2463.743408, -11.213090, 0.000037, 0.000000, -90.000083, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18646, "matcolours", "grey", 0x00000000);
    kwdstx = CreateDynamicObject(19379, 1926.085205, -2480.526367, -11.213089, 0.000037, 0.000000, -90.000083, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18646, "matcolours", "grey", 0x00000000);
    kwdstx = CreateDynamicObject(19359, 1940.397338, -2479.002929, -14.513148, 0.000000, 0.000007, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(19359, 1940.397338, -2475.862548, -14.513148, 0.000000, 0.000007, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(19359, 1940.397338, -2468.400878, -14.513148, 0.000000, 0.000014, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(19359, 1940.397338, -2465.260498, -14.513148, 0.000000, 0.000014, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(19463, 1931.840087, -2472.338623, -14.533147, 0.000000, 0.000000, 90.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(19395, 1935.104614, -2469.642822, -14.543148, 0.000000, 0.000000, 89.999969, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(19395, 1931.944458, -2469.642822, -14.543148, 0.000000, 0.000000, 89.999969, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(19395, 1928.744506, -2469.642822, -14.543148, 0.000000, 0.000000, 90.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(1502, 1934.307373, -2469.683837, -16.233152, 0.000000, 0.000000, -0.000075, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(kwdstx, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(1502, 1931.157714, -2469.683837, -16.233152, 0.000000, 0.000000, -0.000075, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(kwdstx, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(19395, 1931.847900, -2475.155761, -14.543148, 0.000000, 0.000000, -90.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(19395, 1935.008056, -2475.155761, -14.543148, 0.000000, 0.000000, -90.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(1502, 1932.645141, -2475.114746, -16.233152, 0.000000, 0.000000, 179.999816, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(kwdstx, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(1502, 1935.794799, -2475.114746, -16.233152, 0.000000, 0.000000, 179.999816, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(kwdstx, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(19395, 1928.744506, -2475.193359, -14.543148, 0.000000, 0.000000, 90.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(19359, 1933.567260, -2471.100830, -14.553149, 0.000000, 0.000007, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(19359, 1933.567260, -2473.661865, -14.553149, 0.000000, 0.000007, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(19441, 1975.325927, -2484.642578, -9.923081, 89.999992, 90.000015, -89.999961, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    kwdstx = CreateDynamicObject(19441, 1975.325927, -2481.883300, -9.923081, 89.999992, 90.000015, -89.999961, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    kwdstx = CreateDynamicObject(19441, 1975.325927, -2484.642578, -8.323078, 89.999992, 90.000030, -89.999961, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    kwdstx = CreateDynamicObject(19441, 1975.325927, -2481.883300, -8.323078, 89.999992, 90.000030, -89.999961, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    kwdstx = CreateDynamicObject(19441, 1975.325927, -2484.642578, -6.813076, 89.999992, 90.000045, -89.999961, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    kwdstx = CreateDynamicObject(19441, 1975.325927, -2481.883300, -6.813076, 89.999992, 90.000045, -89.999961, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    kwdstx = CreateDynamicObject(19441, 1975.325927, -2484.642578, -6.563076, 89.999992, 90.000061, -89.999961, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    kwdstx = CreateDynamicObject(19441, 1975.325927, -2481.883300, -6.563076, 89.999992, 90.000061, -89.999961, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    kwdstx = CreateDynamicObject(2435, 1976.818359, -2477.364501, -16.133148, 0.000000, 0.000000, 180.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 1, 1273, "icons3", "greengrad32", 0x00000000);
    kwdstx = CreateDynamicObject(2435, 1977.678710, -2477.364501, -16.133148, 0.000000, 0.000000, 180.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 1, 1273, "icons3", "greengrad32", 0x00000000);
    kwdstx = CreateDynamicObject(2434, 1978.790405, -2477.545410, -16.133148, 0.000000, 0.000000, 90.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 1, 1273, "icons3", "greengrad32", 0x00000000);
    kwdstx = CreateDynamicObject(18766, 1957.266723, -2466.333251, -24.033105, 0.000000, 0.000000, 180.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    kwdstx = CreateDynamicObject(18766, 1957.246459, -2475.394042, -24.033105, 0.000000, 0.000000, 180.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    kwdstx = CreateDynamicObject(18763, 1954.191040, -2473.428466, -24.553110, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 4835, "airoads_las", "grassdry_128HV", 0x00000000);
    kwdstx = CreateDynamicObject(18763, 1957.110961, -2473.428466, -24.553110, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 4835, "airoads_las", "grassdry_128HV", 0x00000000);
    kwdstx = CreateDynamicObject(18763, 1959.991210, -2473.428466, -24.553110, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 4835, "airoads_las", "grassdry_128HV", 0x00000000);
    kwdstx = CreateDynamicObject(18763, 1959.991210, -2470.568115, -24.553110, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 4835, "airoads_las", "grassdry_128HV", 0x00000000);
    kwdstx = CreateDynamicObject(18763, 1959.991210, -2467.778320, -24.554109, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 4835, "airoads_las", "grassdry_128HV", 0x00000000);
    kwdstx = CreateDynamicObject(18763, 1954.191040, -2470.458251, -24.553110, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 4835, "airoads_las", "grassdry_128HV", 0x00000000);
    kwdstx = CreateDynamicObject(19482, 1974.525756, -2467.243652, -18.073101, 0.000000, 0.000000, 179.999954, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(kwdstx, 0, "TEKSTIL", 90, "Arial", 50, 0, 0xFFFFFFFF, 0x00000000, 1);
    kwdstx = CreateDynamicObject(18763, 1954.191040, -2468.268066, -24.554109, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 4835, "airoads_las", "grassdry_128HV", 0x00000000);
    kwdstx = CreateDynamicObject(18763, 1956.971313, -2470.458251, -24.553110, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 4835, "airoads_las", "grassdry_128HV", 0x00000000);
    kwdstx = CreateDynamicObject(18763, 1957.161254, -2468.017578, -24.553110, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 4835, "airoads_las", "grassdry_128HV", 0x00000000);
    kwdstx = CreateDynamicObject(18766, 1960.196655, -2466.123535, -8.133103, 0.000000, 0.000000, 180.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    kwdstx = CreateDynamicObject(18766, 1953.906738, -2466.123535, -8.133103, 0.000000, 0.000000, 180.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    kwdstx = CreateDynamicObject(2662, 1950.802246, -2465.574707, -7.523101, 0.000000, 0.000000, 180.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 19267, "mapmarkers", "red-2", 0x00000000);
    kwdstx = CreateDynamicObject(2662, 1950.802246, -2465.574707, -8.469103, 0.000000, 0.000000, 180.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(2662, 1962.967529, -2466.652587, -7.523101, 0.000000, 0.000000, -0.000014, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 19267, "mapmarkers", "red-2", 0x00000000);
    kwdstx = CreateDynamicObject(2662, 1962.967529, -2466.652587, -8.469103, 0.000000, 0.000000, -0.000014, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(19482, 1950.802246, -2466.665039, -8.923107, -0.000007, 0.000000, -90.000007, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(kwdstx, 0, "IKEA", 120, "Arial", 143, 0, 0xFFFFFF00, 0x00000000, 1);
    kwdstx = CreateDynamicObject(19482, 1954.142089, -2466.665039, -8.923107, -0.000007, 0.000000, -90.000007, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(kwdstx, 0, "KOTA", 120, "Arial", 143, 0, 0xFFFFFF00, 0x00000000, 1);
    kwdstx = CreateDynamicObject(19437, 1930.694091, -2464.484619, -15.093147, 180.000000, 90.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(19437, 1934.194091, -2464.484619, -15.093147, 180.000000, 90.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(19325, 1932.592163, -2463.852539, -15.353149, 0.000000, 0.000000, 90.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18646, "matcolours", "grey-20-percent", 0x00000000);
    kwdstx = CreateDynamicObject(19437, 1930.694091, -2465.304931, -15.823144, 270.000000, 90.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(19437, 1934.183959, -2465.304931, -15.823144, 270.000000, 90.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(18762, 1933.423706, -2464.813720, -15.653147, 90.000000, 0.000000, 90.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(18762, 1933.423706, -2464.353271, -15.653147, 90.000000, 0.000000, 90.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(19437, 1934.194091, -2479.734619, -15.093147, 0.000000, 270.000000, -0.000045, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(19437, 1930.694091, -2479.734619, -15.093147, 0.000000, 270.000000, -0.000045, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(19325, 1932.296020, -2480.366699, -14.993144, 0.000000, 0.000000, -89.999969, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18646, "matcolours", "grey-20-percent", 0x00000000);
    kwdstx = CreateDynamicObject(19437, 1934.194091, -2478.914306, -15.823144, -89.999992, 90.039573, -179.960433, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(19437, 1930.704223, -2478.914306, -15.823144, -89.999992, 90.039573, -179.960433, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(18762, 1933.444946, -2479.405517, -15.653147, 89.999992, 269.802124, 0.197833, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(18762, 1931.464477, -2479.865966, -15.653147, 89.999992, 269.802124, 0.197833, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(18762, 1933.444946, -2479.946044, -15.653147, 89.999992, 269.802124, 0.197833, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(19359, 1940.397338, -2479.002929, -11.043147, 0.000000, 0.000007, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(19359, 1940.397338, -2475.862548, -11.043147, 0.000000, 0.000007, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(19359, 1940.397338, -2468.400878, -11.043147, 0.000000, 0.000014, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(19359, 1940.397338, -2465.260498, -11.043147, 0.000000, 0.000014, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(19463, 1931.840087, -2472.338623, -11.023145, 0.000007, 0.000000, 89.999977, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(19463, 1936.640747, -2472.338623, -11.073140, 0.000000, 0.000007, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(19359, 1933.567260, -2473.661865, -11.033149, 0.000000, 0.000007, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(19359, 1933.567260, -2471.100830, -11.093139, 0.000000, 0.000014, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(19379, 1935.666748, -2475.336181, -9.333088, 0.000045, 90.000000, -90.000106, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18646, "matcolours", "grey", 0x00000000);
    kwdstx = CreateDynamicObject(19379, 1935.626708, -2468.955810, -9.324090, 0.000045, 90.000000, -90.000106, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18646, "matcolours", "grey", 0x00000000);
    kwdstx = CreateDynamicObject(19379, 1925.996337, -2468.955810, -9.324093, 0.000045, 90.000000, -90.000106, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18646, "matcolours", "grey", 0x00000000);
    kwdstx = CreateDynamicObject(19379, 1926.046386, -2475.336181, -9.333095, 0.000045, 90.000000, -90.000106, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 18646, "matcolours", "grey", 0x00000000);
    kwdstx = CreateDynamicObject(19482, 1936.744018, -2473.653808, -14.053149, 0.000000, 0.000000, 360.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(kwdstx, 0, "PRIA", 130, "Arial", 25, 0, 0xFF000000, 0x00000000, 1);
    kwdstx = CreateDynamicObject(19482, 1975.265991, -2483.163330, -11.053100, 0.000000, 0.000000, 179.999954, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(kwdstx, 0, "RECYCLE", 90, "Arial", 50, 0, 0xFFFFFFFF, 0x00000000, 1);
    kwdstx = CreateDynamicObject(19482, 1958.821899, -2466.665039, -8.923107, -0.000007, 0.000000, -90.000007, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(kwdstx, 0, "ATHERLIFE", 120, "Arial", 143, 0, 0xFFFF66CC, 0x00000000, 1);
    kwdstx = CreateDynamicObject(19482, 1952.172241, -2466.665039, -7.133108, -0.000007, 0.000000, -90.000007, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(kwdstx, 0, "SELAMAT", 130, "Arial", 140, 0, 0xFFFFFFFF, 0x00000000, 1);
    kwdstx = CreateDynamicObject(19482, 1957.581054, -2466.665039, -7.133108, -0.000007, 0.000000, -90.000007, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(kwdstx, 0, "DATANG", 130, "Arial", 140, 0, 0xFFFFFFFF, 0x00000000, 1);
    kwdstx = CreateDynamicObject(19482, 1960.730712, -2466.665039, -7.133108, -0.000007, 0.000000, -90.000007, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(kwdstx, 0, "DI", 130, "Arial", 140, 0, 0xFFFFFFFF, 0x00000000, 1);
    kwdstx = CreateDynamicObject(19379, 1959.610229, -2495.726806, -19.682897, 0.000000, 0.000000, 90.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 17588, "lae2coast_alpha", "plainglass", 0x00000000);
    kwdstx = CreateDynamicObject(19379, 1949.980590, -2495.726806, -19.682897, 0.000000, 0.000000, 90.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 17588, "lae2coast_alpha", "plainglass", 0x00000000);
    kwdstx = CreateDynamicObject(19482, 1962.967529, -2465.562255, -8.923107, -0.000007, 0.000000, 89.999961, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(kwdstx, 0, "IKEA", 120, "Arial", 143, 0, 0xFFFFFF00, 0x00000000, 1);
    kwdstx = CreateDynamicObject(19482, 1959.627685, -2465.562255, -8.923107, -0.000007, 0.000000, 89.999961, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(kwdstx, 0, "KOTA", 120, "Arial", 143, 0, 0xFFFFFF00, 0x00000000, 1);
    kwdstx = CreateDynamicObject(19482, 1954.947875, -2465.562255, -8.923107, -0.000007, 0.000000, 89.999961, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(kwdstx, 0, "ATHERLIFE", 120, "Arial", 143, 0, 0xFFFF66CC, 0x00000000, 1);
    kwdstx = CreateDynamicObject(19482, 1961.597534, -2465.562255, -7.133108, -0.000007, 0.000000, 89.999961, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(kwdstx, 0, "SELAMAT", 130, "Arial", 140, 0, 0xFFFFFFFF, 0x00000000, 1);
    kwdstx = CreateDynamicObject(19482, 1956.188720, -2465.562255, -7.133108, -0.000007, 0.000000, 89.999961, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(kwdstx, 0, "DATANG", 130, "Arial", 140, 0, 0xFFFFFFFF, 0x00000000, 1);
    kwdstx = CreateDynamicObject(19482, 1953.039062, -2465.562255, -7.133108, -0.000007, 0.000000, 89.999961, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(kwdstx, 0, "DI", 130, "Arial", 140, 0, 0xFFFFFFFF, 0x00000000, 1);
    kwdstx = CreateDynamicObject(19128, 1952.556762, -2447.344482, -14.843111, 90.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 6354, "sunset03_law2", "billLA02", 0x00000000);
    kwdstx = CreateDynamicObject(19128, 1962.176757, -2447.344482, -14.843111, 90.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 6357, "sunstrans_law2", "SunBillB10", 0x00000000);
    kwdstx = CreateDynamicObject(18766, 1934.430664, -2458.798828, -19.863964, 0.000000, 0.000000, 90.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 1717, "cj_tv", "green_glass_64", 0x00000000);
    kwdstx = CreateDynamicObject(18766, 1929.620849, -2452.337402, -19.863964, 0.000000, 0.000000, 180.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 1717, "cj_tv", "green_glass_64", 0x00000000);
    kwdstx = CreateDynamicObject(18766, 1929.620849, -2453.307373, -19.863964, 0.000000, 0.000000, 180.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 1717, "cj_tv", "green_glass_64", 0x00000000);
    kwdstx = CreateDynamicObject(1280, 1937.320556, -2461.328857, -22.393104, 0.000000, 0.000000, 180.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(1280, 1937.320556, -2456.388916, -22.393104, 0.000000, 0.000000, 180.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(18766, 1979.114990, -2458.994140, -24.483110, 0.000000, 0.000000, 90.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(18766, 1979.114990, -2451.563232, -24.483110, 0.000000, 0.000000, 90.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(18764, 1982.125244, -2461.531005, -24.503112, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 4835, "airoads_las", "grassdry_128HV", 0x00000000);
    kwdstx = CreateDynamicObject(18764, 1982.125244, -2456.600830, -24.503112, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 4835, "airoads_las", "grassdry_128HV", 0x00000000);
    kwdstx = CreateDynamicObject(18764, 1982.125244, -2451.650634, -24.503112, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 4835, "airoads_las", "grassdry_128HV", 0x00000000);
    kwdstx = CreateDynamicObject(18764, 1982.125244, -2446.711181, -24.503112, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 4835, "airoads_las", "grassdry_128HV", 0x00000000);
    kwdstx = CreateDynamicObject(19482, 1941.294677, -2492.644287, -18.283103, 0.000000, 0.000007, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(kwdstx, 0, "DAGING", 90, "Arial", 50, 0, 0xFFFFFFFF, 0x00000000, 1);
    kwdstx = CreateDynamicObject(1280, 1978.060791, -2461.328857, -22.393104, 0.000000, 0.000000, 360.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(1280, 1978.060791, -2453.819091, -22.393104, 0.000000, 0.000000, 360.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(19128, 1959.946777, -2495.225097, -9.173110, 89.999992, 224.950515, -44.950569, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 6354, "sunset03_law2", "billLA02", 0x00000000);
    kwdstx = CreateDynamicObject(19128, 1953.146850, -2495.225097, -9.173110, 89.999992, 224.950515, -44.950569, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 6357, "sunstrans_law2", "SunBillB10", 0x00000000);
    kwdstx = CreateDynamicObject(19482, 1941.294677, -2476.713134, -18.283103, 0.000000, 0.000007, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(kwdstx, 0, "MEBEL", 90, "Arial", 50, 0, 0xFFFFFFFF, 0x00000000, 1);
    kwdstx = CreateDynamicObject(19482, 1936.744018, -2470.272949, -14.053149, 0.000000, 0.000000, 360.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(kwdstx, 0, "WANITA", 130, "Arial", 25, 0, 0xFF000000, 0x00000000, 1);
    kwdstx = CreateDynamicObject(19359, 1940.397338, -2479.002929, -7.633135, 0.000000, 0.000014, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(19359, 1940.397338, -2475.862548, -7.633135, 0.000000, 0.000014, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(19359, 1940.397338, -2468.400878, -7.633135, 0.000000, 0.000022, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(19359, 1940.397338, -2465.260498, -7.633135, 0.000000, 0.000022, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(19359, 1940.397338, -2473.531738, -7.633135, 0.000000, 0.000014, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(19359, 1940.397338, -2470.781982, -7.633135, 0.000000, 0.000014, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(1280, 1977.446533, -2463.887695, -15.773825, 0.000000, 0.000000, -90.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(1280, 1981.246582, -2463.887695, -15.773825, 0.000000, 0.000000, -90.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(1280, 1983.476928, -2461.587890, -15.773825, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(1280, 1983.476928, -2458.247070, -15.773825, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    kwdstx = CreateDynamicObject(18981, 1939.996582, -2493.085449, -3.903100, 0.000014, -0.000007, 179.999877, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    kwdstx = CreateDynamicObject(19128, 1975.145629, -2472.411132, -11.463145, 90.000000, 0.000000, 270.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    kwdstx = CreateDynamicObject(19128, 1940.449829, -2491.107421, -11.103151, 90.000000, 0.000000, 90.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 17535, "lae2billboards", "SunBillB03", 0x00000000);
    kwdstx = CreateDynamicObject(19128, 1940.449829, -2484.197753, -11.103151, 90.000000, 0.000000, 90.000000, 543, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kwdstx, 0, 6349, "sunbill_law2", "SunBillB02", 0x00000000);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(1776, 1983.434448, -2469.671386, -15.133334, 0.000000, 0.000000, 270.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1209, 1983.519287, -2470.897949, -16.167173, 0.000000, 0.000000, 270.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11729, 1932.090820, -2494.936767, -22.693105, 0.000000, 0.000000, 180.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2436, 1929.836669, -2487.006347, -22.693105, 0.000007, 0.000000, 89.999977, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2436, 1929.836669, -2490.328125, -22.693105, 0.000000, 0.000000, 90.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2436, 1929.836669, -2488.667236, -22.693105, 0.000000, 0.000000, 90.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 1968.501831, -2454.754150, -15.965353, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 1945.631225, -2455.154541, -15.965353, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 1967.851806, -2491.634277, -15.965353, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 1946.111083, -2491.634277, -15.965353, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(4569, 1956.237182, -2538.704833, -9.852230, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2012, 1978.481689, -2468.010253, -16.153150, 0.000000, 0.000000, 90.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2012, 1978.491699, -2472.780517, -16.153150, 0.000000, 0.000000, 90.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1514, 1934.215698, -2492.682617, -21.483350, 0.000000, 0.000000, 90.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1514, 1934.215698, -2477.651611, -21.483350, 0.000000, 0.000000, 90.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1514, 1979.835205, -2466.900634, -21.483350, 0.000000, 0.000000, 270.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1514, 1979.835205, -2482.709472, -21.483350, 0.000000, 0.000000, 270.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1514, 1979.835205, -2482.799560, -15.003355, 0.000000, 0.000000, 270.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, 1975.990356, -2494.389160, -22.337310, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3586, 1971.736328, -2451.489990, -19.433103, 0.000000, -0.000014, 89.999908, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3586, 1942.586303, -2452.206054, -19.443103, 0.000000, -0.000014, 269.999908, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2804, 1929.976318, -2490.467529, -21.823099, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19639, 1934.101684, -2494.687744, -21.643106, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19639, 1934.101684, -2494.687744, -21.503107, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19639, 1934.101684, -2494.687744, -21.383106, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(0, 0.000000, 0.000000, 0.000000, 0.000000, 0.000000, 0.000000, 543, 0, -1, 0.00, 0.00); 
    CreateDynamicObject(19639, 1934.101684, -2493.921386, -21.423864, -31.400011, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(916, 1933.347412, -2491.550048, -22.543104, 0.000000, 0.000000, 90.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(916, 1933.347412, -2491.550048, -22.363100, 0.000000, 0.000000, 90.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(916, 1933.347412, -2491.550048, -22.223096, 0.000000, 0.000000, 90.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2804, 1933.350463, -2491.544189, -22.213092, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2804, 1934.220703, -2494.704833, -21.343084, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2804, 1934.130615, -2493.868164, -21.338987, -34.300003, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19811, 1934.167236, -2491.463623, -21.583097, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(916, 1933.957763, -2491.550048, -21.553098, 0.000000, 0.000000, 180.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19811, 1934.167236, -2491.673828, -21.583097, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19811, 1933.837402, -2491.463623, -21.583097, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19811, 1933.837402, -2491.683837, -21.583097, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2804, 1929.976318, -2489.687988, -21.823099, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1569, 1955.891479, -2447.348388, -22.793109, 0.000000, 0.000014, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1569, 1958.891601, -2447.348388, -22.793109, 0.000000, -0.000014, 179.999908, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2804, 1929.976318, -2488.756103, -21.823099, 0.000000, 0.000007, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2804, 1929.976318, -2487.976562, -21.823099, 0.000000, 0.000007, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2413, 1937.294433, -2486.510009, -22.693111, 0.000000, 0.000000, 90.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2413, 1937.294433, -2484.889648, -22.693111, 0.000000, 0.000000, 90.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2413, 1936.584350, -2484.239013, -22.693111, 0.000000, 0.000000, 270.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2413, 1936.584350, -2485.870361, -22.693111, 0.000000, 0.000000, 270.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2413, 1936.624389, -2487.370849, -22.693111, 0.000000, 0.000000, 360.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2413, 1937.284667, -2483.370361, -22.693111, 0.000000, 0.000000, 540.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2804, 1936.502319, -2484.230957, -22.423101, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2804, 1936.502319, -2484.951660, -22.103097, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2806, 1936.532104, -2486.179199, -22.053104, 0.000000, 0.000007, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2806, 1937.242919, -2486.164550, -22.053104, 0.000000, 0.000007, 179.999893, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2804, 1937.272705, -2485.811523, -22.423101, 0.000000, 0.000007, 179.999893, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2806, 1937.242919, -2484.442871, -22.053104, 0.000000, 0.000000, 179.999847, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2804, 1937.272705, -2484.810546, -22.423101, 0.000000, 0.000000, 179.999847, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2806, 1936.940185, -2483.305419, -22.053104, -0.000007, -0.000007, -90.000114, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2804, 1937.307861, -2483.275634, -22.423101, -0.000007, -0.000007, -90.000114, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2804, 1936.587158, -2487.367187, -22.423101, -0.000007, -0.000007, 89.999824, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2804, 1937.307861, -2487.367187, -22.423101, -0.000007, -0.000007, 89.999824, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2804, 1929.976318, -2487.095214, -21.823099, 0.000000, 0.000014, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2804, 1929.976318, -2486.315673, -21.823099, 0.000000, 0.000014, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2436, 1929.836669, -2485.335205, -22.693105, 0.000014, 0.000000, 89.999954, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19943, 1968.984741, -2455.280761, -24.103111, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19943, 1944.583496, -2455.541015, -24.103111, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2804, 1929.976318, -2485.424072, -21.823099, 0.000000, 0.000022, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2804, 1929.976318, -2484.644531, -21.823099, 0.000000, 0.000022, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2436, 1929.836669, -2483.645019, -22.693105, 0.000022, 0.000000, 89.999931, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2804, 1929.976318, -2483.733886, -21.823099, 0.000000, 0.000029, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2804, 1929.976318, -2482.954345, -21.823099, 0.000000, 0.000029, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(931, 1938.986816, -2464.843750, -21.683107, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1463, 1939.037841, -2464.873779, -22.503107, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2435, 1979.756835, -2480.557128, -22.693109, 0.000000, 0.000000, -89.999969, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2435, 1979.756835, -2481.447753, -22.693109, 0.000000, 0.000000, -89.999969, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2435, 1979.756835, -2482.328613, -22.693109, 0.000000, 0.000000, -89.999969, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2435, 1979.756835, -2483.228271, -22.693109, 0.000000, 0.000000, -89.999969, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1671, 1957.085327, -2462.220458, -22.263113, 0.000000, 0.000000, 180.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2434, 1979.741088, -2484.127441, -22.683111, 0.000000, 0.000000, -89.700004, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11729, 1981.330932, -2480.396240, -22.693105, 0.000000, 0.000000, 360.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11729, 1982.031127, -2480.396240, -22.693105, 0.000000, 0.000000, 360.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1431, 1983.408447, -2488.447998, -22.143102, 0.000000, 0.000000, -90.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(931, 1982.740478, -2490.797119, -21.613086, 0.000000, 0.000000, 270.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3929, 1982.557861, -2491.371337, -22.143096, 0.000000, 0.000000, 110.999984, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3930, 1982.844726, -2490.059082, -21.213169, 47.599990, -39.200000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2973, 1982.413452, -2493.708984, -22.693103, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2413, 1978.287231, -2488.035400, -22.693109, 0.000000, 0.000000, 270.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2413, 1978.287231, -2489.645019, -22.693109, 0.000000, 0.000000, 270.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2770, 1933.788940, -2490.651855, -22.093095, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19941, 1978.284301, -2487.660644, -21.823099, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19941, 1978.284301, -2488.151123, -21.823099, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19941, 1978.284301, -2488.271240, -21.823099, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19941, 1978.284301, -2488.521484, -21.823099, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19941, 1978.284301, -2488.821777, -21.823099, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2514, 1935.225708, -2471.687011, -16.133150, 0.000000, 0.000000, 180.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2514, 1931.806274, -2471.687011, -16.133150, 0.000000, 0.000000, 180.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2514, 1935.225708, -2472.877685, -16.133150, 0.000000, 0.000000, 360.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19941, 1978.284301, -2489.300781, -21.823099, 0.000000, 0.000007, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19941, 1978.284301, -2489.390869, -21.823099, 0.000000, 0.000007, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19941, 1978.284301, -2489.911376, -21.823099, 0.000000, 0.000007, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19941, 1978.284301, -2490.311767, -21.823099, 0.000000, 0.000007, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2063, 1983.445800, -2482.723388, -21.843114, 0.000000, 0.000000, 270.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1449, 1983.001342, -2482.801757, -22.243103, 0.000000, 0.000000, -90.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1555, 1983.806884, -2479.419433, -16.133148, 0.000000, 0.000000, 90.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2620, 1979.816650, -2477.861816, -15.393149, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2578, 1976.153686, -2479.952880, -14.523146, 0.000000, 0.000000, 180.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2579, 1977.221679, -2479.940429, -14.533145, 0.000000, 0.000000, 180.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2585, 1978.302246, -2479.950683, -14.533145, 0.000000, 0.000000, 180.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2435, 1979.756835, -2464.676513, -22.693109, -0.000007, 0.000000, -89.999946, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2435, 1979.756835, -2465.567138, -22.693109, -0.000007, 0.000000, -89.999946, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2435, 1979.756835, -2466.447998, -22.693109, -0.000007, 0.000000, -89.999946, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2435, 1979.756835, -2467.347656, -22.693109, -0.000007, 0.000000, -89.999946, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2434, 1979.741088, -2468.246826, -22.683111, -0.000007, 0.000000, -89.699981, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11729, 1981.330932, -2464.515625, -22.693105, 0.000000, 0.000007, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11729, 1982.031127, -2464.515625, -22.693105, 0.000000, 0.000007, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2372, 1983.583984, -2471.228271, -22.693105, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2394, 1983.352172, -2470.690185, -21.953102, 0.000000, 0.000000, 90.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2372, 1983.583984, -2473.469238, -22.693105, 0.000000, 0.000007, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2372, 1983.583984, -2475.749023, -22.693105, 0.000000, 0.000014, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2394, 1983.352172, -2475.210937, -21.953102, 0.000014, 0.000000, 89.999954, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2372, 1983.583984, -2478.099365, -22.693105, 0.000000, 0.000022, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2371, 1979.096191, -2472.885498, -22.693109, 0.000000, 0.000000, 90.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2392, 1979.033691, -2472.480468, -21.953109, 0.000000, 0.000000, 270.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2392, 1978.103515, -2473.160644, -21.953109, 0.000000, 0.000000, 450.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2371, 1979.096191, -2475.795898, -22.693109, 0.000007, 0.000000, 89.999977, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2392, 1978.103637, -2475.390869, -21.953109, 0.000007, 0.000000, 89.999977, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2392, 1979.033691, -2476.071044, -21.953109, -0.000007, 0.000000, -89.999977, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2621, 1976.993530, -2475.644042, -21.953105, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2620, 1977.002319, -2472.918457, -21.933105, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2387, 1981.953979, -2479.287597, -22.693105, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2387, 1977.973999, -2479.287597, -22.693105, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(869, 1959.109741, -2468.818847, -21.683105, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(869, 1955.369750, -2468.818847, -21.683105, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(869, 1955.369750, -2472.768554, -21.683105, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(869, 1959.140258, -2472.768554, -21.683105, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(673, 1957.201782, -2470.937011, -24.043109, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2514, 1931.715454, -2472.877685, -16.133150, 0.000000, 0.000000, 360.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2602, 1931.120849, -2464.744873, -15.323147, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2602, 1933.570922, -2464.744873, -15.323147, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2602, 1933.767333, -2479.474365, -15.323147, 0.000000, 0.000000, 179.999893, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2435, 1979.756835, -2480.637207, -16.213111, -0.000014, 0.000000, -89.999923, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2435, 1979.756835, -2481.527832, -16.213111, -0.000014, 0.000000, -89.999923, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2435, 1979.756835, -2482.408691, -16.213111, -0.000014, 0.000000, -89.999923, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2435, 1979.756835, -2483.308349, -16.213111, -0.000014, 0.000000, -89.999923, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2434, 1979.741088, -2484.207519, -16.203113, -0.000014, 0.000000, -89.699958, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11729, 1981.330932, -2480.396240, -16.213108, 0.000000, 0.000007, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11729, 1982.031127, -2480.396240, -16.213108, 0.000000, 0.000007, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19842, 1929.459228, -2457.953125, -29.223117, 90.000000, 0.000000, 90.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19842, 1929.459228, -2457.953125, -29.223117, 90.000000, 0.000000, 90.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19842, 1921.889038, -2457.953125, -22.663118, 360.000000, 180.000000, 270.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(744, 1934.815185, -2461.958984, -27.143106, 0.000000, 0.000000, 90.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(744, 1934.445068, -2457.969238, -27.143106, 0.000000, 0.000000, 180.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(744, 1933.775146, -2455.519042, -27.143106, 0.000000, 0.000000, 270.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(744, 1933.775146, -2453.938720, -27.143106, 0.000000, 0.000000, 360.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(744, 1931.325073, -2452.608154, -27.143106, 0.000000, 0.000000, 450.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(870, 1982.693359, -2448.916748, -21.733110, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(870, 1981.113281, -2448.916748, -21.733110, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(870, 1981.113281, -2450.796875, -21.733110, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(870, 1981.113281, -2453.058349, -21.733110, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(870, 1981.533569, -2455.448242, -21.733110, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(870, 1981.533569, -2458.038085, -21.733110, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(870, 1981.533569, -2460.918212, -21.733110, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14402, 1981.696166, -2450.065673, -22.183113, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14402, 1981.696166, -2453.356689, -22.183113, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2435, 1934.229614, -2495.098144, -22.693109, 0.000007, 0.000000, 89.999977, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2435, 1934.229614, -2494.207519, -22.693109, 0.000007, 0.000000, 89.999977, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2435, 1934.229614, -2493.326660, -22.693109, 0.000007, 0.000000, 89.999977, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2435, 1934.229614, -2492.427001, -22.693109, 0.000007, 0.000000, 89.999977, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2434, 1934.245361, -2491.527832, -22.683111, 0.000007, 0.000000, 90.299942, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14402, 1981.696166, -2457.567382, -22.183113, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14402, 1981.696166, -2461.038085, -22.183113, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2912, 1930.317260, -2480.442382, -22.693105, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2912, 1931.007446, -2480.442382, -22.693105, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2912, 1931.767333, -2480.442382, -22.693105, 0.000000, 0.000007, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2912, 1932.457519, -2480.442382, -22.693105, 0.000000, 0.000007, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2912, 1930.927001, -2480.442382, -21.953102, 0.000000, 0.000014, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2912, 1931.617187, -2480.442382, -21.953102, 0.000000, 0.000014, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11729, 1932.771118, -2494.936767, -22.693105, 0.000000, 0.000000, 180.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2435, 1934.229614, -2479.276855, -22.693109, 0.000014, 0.000000, 89.999954, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2435, 1934.229614, -2478.386230, -22.693109, 0.000014, 0.000000, 89.999954, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2435, 1934.229614, -2477.505371, -22.693109, 0.000014, 0.000000, 89.999954, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2435, 1934.229614, -2476.605712, -22.693109, 0.000014, 0.000000, 89.999954, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2434, 1934.245361, -2475.706542, -22.683111, 0.000014, 0.000000, 90.299919, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1463, 1930.123535, -2476.945312, -22.403108, 0.000000, 0.000000, 90.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1463, 1930.123535, -2475.155029, -22.403108, 0.000000, 0.000000, 90.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14872, 1930.826904, -2472.898193, -22.283107, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14872, 1930.826904, -2470.828125, -22.283107, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(842, 1930.932617, -2471.765380, -22.693107, 0.000000, 0.000000, -84.699981, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(942, 1932.179565, -2465.122802, -20.313095, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1463, 1930.473510, -2467.534912, -22.403108, 0.000000, 0.000000, 180.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2973, 1936.315795, -2465.380615, -22.693109, 0.000000, 0.000000, 90.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1431, 1983.408447, -2486.248046, -15.513102, -0.000007, 0.000000, -89.999977, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1463, 1936.664184, -2465.504150, -19.953098, 0.000000, 0.000000, 180.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(931, 1982.740478, -2490.797119, -15.043087, -0.000007, 0.000000, -89.999977, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2973, 1982.413452, -2493.708984, -16.063104, 0.000000, 0.000007, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2063, 1983.445800, -2482.723388, -15.213114, -0.000007, 0.000000, -89.999977, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3632, 1983.209716, -2489.207519, -15.663144, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3632, 1982.509765, -2489.207519, -15.663144, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3632, 1982.509765, -2488.586914, -15.663144, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3632, 1983.170043, -2488.586914, -15.663144, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, 1979.414428, -2493.056884, -15.423143, 0.000000, 0.000000, 180.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2977, 1980.218627, -2490.411621, -16.133144, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2578, 1979.393676, -2479.952880, -14.523146, 0.000000, -0.000007, 179.999954, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2579, 1980.461669, -2479.940429, -14.533145, 0.000000, -0.000007, 179.999954, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2585, 1981.542236, -2479.950683, -14.533145, 0.000000, -0.000007, 179.999954, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1775, 1982.814941, -2479.700439, -15.023147, 0.000000, 0.000000, 180.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2582, 1976.314453, -2464.860351, -15.343148, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2583, 1977.820800, -2464.847656, -15.383147, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2621, 1979.247558, -2465.022216, -15.403147, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2012, 1982.252075, -2465.049560, -16.133150, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2621, 1980.147705, -2465.022216, -15.403147, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1850, 1977.608764, -2468.251464, -16.133150, 0.000000, 0.000000, 270.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1850, 1977.608764, -2473.021728, -16.133150, 0.000000, 0.000000, 270.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2941, 1977.515014, -2477.351806, -14.843148, 0.000000, 0.000000, 180.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1513, 1978.565185, -2477.514892, -14.793148, 0.000000, 0.000000, 180.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19570, 1977.808837, -2474.020263, -14.533145, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19570, 1977.548583, -2474.020263, -14.533145, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19570, 1977.808837, -2473.880126, -14.533145, 0.000000, 0.000007, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19570, 1977.548583, -2473.880126, -14.533145, 0.000000, 0.000007, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19570, 1977.808837, -2473.719970, -14.533145, 0.000000, 0.000014, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19570, 1977.548583, -2473.719970, -14.533145, 0.000000, 0.000014, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19570, 1977.808837, -2473.559814, -14.533145, 0.000000, 0.000022, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19570, 1977.548583, -2473.559814, -14.533145, 0.000000, 0.000022, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19569, 1977.796020, -2473.336181, -14.523142, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19569, 1977.585937, -2473.336181, -14.523142, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19569, 1977.796020, -2473.196044, -14.523142, 0.000000, 0.000007, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19569, 1977.585937, -2473.196044, -14.523142, 0.000000, 0.000007, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19569, 1977.796020, -2473.036132, -14.523142, 0.000000, 0.000014, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19569, 1977.585937, -2473.036132, -14.523142, 0.000000, 0.000014, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19569, 1977.796020, -2472.895996, -14.523142, 0.000000, 0.000022, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19569, 1977.585937, -2472.895996, -14.523142, 0.000000, 0.000022, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19563, 1977.769165, -2472.638916, -14.563144, 0.000000, 0.000000, 90.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19563, 1977.599121, -2472.638916, -14.563144, 0.000000, 0.000000, 90.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19563, 1977.769165, -2472.438720, -14.563144, 0.000007, 0.000000, 89.999977, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19563, 1977.599121, -2472.438720, -14.563144, 0.000007, 0.000000, 89.999977, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19563, 1977.769165, -2472.248535, -14.563144, 0.000014, 0.000000, 89.999954, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19563, 1977.599121, -2472.248535, -14.563144, 0.000014, 0.000000, 89.999954, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19563, 1977.769165, -2472.038330, -14.563144, 0.000022, 0.000000, 89.999931, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19563, 1977.599121, -2472.038330, -14.563144, 0.000022, 0.000000, 89.999931, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19564, 1977.763916, -2471.817626, -14.523144, 0.000000, 0.000000, 90.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19564, 1977.524047, -2471.817626, -14.523144, 0.000000, 0.000000, 90.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19564, 1977.763916, -2471.627441, -14.523144, 0.000007, 0.000000, 89.999977, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19564, 1977.524047, -2471.627441, -14.523144, 0.000007, 0.000000, 89.999977, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19564, 1977.763916, -2471.447265, -14.523144, 0.000014, 0.000000, 89.999954, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19564, 1977.524047, -2471.447265, -14.523144, 0.000014, 0.000000, 89.999954, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19566, 1977.793945, -2473.893798, -14.953145, 0.000000, 0.000000, 90.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19566, 1977.613769, -2473.893798, -14.953145, 0.000000, 0.000000, 90.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19566, 1977.793945, -2473.393554, -14.953145, 0.000007, 0.000000, 89.999977, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19566, 1977.613769, -2473.393554, -14.953145, 0.000007, 0.000000, 89.999977, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19566, 1977.793945, -2472.863037, -14.953145, 0.000014, 0.000000, 89.999954, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19566, 1977.613769, -2472.863037, -14.953145, 0.000014, 0.000000, 89.999954, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19565, 1977.792480, -2471.626953, -14.953145, 0.000000, 0.000000, 90.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19565, 1977.602416, -2471.626953, -14.953145, 0.000000, 0.000000, 90.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19565, 1977.792480, -2472.267578, -14.953145, 0.000007, 0.000000, 89.999977, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19565, 1977.602416, -2472.267578, -14.953145, 0.000007, 0.000000, 89.999977, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19561, 1977.760253, -2471.942871, -15.373145, 0.000007, 0.000000, 89.999977, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19561, 1977.760253, -2472.883544, -15.373145, 0.000029, 0.000000, 89.999908, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19562, 1977.812377, -2473.203857, -15.493144, 0.000000, 0.000000, 90.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19562, 1977.602172, -2473.203857, -15.493144, 0.000000, 0.000000, 90.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19562, 1977.812377, -2473.524169, -15.493144, 0.000007, 0.000000, 89.999977, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19562, 1977.602172, -2473.524169, -15.493144, 0.000007, 0.000000, 89.999977, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19562, 1977.812377, -2473.924560, -15.493144, 0.000014, 0.000000, 89.999954, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19562, 1977.602172, -2473.924560, -15.493144, 0.000014, 0.000000, 89.999954, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19572, 1977.512939, -2471.645507, -15.853148, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19572, 1977.512939, -2471.965820, -15.853148, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19572, 1977.512939, -2472.285888, -15.853148, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19572, 1977.512939, -2472.605957, -15.853148, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19572, 1977.512939, -2472.936035, -15.853148, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19572, 1977.512939, -2473.256347, -15.853148, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19572, 1977.512939, -2473.576660, -15.853148, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19572, 1977.512939, -2469.155517, -15.853148, 0.000000, 0.000007, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19570, 1977.808837, -2469.248779, -14.533145, 0.000000, 0.000007, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19570, 1977.548583, -2469.248779, -14.533145, 0.000000, 0.000007, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19570, 1977.808837, -2469.108642, -14.533145, 0.000000, 0.000014, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19570, 1977.548583, -2469.108642, -14.533145, 0.000000, 0.000014, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19570, 1977.808837, -2468.948486, -14.533145, 0.000000, 0.000022, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19570, 1977.548583, -2468.948486, -14.533145, 0.000000, 0.000022, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19570, 1977.808837, -2468.788330, -14.533145, 0.000000, 0.000029, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19570, 1977.548583, -2473.559814, -14.533145, 0.000000, 0.000022, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19569, 1977.796020, -2468.564697, -14.523142, 0.000000, 0.000007, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19569, 1977.585937, -2468.564697, -14.523142, 0.000000, 0.000007, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19569, 1977.796020, -2468.424560, -14.523142, 0.000000, 0.000014, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19569, 1977.585937, -2468.424560, -14.523142, 0.000000, 0.000014, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19569, 1977.796020, -2468.264648, -14.523142, 0.000000, 0.000022, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19569, 1977.585937, -2468.264648, -14.523142, 0.000000, 0.000022, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19569, 1977.796020, -2468.124511, -14.523142, 0.000000, 0.000029, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19569, 1977.585937, -2468.124511, -14.523142, 0.000000, 0.000029, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19563, 1977.769165, -2467.867431, -14.563144, 0.000007, 0.000000, 89.999977, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19563, 1977.599121, -2467.867431, -14.563144, 0.000007, 0.000000, 89.999977, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19563, 1977.769165, -2467.667236, -14.563144, 0.000014, 0.000000, 89.999954, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19563, 1977.599121, -2467.667236, -14.563144, 0.000014, 0.000000, 89.999954, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19563, 1977.769165, -2467.477050, -14.563144, 0.000022, 0.000000, 89.999931, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19563, 1977.599121, -2467.477050, -14.563144, 0.000022, 0.000000, 89.999931, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19563, 1977.769165, -2467.266845, -14.563144, 0.000029, 0.000000, 89.999908, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19563, 1977.599121, -2467.266845, -14.563144, 0.000029, 0.000000, 89.999908, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19564, 1977.763916, -2467.046142, -14.523144, 0.000007, 0.000000, 89.999977, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19564, 1977.524047, -2467.046142, -14.523144, 0.000007, 0.000000, 89.999977, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19564, 1977.763916, -2466.855957, -14.523144, 0.000014, 0.000000, 89.999954, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19564, 1977.524047, -2466.855957, -14.523144, 0.000014, 0.000000, 89.999954, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19564, 1977.763916, -2466.675781, -14.523144, 0.000022, 0.000000, 89.999931, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19564, 1977.524047, -2466.675781, -14.523144, 0.000022, 0.000000, 89.999931, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19566, 1977.613769, -2469.122314, -14.953145, 0.000007, 0.000000, 89.999977, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19565, 1977.602416, -2466.855468, -14.953145, 0.000007, 0.000000, 89.999977, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19561, 1977.600097, -2466.811279, -15.373145, 0.000007, 0.000000, 89.999977, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19561, 1977.760253, -2467.491455, -15.373145, 0.000022, 0.000000, 89.999931, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19561, 1977.600097, -2467.491455, -15.373145, 0.000022, 0.000000, 89.999931, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19561, 1977.760253, -2468.112060, -15.373145, 0.000037, 0.000000, 89.999885, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19561, 1977.600097, -2468.112060, -15.373145, 0.000037, 0.000000, 89.999885, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19562, 1977.812377, -2468.432373, -15.493144, 0.000007, 0.000000, 89.999977, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19562, 1977.602172, -2468.432373, -15.493144, 0.000007, 0.000000, 89.999977, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19562, 1977.812377, -2468.752685, -15.493144, 0.000014, 0.000000, 89.999954, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19562, 1977.602172, -2468.752685, -15.493144, 0.000014, 0.000000, 89.999954, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19562, 1977.812377, -2469.153076, -15.493144, 0.000022, 0.000000, 89.999931, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19562, 1977.602172, -2469.153076, -15.493144, 0.000022, 0.000000, 89.999931, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19572, 1977.512939, -2466.874023, -15.853148, 0.000000, 0.000007, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19572, 1977.512939, -2467.194335, -15.853148, 0.000000, 0.000007, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19572, 1977.512939, -2467.514404, -15.853148, 0.000000, 0.000007, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19572, 1977.512939, -2467.834472, -15.853148, 0.000000, 0.000007, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19572, 1977.512939, -2468.164550, -15.853148, 0.000000, 0.000007, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19572, 1977.512939, -2468.484863, -15.853148, 0.000000, 0.000007, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19572, 1977.512939, -2468.805175, -15.853148, 0.000000, 0.000007, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19572, 1977.512939, -2473.927001, -15.853148, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1594, 1953.663818, -2489.714843, -22.253103, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1594, 1959.433959, -2489.714843, -22.253103, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1594, 1963.163940, -2484.895263, -22.253103, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1594, 1959.523559, -2481.414062, -22.253103, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1594, 1956.093383, -2483.983642, -22.253103, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1594, 1952.803955, -2482.023193, -22.253103, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1574, 1983.025634, -2463.375244, -16.143827, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19943, 1946.943237, -2492.072753, -24.103111, 0.000000, 0.000000, 0.000000, 543, 0, -1, 200.00, 200.00);
}