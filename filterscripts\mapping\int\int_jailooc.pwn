CreateJailOOCInt()
{
    new STREAMER_TAG_OBJECT:ocstx;
    ocstx = CreateDynamicObject(18981, -726.109558, -2415.331787, -62.742244, 0.000000, 90.000000, 0.000000, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocstx, 0, 19517, "noncolored", "gen_white", 0x00000000);
    ocstx = CreateDynamicObject(18981, -726.109558, -2427.345214, -62.742244, 0.000000, 180.000000, 90.000000, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocstx, 0, 14391, "dr_gsmix", "white_128", 0x00000000);
    ocstx = CreateDynamicObject(18981, -726.109558, -2403.424804, -62.742244, 0.000000, 180.000000, 90.000000, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocstx, 0, 14391, "dr_gsmix", "white_128", 0x00000000);
    ocstx = CreateDynamicObject(18981, -714.079162, -2415.094970, -62.742244, 0.000000, 180.000000, 180.000000, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocstx, 0, 14391, "dr_gsmix", "white_128", 0x00000000);
    ocstx = CreateDynamicObject(18981, -738.839599, -2415.094970, -62.742244, 0.000000, 180.000000, 180.000000, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocstx, 0, 14391, "dr_gsmix", "white_128", 0x00000000);
    ocstx = CreateDynamicObject(18981, -726.109558, -2420.945556, -62.742244, 0.000000, 180.000000, 90.000000, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocstx, 0, 4829, "airport_las", "fencekb_64h", 0x00000000);
    ocstx = CreateDynamicObject(18981, -726.109558, -2409.614013, -62.742244, 0.000000, 180.000000, 90.000000, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocstx, 0, 4829, "airport_las", "fencekb_64h", 0x00000000);
    ocstx = CreateDynamicObject(18981, -722.539550, -2433.936279, -62.742244, 0.000000, 180.000000, 180.000000, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocstx, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    ocstx = CreateDynamicObject(18981, -730.858825, -2433.936279, -62.742244, 0.000000, 180.000000, 180.000000, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocstx, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    ocstx = CreateDynamicObject(18981, -722.799194, -2396.655517, -62.742244, 0.000000, 180.000000, 180.000000, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocstx, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    ocstx = CreateDynamicObject(18981, -730.578552, -2396.635009, -62.742244, 0.000000, 180.000000, 180.000000, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocstx, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    
    ocstx = CreateDynamicObject(2789, -722.243225, -2406.382568, -58.989402, 0.000000, 90.000000, 270.000000, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocstx, 0, 10988, "mission2_sfse", "ws_fancywallpink", 0x00000000);
    SetDynamicObjectMaterial(ocstx, 1, 10988, "mission2_sfse", "ws_fancywallpink", 0x00000000);
    ocstx = CreateDynamicObject(18661, -722.087402, -2406.355224, -58.748718, 0.000000, 0.000000, 180.000000, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocstx, 0, "ATHERLIFE\nROLEPLAY", 100, "Arial", 36, 1, 0xFF000000, 0x00000000, 1);
    ocstx = CreateDynamicObject(18661, -722.107421, -2406.825195, -59.498683, 0.000000, 0.000000, 180.000000, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocstx, 0, "Mohon untuk tidak mengulangi", 130, "Arial", 30, 1, 0xFF000000, 0x00000000, 1);
    ocstx = CreateDynamicObject(18661, -722.107421, -2406.785156, -59.778675, 0.000000, 0.000000, 180.000000, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocstx, 0, "Jangan gunakan cheat", 130, "Arial", 30, 1, 0xFF000000, 0x00000000, 1);
    ocstx = CreateDynamicObject(18661, -722.107421, -2406.985595, -60.038669, 0.000000, 0.000000, 180.000000, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocstx, 0, "Jadilah orang yang cerdas", 130, "Arial", 30, 1, 0xFF000000, 0x00000000, 1);
    ocstx = CreateDynamicObject(18661, -722.107421, -2406.254150, -60.498664, 0.000000, 0.000000, 180.000000, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocstx, 0, "Baca panduan warga di discord", 130, "Arial", 30, 1, 0xFFFFFF00, 0x00000000, 1);
    ocstx = CreateDynamicObject(18661, -722.107421, -2407.525390, -59.308628, 0.000000, 0.000000, 180.000000, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocstx, 0, "WARNING:", 130, "Arial", 30, 1, 0xFFFF0000, 0x00000000, 1);
    ocstx = CreateDynamicObject(18661, -722.107421, -2406.285156, -60.818622, 0.000000, 0.000000, 180.000000, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocstx, 0, "Rules & Guides", 130, "Arial", 30, 1, 0xFFFFFFFF, 0x00000000, 1);
    ocstx = CreateDynamicObject(18661, -722.107421, -2406.244140, -61.098609, 0.000000, 0.000000, 180.000000, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocstx, 0, "discord.gg/ATHERLIFEtheater", 130, "Arial", 30, 1, 0xFFFFFFFF, 0x00000000, 1);
    ocstx = CreateDynamicObject(2734, -722.098083, -2406.883789, -60.852226, 0.000000, 0.000000, 90.000000, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    ocstx = CreateDynamicObject(2734, -722.098083, -2405.654052, -60.852226, 0.000000, 0.000000, 90.000000, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    ocstx = CreateDynamicObject(18981, -714.399291, -2396.655517, -62.742244, 0.000000, 180.000000, 180.000000, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocstx, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    ocstx = CreateDynamicObject(18981, -738.629943, -2396.655517, -62.742244, 0.000000, 180.000000, 180.000000, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocstx, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    
    ocstx = CreateDynamicObject(2789, -730.002807, -2406.382568, -58.989402, -0.000022, 90.000000, -89.999931, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocstx, 0, 10988, "mission2_sfse", "ws_fancywallpink", 0x00000000);
    SetDynamicObjectMaterial(ocstx, 1, 10988, "mission2_sfse", "ws_fancywallpink", 0x00000000);
    ocstx = CreateDynamicObject(18661, -729.846984, -2406.355224, -58.748718, 0.000000, -0.000022, 179.999862, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocstx, 0, "ATHERLIFE\nRoleplay", 100, "Arial", 36, 1, 0xFF000000, 0x00000000, 1);
    ocstx = CreateDynamicObject(18661, -729.867004, -2406.825195, -59.498683, 0.000000, -0.000022, 179.999862, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocstx, 0, "Mohon untuk tidak mengulangi", 130, "Arial", 30, 1, 0xFF000000, 0x00000000, 1);
    ocstx = CreateDynamicObject(18661, -729.867004, -2406.785156, -59.778675, 0.000000, -0.000022, 179.999862, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocstx, 0, "Jangan gunakan cheat", 130, "Arial", 30, 1, 0xFF000000, 0x00000000, 1);
    ocstx = CreateDynamicObject(18661, -729.867004, -2406.985595, -60.038669, 0.000000, -0.000022, 179.999862, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocstx, 0, "Jadilah orang yang cerdas", 130, "Arial", 30, 1, 0xFF000000, 0x00000000, 1);
    ocstx = CreateDynamicObject(18661, -729.867004, -2406.254150, -60.498664, 0.000000, -0.000022, 179.999862, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocstx, 0, "Baca panduan warga di discord", 130, "Arial", 30, 1, 0xFFFFFF00, 0x00000000, 1);
    ocstx = CreateDynamicObject(18661, -729.867004, -2407.525390, -59.308628, 0.000000, -0.000022, 179.999862, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocstx, 0, "WARNING:", 130, "Arial", 30, 1, 0xFFFF0000, 0x00000000, 1);
    ocstx = CreateDynamicObject(18661, -729.867004, -2406.285156, -60.818622, 0.000000, -0.000022, 179.999862, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocstx, 0, "Rules & Guides", 130, "Arial", 30, 1, 0xFFFFFFFF, 0x00000000, 1);
    ocstx = CreateDynamicObject(18661, -729.867004, -2406.244140, -61.098609, 0.000000, -0.000022, 179.999862, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocstx, 0, "discord.gg/ATHERLIFEtheater", 130, "Arial", 30, 1, 0xFFFFFFFF, 0x00000000, 1);
    ocstx = CreateDynamicObject(2734, -729.857666, -2406.883789, -60.852226, 0.000022, 0.000000, 89.999931, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    ocstx = CreateDynamicObject(2734, -729.857666, -2405.654052, -60.852226, 0.000022, 0.000000, 89.999931, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    ocstx = CreateDynamicObject(2789, -738.064392, -2406.382568, -58.989402, -0.000015, 90.000000, -89.999954, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocstx, 0, 10988, "mission2_sfse", "ws_fancywallpink", 0x00000000);
    SetDynamicObjectMaterial(ocstx, 1, 10988, "mission2_sfse", "ws_fancywallpink", 0x00000000);
    ocstx = CreateDynamicObject(18661, -737.908569, -2406.355224, -58.748718, 0.000000, -0.000015, 179.999908, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocstx, 0, "ATHERLIFE\nRoleplay", 100, "Arial", 36, 1, 0xFF000000, 0x00000000, 1);
    ocstx = CreateDynamicObject(18661, -737.928588, -2406.825195, -59.498683, 0.000000, -0.000015, 179.999908, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocstx, 0, "Mohon untuk tidak mengulangi", 130, "Arial", 30, 1, 0xFF000000, 0x00000000, 1);
    ocstx = CreateDynamicObject(18661, -737.928588, -2406.785156, -59.778675, 0.000000, -0.000015, 179.999908, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocstx, 0, "Jangan gunakan cheat", 130, "Arial", 30, 1, 0xFF000000, 0x00000000, 1);
    ocstx = CreateDynamicObject(18661, -737.928588, -2406.985595, -60.038669, 0.000000, -0.000015, 179.999908, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocstx, 0, "Jadilah orang yang cerdas", 130, "Arial", 30, 1, 0xFF000000, 0x00000000, 1);
    ocstx = CreateDynamicObject(18661, -737.928588, -2406.254150, -60.498664, 0.000000, -0.000015, 179.999908, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocstx, 0, "Baca panduan warga di discord", 130, "Arial", 30, 1, 0xFFFFFF00, 0x00000000, 1);
    ocstx = CreateDynamicObject(18661, -737.928588, -2407.525390, -59.308628, 0.000000, -0.000015, 179.999908, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocstx, 0, "WARNING:", 130, "Arial", 30, 1, 0xFFFF0000, 0x00000000, 1);
    ocstx = CreateDynamicObject(18661, -737.928588, -2406.285156, -60.818622, 0.000000, -0.000015, 179.999908, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocstx, 0, "Rules & Guides", 130, "Arial", 30, 1, 0xFFFFFFFF, 0x00000000, 1);
    ocstx = CreateDynamicObject(18661, -737.928588, -2406.244140, -61.098609, 0.000000, -0.000015, 179.999908, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocstx, 0, "discord.gg/ATHERLIFEtheater", 130, "Arial", 30, 1, 0xFFFFFFFF, 0x00000000, 1);
    ocstx = CreateDynamicObject(2734, -737.919250, -2406.883789, -60.852226, 0.000015, 0.000000, 89.999954, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    ocstx = CreateDynamicObject(2734, -737.919250, -2405.654052, -60.852226, 0.000015, 0.000000, 89.999954, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    
    ocstx = CreateDynamicObject(2789, -730.293090, -2424.275634, -58.989402, -0.000038, 90.000000, -89.999885, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocstx, 0, 10988, "mission2_sfse", "ws_fancywallpink", 0x00000000);
    SetDynamicObjectMaterial(ocstx, 1, 10988, "mission2_sfse", "ws_fancywallpink", 0x00000000);
    ocstx = CreateDynamicObject(18661, -730.137268, -2424.248291, -58.748718, 0.000000, -0.000038, 179.999771, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocstx, 0, "ATHERLIFE\nRoleplay", 100, "Arial", 36, 1, 0xFF000000, 0x00000000, 1);
    ocstx = CreateDynamicObject(18661, -730.157287, -2424.718261, -59.498683, 0.000000, -0.000038, 179.999771, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocstx, 0, "Mohon untuk tidak mengulangi", 130, "Arial", 30, 1, 0xFF000000, 0x00000000, 1);
    ocstx = CreateDynamicObject(18661, -730.157287, -2424.678222, -59.778675, 0.000000, -0.000038, 179.999771, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocstx, 0, "Jangan gunakan cheat", 130, "Arial", 30, 1, 0xFF000000, 0x00000000, 1);
    ocstx = CreateDynamicObject(18661, -730.157287, -2424.878662, -60.038669, 0.000000, -0.000038, 179.999771, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocstx, 0, "Jadilah orang yang cerdas", 130, "Arial", 30, 1, 0xFF000000, 0x00000000, 1);
    ocstx = CreateDynamicObject(18661, -730.157287, -2424.147216, -60.498664, 0.000000, -0.000038, 179.999771, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocstx, 0, "Baca panduan warga di discord", 130, "Arial", 30, 1, 0xFFFFFF00, 0x00000000, 1);
    ocstx = CreateDynamicObject(18661, -730.157287, -2425.418457, -59.308628, 0.000000, -0.000038, 179.999771, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocstx, 0, "WARNING:", 130, "Arial", 30, 1, 0xFFFF0000, 0x00000000, 1);
    ocstx = CreateDynamicObject(18661, -730.157287, -2424.178222, -60.818622, 0.000000, -0.000038, 179.999771, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocstx, 0, "Rules & Guides", 130, "Arial", 30, 1, 0xFFFFFFFF, 0x00000000, 1);
    ocstx = CreateDynamicObject(18661, -730.157287, -2424.137207, -61.098609, 0.000000, -0.000038, 179.999771, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocstx, 0, "discord.gg/ATHERLIFEtheater", 130, "Arial", 30, 1, 0xFFFFFFFF, 0x00000000, 1);
    ocstx = CreateDynamicObject(2734, -730.147949, -2424.776855, -60.852226, 0.000038, 0.000000, 89.999885, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    ocstx = CreateDynamicObject(2734, -730.147949, -2423.547119, -60.852226, 0.000038, 0.000000, 89.999885, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    
    ocstx = CreateDynamicObject(2789, -721.942687, -2424.275634, -58.989402, -0.000045, 90.000000, -89.999862, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocstx, 0, 10988, "mission2_sfse", "ws_fancywallpink", 0x00000000);
    SetDynamicObjectMaterial(ocstx, 1, 10988, "mission2_sfse", "ws_fancywallpink", 0x00000000);
    ocstx = CreateDynamicObject(18661, -721.786865, -2424.248291, -58.748718, 0.000000, -0.000045, 179.999725, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocstx, 0, "ATHERLIFE\nRoleplay", 100, "Arial", 36, 1, 0xFF000000, 0x00000000, 1);
    ocstx = CreateDynamicObject(18661, -721.806884, -2424.718261, -59.498683, 0.000000, -0.000045, 179.999725, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocstx, 0, "Mohon untuk tidak mengulangi", 130, "Arial", 30, 1, 0xFF000000, 0x00000000, 1);
    ocstx = CreateDynamicObject(18661, -721.806884, -2424.678222, -59.778675, 0.000000, -0.000045, 179.999725, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocstx, 0, "Jangan gunakan cheat", 130, "Arial", 30, 1, 0xFF000000, 0x00000000, 1);
    ocstx = CreateDynamicObject(18661, -721.806884, -2424.878662, -60.038669, 0.000000, -0.000045, 179.999725, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocstx, 0, "Jadilah orang yang cerdas", 130, "Arial", 30, 1, 0xFF000000, 0x00000000, 1);
    ocstx = CreateDynamicObject(18661, -721.806884, -2424.147216, -60.498664, 0.000000, -0.000045, 179.999725, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocstx, 0, "Baca panduan warga di discord", 130, "Arial", 30, 1, 0xFFFFFF00, 0x00000000, 1);
    ocstx = CreateDynamicObject(18661, -721.806884, -2425.418457, -59.308628, 0.000000, -0.000045, 179.999725, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocstx, 0, "WARNING:", 130, "Arial", 30, 1, 0xFFFF0000, 0x00000000, 1);
    ocstx = CreateDynamicObject(18661, -721.806884, -2424.178222, -60.818622, 0.000000, -0.000045, 179.999725, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocstx, 0, "Rules & Guides", 130, "Arial", 30, 1, 0xFFFFFFFF, 0x00000000, 1);
    ocstx = CreateDynamicObject(18661, -721.806884, -2424.137207, -61.098609, 0.000000, -0.000045, 179.999725, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocstx, 0, "discord.gg/ATHERLIFEtheater", 130, "Arial", 30, 1, 0xFFFFFFFF, 0x00000000, 1);
    ocstx = CreateDynamicObject(2734, -721.797546, -2424.776855, -60.852226, 0.000045, 0.000000, 89.999862, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    ocstx = CreateDynamicObject(2734, -721.797546, -2423.547119, -60.852226, 0.000045, 0.000000, 89.999862, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    ocstx = CreateDynamicObject(18981, -738.769470, -2433.936279, -62.742244, 0.000000, 180.000000, 180.000000, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocstx, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    ocstx = CreateDynamicObject(18981, -714.298828, -2433.936279, -62.742244, 0.000000, 180.000000, 180.000000, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocstx, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    ocstx = CreateDynamicObject(2789, -738.233947, -2424.275634, -58.989402, -0.000061, 90.000000, -89.999816, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocstx, 0, 10988, "mission2_sfse", "ws_fancywallpink", 0x00000000);
    SetDynamicObjectMaterial(ocstx, 1, 10988, "mission2_sfse", "ws_fancywallpink", 0x00000000);
    ocstx = CreateDynamicObject(18661, -738.078125, -2424.248291, -58.748718, 0.000000, -0.000061, 179.999633, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocstx, 0, "ATHERLIFE\nRoleplay", 100, "Arial", 36, 1, 0xFF000000, 0x00000000, 1);
    ocstx = CreateDynamicObject(18661, -738.098144, -2424.718261, -59.498683, 0.000000, -0.000061, 179.999633, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocstx, 0, "Mohon untuk tidak mengulangi", 130, "Arial", 30, 1, 0xFF000000, 0x00000000, 1);
    ocstx = CreateDynamicObject(18661, -738.098144, -2424.678222, -59.778675, 0.000000, -0.000061, 179.999633, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocstx, 0, "Jangan gunakan cheat", 130, "Arial", 30, 1, 0xFF000000, 0x00000000, 1);
    ocstx = CreateDynamicObject(18661, -738.098144, -2424.878662, -60.038669, 0.000000, -0.000061, 179.999633, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocstx, 0, "Jadilah orang yang cerdas", 130, "Arial", 30, 1, 0xFF000000, 0x00000000, 1);
    ocstx = CreateDynamicObject(18661, -738.098144, -2424.147216, -60.498664, 0.000000, -0.000061, 179.999633, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocstx, 0, "Baca panduan warga di discord", 130, "Arial", 30, 1, 0xFFFFFF00, 0x00000000, 1);
    ocstx = CreateDynamicObject(18661, -738.098144, -2425.418457, -59.308628, 0.000000, -0.000061, 179.999633, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocstx, 0, "WARNING:", 130, "Arial", 30, 1, 0xFFFF0000, 0x00000000, 1);
    ocstx = CreateDynamicObject(18661, -738.098144, -2424.178222, -60.818622, 0.000000, -0.000061, 179.999633, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocstx, 0, "Rules & Guides", 130, "Arial", 30, 1, 0xFFFFFFFF, 0x00000000, 1);
    ocstx = CreateDynamicObject(18661, -738.098144, -2424.137207, -61.098609, 0.000000, -0.000061, 179.999633, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocstx, 0, "discord.gg/ATHERLIFEtheater", 130, "Arial", 30, 1, 0xFFFFFFFF, 0x00000000, 1);
    ocstx = CreateDynamicObject(2734, -738.088806, -2424.776855, -60.852226, 0.000061, 0.000000, 89.999816, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    ocstx = CreateDynamicObject(2734, -738.088806, -2423.547119, -60.852226, 0.000061, 0.000000, 89.999816, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    ocstx = CreateDynamicObject(18981, -726.109558, -2415.331787, -55.002231, 0.000000, 90.000000, 0.000000, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocstx, 0, 19517, "noncolored", "gen_white", 0x00000000);

    ocstx = CreateDynamicObject(2734, -731.487670, -2423.547363, -60.852226, 0.000068, -0.000007, -90.000221, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    ocstx = CreateDynamicObject(2734, -731.487670, -2424.776855, -60.852226, 0.000068, -0.000007, -90.000221, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    
    ocstx = CreateDynamicObject(2734, -723.186950, -2423.547363, -60.852226, 0.000061, -0.000007, -90.000198, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    ocstx = CreateDynamicObject(2734, -723.186950, -2424.776855, -60.852226, 0.000061, -0.000007, -90.000198, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    
    ocstx = CreateDynamicObject(2734, -714.957153, -2423.547363, -60.852226, 0.000053, -0.000007, -90.000175, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    ocstx = CreateDynamicObject(2734, -714.957153, -2424.776855, -60.852226, 0.000053, -0.000007, -90.000175, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    
    ocstx = CreateDynamicObject(2734, -714.957153, -2405.675781, -60.852226, 0.000045, -0.000007, -90.000152, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    ocstx = CreateDynamicObject(2734, -714.957153, -2406.905273, -60.852226, 0.000045, -0.000007, -90.000152, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);

    ocstx = CreateDynamicObject(2734, -723.427001, -2405.675781, -60.852226, 0.000038, -0.000007, -90.000129, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    ocstx = CreateDynamicObject(2734, -723.427001, -2406.905273, -60.852226, 0.000038, -0.000007, -90.000129, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    
    ocstx = CreateDynamicObject(2734, -731.237976, -2405.675781, -60.852226, 0.000030, -0.000007, -90.000106, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    ocstx = CreateDynamicObject(2734, -731.237976, -2406.905273, -60.852226, 0.000030, -0.000007, -90.000106, 5, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocstx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
}