
//jobcenter v1
/*
CreateJobCenterInt()
{
    static jobcentertxt;
    jobcentertxt = CreateDynamicObject(18981, 2893.607177, 1740.773193, -41.179687, 0.000000, 90.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jobcentertxt, 0, 15034, "genhotelsave", "walp57S", 0x00000000);
    jobcentertxt = CreateDynamicObject(19353, 2892.665283, 1728.218505, -38.929691, 0.000000, 0.000000, 90.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jobcentertxt, 0, 9583, "bigshap_sfw", "bridge_walls3_sfw", 0x00000000);
    jobcentertxt = CreateDynamicObject(19353, 2890.975341, 1729.018310, -38.929691, 0.000000, 0.000000, 180.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jobcentertxt, 0, 9583, "bigshap_sfw", "bridge_walls3_sfw", 0x00000000);
    jobcentertxt = CreateDynamicObject(19353, 2894.355224, 1729.018310, -38.929695, 0.000000, 0.000000, 180.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jobcentertxt, 0, 9583, "bigshap_sfw", "bridge_walls3_sfw", 0x00000000);
    jobcentertxt = CreateDynamicObject(19426, 2894.655029, 1730.618286, -38.929691, 0.000000, 0.000000, 270.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jobcentertxt, 0, 9583, "bigshap_sfw", "bridge_walls3_sfw", 0x00000000);
    jobcentertxt = CreateDynamicObject(19426, 2890.655029, 1730.618286, -38.929691, 0.000000, 0.000000, 270.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jobcentertxt, 0, 9583, "bigshap_sfw", "bridge_walls3_sfw", 0x00000000);
    jobcentertxt = CreateDynamicObject(19383, 2893.924560, 1732.310913, -38.939998, 0.000000, 0.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jobcentertxt, 0, 9583, "bigshap_sfw", "bridge_walls3_sfw", 0x00000000);
    jobcentertxt = CreateDynamicObject(19383, 2891.384521, 1732.310913, -38.940002, 0.000000, 0.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jobcentertxt, 0, 9583, "bigshap_sfw", "bridge_walls3_sfw", 0x00000000);
    jobcentertxt = CreateDynamicObject(1536, 2891.349853, 1731.571777, -40.689674, 0.000000, 0.000000, 90.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jobcentertxt, 0, 14577, "casinovault01", "dts_elevator_door", 0x00000000);
    jobcentertxt = CreateDynamicObject(1536, 2893.949951, 1733.081787, -40.699676, 0.000000, 0.000000, 270.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jobcentertxt, 0, 14577, "casinovault01", "dts_elevator_door", 0x00000000);
    jobcentertxt = CreateDynamicObject(19383, 2893.924560, 1735.520751, -38.939998, 0.000000, 0.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jobcentertxt, 0, 9583, "bigshap_sfw", "bridge_walls3_sfw", 0x00000000);
    jobcentertxt = CreateDynamicObject(1536, 2893.949951, 1736.291625, -40.699676, 0.000000, 0.000000, 270.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jobcentertxt, 0, 14577, "casinovault01", "dts_elevator_door", 0x00000000);
    jobcentertxt = CreateDynamicObject(19383, 2891.384521, 1735.520751, -38.940002, 0.000000, 0.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jobcentertxt, 0, 9583, "bigshap_sfw", "bridge_walls3_sfw", 0x00000000);
    jobcentertxt = CreateDynamicObject(19353, 2889.865234, 1737.208496, -38.929691, 0.000000, 0.000000, 90.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jobcentertxt, 0, 9583, "bigshap_sfw", "bridge_walls3_sfw", 0x00000000);
    jobcentertxt = CreateDynamicObject(2395, 2891.292968, 1733.904174, -40.579689, 270.000000, 0.000000, 90.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jobcentertxt, 0, 18757, "vcinteriors", "dts_elevator_ceiling", 0x00000000);
    jobcentertxt = CreateDynamicObject(19353, 2889.865234, 1733.908569, -38.929691, 0.000000, 0.000000, 90.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jobcentertxt, 0, 9583, "bigshap_sfw", "bridge_walls3_sfw", 0x00000000);
    jobcentertxt = CreateDynamicObject(19353, 2888.665283, 1735.548583, -38.929691, 0.000000, 0.000000, 180.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jobcentertxt, 0, 9583, "bigshap_sfw", "bridge_walls3_sfw", 0x00000000);
    jobcentertxt = CreateDynamicObject(19353, 2895.457275, 1737.208496, -38.929691, 0.000000, 0.000000, 90.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jobcentertxt, 0, 9583, "bigshap_sfw", "bridge_walls3_sfw", 0x00000000);
    jobcentertxt = CreateDynamicObject(19353, 2897.117919, 1738.798095, -38.929691, 0.000000, 0.000000, 360.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jobcentertxt, 0, 9583, "bigshap_sfw", "bridge_walls3_sfw", 0x00000000);
    jobcentertxt = CreateDynamicObject(19353, 2888.167968, 1738.798095, -38.929691, 0.000000, 0.000000, 360.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jobcentertxt, 0, 9583, "bigshap_sfw", "bridge_walls3_sfw", 0x00000000);
    jobcentertxt = CreateDynamicObject(19353, 2889.865234, 1740.318359, -38.929691, 0.000000, 0.000000, 90.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jobcentertxt, 0, 9583, "bigshap_sfw", "bridge_walls3_sfw", 0x00000000);
    jobcentertxt = CreateDynamicObject(19353, 2895.457275, 1740.318603, -38.929691, 0.000000, 0.000000, 90.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jobcentertxt, 0, 9583, "bigshap_sfw", "bridge_walls3_sfw", 0x00000000);
    jobcentertxt = CreateDynamicObject(19353, 2890.985107, 1742.018188, -38.929691, 0.000000, 0.000000, 180.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jobcentertxt, 0, 7488, "vegasdwntwn1", "villainnwall02_128", 0x00000000);
    jobcentertxt = CreateDynamicObject(19353, 2894.375000, 1742.018188, -38.929691, 0.000000, 0.000000, 180.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jobcentertxt, 0, 7488, "vegasdwntwn1", "villainnwall02_128", 0x00000000);
    jobcentertxt = CreateDynamicObject(19383, 2892.686767, 1740.310791, -38.930004, 0.000000, 0.000000, 90.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jobcentertxt, 0, 9583, "bigshap_sfw", "bridge_walls3_sfw", 0x00000000);
    jobcentertxt = CreateDynamicObject(19383, 2892.686767, 1740.490722, -38.940002, 0.000000, 0.000000, 90.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jobcentertxt, 0, 7488, "vegasdwntwn1", "villainnwall02_128", 0x00000000);
    jobcentertxt = CreateDynamicObject(2180, 2892.193847, 1746.916381, -40.679687, 0.000000, 0.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jobcentertxt, 0, 9259, "presidio01_sfn", "sl_whitewood01", 0x00000000);
    jobcentertxt = CreateDynamicObject(19353, 2892.094970, 1748.538208, -38.929691, 0.000000, 0.000000, 270.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jobcentertxt, 0, 7488, "vegasdwntwn1", "villainnwall02_128", 0x00000000);
    jobcentertxt = CreateDynamicObject(19445, 2887.795166, 1748.438232, -38.929691, 0.000000, 0.000000, 360.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jobcentertxt, 0, 7488, "vegasdwntwn1", "villainnwall02_128", 0x00000000);
    jobcentertxt = CreateDynamicObject(19353, 2896.064941, 1743.538208, -38.929691, 0.000000, 0.000000, 270.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jobcentertxt, 0, 7488, "vegasdwntwn1", "villainnwall02_128", 0x00000000);
    jobcentertxt = CreateDynamicObject(19353, 2889.305175, 1743.538208, -38.929691, 0.000000, 0.000000, 270.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jobcentertxt, 0, 7488, "vegasdwntwn1", "villainnwall02_128", 0x00000000);
    jobcentertxt = CreateDynamicObject(19353, 2893.305175, 1748.558227, -38.929691, 0.000000, 0.000000, 270.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jobcentertxt, 0, 7488, "vegasdwntwn1", "villainnwall02_128", 0x00000000);
    jobcentertxt = CreateDynamicObject(19445, 2897.584960, 1748.438232, -38.929691, 0.000000, 0.000000, 360.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jobcentertxt, 0, 7488, "vegasdwntwn1", "villainnwall02_128", 0x00000000);
    jobcentertxt = CreateDynamicObject(19445, 2892.685058, 1753.338256, -38.929691, 0.000000, 0.000000, 450.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jobcentertxt, 0, 7488, "vegasdwntwn1", "villainnwall02_128", 0x00000000);
    jobcentertxt = CreateDynamicObject(19893, 2892.087890, 1746.855102, -39.878032, 0.000000, 0.000000, 160.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jobcentertxt, 1, 14776, "genintintcarint3", "auto_tune3", 0x00000000);
    jobcentertxt = CreateDynamicObject(1499, 2891.914794, 1740.364746, -40.669689, 0.000000, 0.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jobcentertxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(jobcentertxt, 1, 10806, "airfence_sfse", "ws_leccyfncetop", 0x00000000);
    jobcentertxt = CreateDynamicObject(18981, 2893.607177, 1740.773193, -36.689685, 0.000000, 90.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jobcentertxt, 0, 15034, "genhotelsave", "walp57S", 0x00000000);
    jobcentertxt = CreateDynamicObject(1498, 2887.852294, 1744.863403, -40.679687, 0.000000, 0.000000, 90.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jobcentertxt, 0, 1569, "adam_v_doort", "ws_guardhousedoor", 0xFFFF0000);
    jobcentertxt = CreateDynamicObject(11684, 2890.943359, 1752.621704, -40.679687, 0.000000, 0.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jobcentertxt, 0, 14407, "carter_block", "zebra_skin", 0x00000000);
    jobcentertxt = CreateDynamicObject(11685, 2891.993408, 1752.621704, -40.679687, 0.000000, 0.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jobcentertxt, 0, 14407, "carter_block", "zebra_skin", 0x00000000);
    jobcentertxt = CreateDynamicObject(11685, 2893.253662, 1752.621704, -40.679687, 0.000000, 0.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jobcentertxt, 0, 14407, "carter_block", "zebra_skin", 0x00000000);
    jobcentertxt = CreateDynamicObject(19353, 2892.094970, 1748.698120, -38.929691, 0.000000, 0.000000, 270.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jobcentertxt, 0, 7488, "vegasdwntwn1", "villainnwall02_128", 0x00000000);
    jobcentertxt = CreateDynamicObject(19353, 2893.315185, 1748.688110, -38.929691, 0.000000, 0.000000, 270.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jobcentertxt, 0, 7488, "vegasdwntwn1", "villainnwall02_128", 0x00000000);
    jobcentertxt = CreateDynamicObject(11682, 2894.323730, 1752.621704, -40.679687, 0.000000, 0.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jobcentertxt, 0, 14407, "carter_block", "zebra_skin", 0x00000000);
    jobcentertxt = CreateDynamicObject(1649, 2896.539062, 1748.642456, -38.679687, 0.000000, 90.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jobcentertxt, 0, 10806, "airfence_sfse", "ws_leccyfncetop", 0x00000000);
    jobcentertxt = CreateDynamicObject(2315, 2891.843994, 1750.911621, -40.679687, 0.000000, 0.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jobcentertxt, 0, 1649, "wglass", "carshowwin2", 0x00000000);
    jobcentertxt = CreateDynamicObject(1649, 2895.889404, 1748.642456, -38.679687, 0.000000, 90.000000, 180.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jobcentertxt, 0, 10806, "airfence_sfse", "ws_leccyfncetop", 0x00000000);
    jobcentertxt = CreateDynamicObject(19089, 2898.837890, 1748.641235, -40.659690, 90.000000, 0.000000, 90.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jobcentertxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    jobcentertxt = CreateDynamicObject(19089, 2897.478027, 1748.641235, -40.659690, 180.000000, 0.000000, 90.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jobcentertxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    jobcentertxt = CreateDynamicObject(19089, 2894.928466, 1748.641235, -40.659690, 180.000000, 0.000000, 90.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jobcentertxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    jobcentertxt = CreateDynamicObject(19089, 2898.837890, 1748.641235, -37.199691, 90.000000, 0.000000, 90.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jobcentertxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    jobcentertxt = CreateDynamicObject(1536, 2891.349853, 1734.781982, -40.699676, 0.000000, 0.000000, 90.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jobcentertxt, 0, 14577, "casinovault01", "dts_elevator_door", 0x00000000);
    jobcentertxt = CreateDynamicObject(2315, 2891.881591, 1728.799804, -40.690773, 0.000000, 0.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jobcentertxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    jobcentertxt = CreateDynamicObject(1498, 2888.302734, 1737.982543, -40.679687, 0.000007, 0.000000, 89.999977, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jobcentertxt, 0, 1569, "adam_v_doort", "ws_guardhousedoor", 0xFFFF0000);
    jobcentertxt = CreateDynamicObject(1498, 2896.997558, 1739.506347, -40.679687, -0.000007, -0.000007, -89.999984, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jobcentertxt, 0, 1569, "adam_v_doort", "ws_guardhousedoor", 0xFFFF0000);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(11705, 2893.299560, 1746.765258, -39.878032, 0.000000, 0.000000, 195.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2251, 2891.149902, 1746.814453, -39.829685, 0.000000, 0.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2152, 2889.726562, 1743.790405, -40.679687, 0.000000, 0.000000, 180.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2267, 2892.694335, 1748.420776, -38.079689, 0.000000, 0.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(14680, 2894.527587, 1733.383056, -38.679687, 0.000000, 0.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(14680, 2894.527587, 1729.383056, -38.679687, 0.000000, 0.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2152, 2888.126464, 1748.790405, -40.679687, 0.000000, 0.000000, 90.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2152, 2888.126464, 1750.090454, -40.689685, 0.000000, 0.000000, 90.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2245, 2889.500732, 1743.963012, -39.326435, 0.000000, 0.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2152, 2888.126464, 1747.490356, -40.679687, 0.000000, 0.000000, 90.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(19810, 2887.913574, 1745.656738, -38.879684, 0.000000, 0.000000, 270.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2266, 2888.353027, 1748.201293, -38.679687, 0.000000, 0.000000, 90.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2266, 2888.353027, 1747.801269, -38.979690, 0.000000, 0.000000, 90.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2266, 2888.353027, 1748.601318, -38.679695, 0.000000, 45.000000, 90.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2266, 2888.353027, 1749.501342, -38.979694, 0.000000, 0.000000, 90.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2266, 2888.353027, 1750.101440, -38.779693, 0.000000, 0.000000, 90.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2853, 2888.265869, 1750.402709, -39.626430, 0.000000, 0.000000, 180.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2257, 2897.460693, 1745.978149, -38.679687, 0.000000, 0.000000, -90.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(1808, 2897.273925, 1750.787597, -40.679687, 0.000000, 0.000000, -90.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(1736, 2892.561035, 1752.935913, -37.979686, 0.000000, 0.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2195, 2896.887695, 1752.616577, -40.079689, 0.000000, 0.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(1828, 2892.923828, 1750.838500, -40.679687, 0.000000, 0.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(1734, 2892.638671, 1745.687133, -36.679687, 0.000000, 0.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(14705, 2893.548339, 1728.586791, -39.957767, 0.000000, 0.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(19810, 2888.354003, 1738.745483, -38.879684, -0.000007, -0.000000, -89.999977, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(19810, 2896.946289, 1738.743164, -38.879684, 0.000007, 0.000007, 89.999916, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(638, 2890.058593, 1739.876464, -40.316940, 0.000000, 0.000000, 270.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(638, 2895.218261, 1739.866455, -40.316940, 0.000000, 0.000000, 270.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2010, 2893.874755, 1740.978027, -40.702056, 0.000000, 0.000000, -6.799998, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2010, 2897.100585, 1748.166137, -40.692050, 0.000000, 0.000000, -29.699996, 99, 3, -1, 200.00, 200.00);
}
*/

CreateJobCenterInt()
{
    new STREAMER_TAG_OBJECT:jbxtxc;
    jbxtxc = CreateDynamicObject(8661, 1384.295532, 1575.578979, 16.000312, 0.000000, 0.000014, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14754, "sfhsb3", "ah_wpaper10", 0x00000000);
    jbxtxc = CreateDynamicObject(19377, 1364.355224, 1570.841674, 19.880329, 0.000000, 0.000029, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(19377, 1364.355224, 1580.461425, 19.880329, 0.000000, 0.000029, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(3494, 1392.882690, 1563.233276, 19.740314, 0.000000, 0.000014, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14623, "mafcasmain", "ab_tileStar", 0xFFBBBBBB);
    jbxtxc = CreateDynamicObject(962, 1367.642211, 1571.723632, 16.790313, 89.999992, -89.999992, -90.000022, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14577, "casinovault01", "dts_elevator_door", 0x00000000);
    SetDynamicObjectMaterial(jbxtxc, 1, 14577, "casinovault01", "vaultWall", 0x00000000);
    SetDynamicObjectMaterial(jbxtxc, 2, 16640, "a51", "a51_panels1", 0x00000000);
    jbxtxc = CreateDynamicObject(962, 1367.642211, 1574.083740, 16.790313, 89.999992, -89.999992, -90.000022, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14577, "casinovault01", "dts_elevator_door", 0x00000000);
    SetDynamicObjectMaterial(jbxtxc, 1, 14577, "casinovault01", "vaultWall", 0x00000000);
    SetDynamicObjectMaterial(jbxtxc, 2, 16640, "a51", "a51_panels1", 0x00000000);
    jbxtxc = CreateDynamicObject(962, 1367.642211, 1576.303833, 16.790313, 89.999992, -89.999992, -90.000022, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14577, "casinovault01", "dts_elevator_door", 0x00000000);
    SetDynamicObjectMaterial(jbxtxc, 1, 14577, "casinovault01", "vaultWall", 0x00000000);
    SetDynamicObjectMaterial(jbxtxc, 2, 16640, "a51", "a51_panels1", 0x00000000);
    jbxtxc = CreateDynamicObject(962, 1367.642211, 1578.853515, 16.790313, 89.999992, -89.999992, -90.000022, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14577, "casinovault01", "dts_elevator_door", 0x00000000);
    SetDynamicObjectMaterial(jbxtxc, 1, 14577, "casinovault01", "vaultWall", 0x00000000);
    SetDynamicObjectMaterial(jbxtxc, 2, 16640, "a51", "a51_panels1", 0x00000000);
    jbxtxc = CreateDynamicObject(970, 1373.258178, 1566.310058, 16.750324, 0.000000, 0.000007, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2755, "ab_dojowall", "ab_trellis", 0x00000000);
    jbxtxc = CreateDynamicObject(970, 1369.117797, 1566.314941, 16.750324, 0.000000, 0.000007, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2755, "ab_dojowall", "ab_trellis", 0x00000000);
    jbxtxc = CreateDynamicObject(970, 1364.937988, 1566.314941, 16.750324, 0.000000, 0.000007, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2755, "ab_dojowall", "ab_trellis", 0x00000000);
    jbxtxc = CreateDynamicObject(970, 1373.258178, 1584.876953, 16.750324, 0.000000, 0.000014, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2755, "ab_dojowall", "ab_trellis", 0x00000000);
    jbxtxc = CreateDynamicObject(970, 1369.117797, 1584.876953, 16.750324, 0.000000, 0.000014, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2755, "ab_dojowall", "ab_trellis", 0x00000000);
    jbxtxc = CreateDynamicObject(970, 1364.937988, 1584.876953, 16.750324, 0.000000, 0.000014, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2755, "ab_dojowall", "ab_trellis", 0x00000000);
    jbxtxc = CreateDynamicObject(19447, 1385.317260, 1584.901489, 18.860300, -89.999992, 90.000053, 89.999961, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19447, 1388.507080, 1585.081665, 17.730302, -89.999992, 90.000053, 89.999961, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19447, 1386.977172, 1578.442382, 20.260307, -89.999992, 0.000029, 89.999961, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(3494, 1392.882690, 1587.483520, 19.740314, 0.000000, 0.000014, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14623, "mafcasmain", "ab_tileStar", 0xFFBBBBBB);
    jbxtxc = CreateDynamicObject(19434, 1385.619995, 1577.621582, 21.794422, -48.799972, 179.999847, 179.999679, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19718, 1386.853515, 1581.021240, 20.700313, 0.000000, 0.000029, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5710, "cemetery_law", "brickgrey", 0x00000000);
    jbxtxc = CreateDynamicObject(3261, 1388.597290, 1580.947143, 19.350299, 0.000029, 0.000000, 89.999908, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5710, "cemetery_law", "brickgrey", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1385.627441, 1579.070434, 23.132614, -34.799972, 179.999847, 179.999679, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1385.619995, 1580.646118, 23.548984, -14.799975, 179.999847, 179.999679, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1385.603027, 1582.448852, 23.384933, 5.200022, 179.999847, 179.999679, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1385.617431, 1583.633422, 22.661628, 25.200021, 179.999847, 179.999694, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1385.619995, 1583.548828, 22.591217, 50.200019, 179.999847, 179.999694, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19434, 1388.550415, 1584.233642, 21.794422, -48.799964, 179.999862, -0.000326, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1388.542968, 1582.784790, 23.132614, -34.799961, 179.999862, -0.000329, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1388.550415, 1581.209106, 23.548984, -14.799969, 179.999862, -0.000331, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1388.567382, 1579.406372, 23.384933, 5.200022, 179.999862, -0.000334, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1388.552978, 1578.221801, 22.661628, 25.200016, 179.999862, -0.000322, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1388.550415, 1578.306396, 22.591217, 50.200016, 179.999862, -0.000329, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19447, 1386.977172, 1566.942138, 20.260307, -89.999992, 0.000086, 89.999961, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19434, 1385.619995, 1565.990722, 21.794422, -48.799964, 179.999801, 179.999542, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19447, 1386.977172, 1572.692138, 20.260307, -89.999992, 0.000067, 89.999961, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19434, 1385.619995, 1571.871337, 21.794422, -48.799964, 179.999816, 179.999588, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19718, 1386.853515, 1575.270996, 20.700313, 0.000000, 0.000044, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5710, "cemetery_law", "brickgrey", 0x00000000);
    jbxtxc = CreateDynamicObject(3261, 1388.597290, 1575.196899, 19.350299, 0.000044, 0.000000, 89.999862, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5710, "cemetery_law", "brickgrey", 0x00000000);
    jbxtxc = CreateDynamicObject(19447, 1386.977172, 1583.332763, 20.260307, -89.999992, 0.000029, 89.999961, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1385.627441, 1573.320190, 23.132614, -34.799964, 179.999816, 179.999588, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1385.619995, 1574.895874, 23.548984, -14.799971, 179.999816, 179.999588, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1385.603027, 1576.698608, 23.384933, 5.200022, 179.999816, 179.999588, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1385.617431, 1577.883178, 22.661628, 25.200017, 179.999816, 179.999633, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1385.619995, 1577.798583, 22.591217, 50.200016, 179.999816, 179.999633, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19434, 1388.550415, 1578.483398, 21.794422, -48.799964, 179.999893, -0.000308, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1388.542968, 1577.034545, 23.132614, -34.799957, 179.999893, -0.000319, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1388.550415, 1575.458862, 23.548984, -14.799969, 179.999893, -0.000327, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1388.567382, 1573.656127, 23.384933, 5.200022, 179.999893, -0.000337, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1388.552978, 1572.471557, 22.661628, 25.200014, 179.999893, -0.000330, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1388.550415, 1572.556152, 22.591217, 50.200016, 179.999893, -0.000346, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19447, 1386.977172, 1577.582519, 20.260307, -89.999992, 0.000067, 89.999961, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19718, 1386.853515, 1569.390380, 20.700313, 0.000000, 0.000050, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5710, "cemetery_law", "brickgrey", 0x00000000);
    jbxtxc = CreateDynamicObject(3261, 1388.597290, 1569.316284, 19.350299, 0.000050, 0.000000, 89.999839, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5710, "cemetery_law", "brickgrey", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1385.627441, 1567.439575, 23.132614, -34.799961, 179.999801, 179.999542, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1385.619995, 1569.015258, 23.548984, -14.799969, 179.999801, 179.999542, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1385.603027, 1570.817993, 23.384933, 5.200022, 179.999801, 179.999542, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1385.617431, 1572.002563, 22.661628, 25.200016, 179.999801, 179.999603, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1385.619995, 1571.917968, 22.591217, 50.200016, 179.999801, 179.999603, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19434, 1388.550415, 1572.602783, 21.794422, -48.799964, 179.999908, -0.000300, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1388.542968, 1571.153930, 23.132614, -34.799957, 179.999908, -0.000312, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19377, 1361.142089, 1562.521728, 18.710317, 0.000000, -0.000007, 179.999954, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1388.550415, 1569.578247, 23.548984, -14.799969, 179.999908, -0.000326, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(18762, 1377.931884, 1575.044677, 16.370309, 0.000014, 90.000000, 89.999954, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    jbxtxc = CreateDynamicObject(18762, 1379.932250, 1572.063232, 16.370309, 0.000000, 89.999984, 179.999908, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    jbxtxc = CreateDynamicObject(18762, 1379.932250, 1578.045410, 16.370309, 0.000000, 89.999984, 179.999908, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    jbxtxc = CreateDynamicObject(18762, 1377.841674, 1575.044677, 16.230304, 0.000014, 90.000000, 89.999954, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14581, "ab_mafiasuitea", "wood02S", 0x00000000);
    jbxtxc = CreateDynamicObject(18762, 1379.822387, 1571.942993, 16.240308, 0.000000, 89.999984, 179.999908, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14581, "ab_mafiasuitea", "wood02S", 0x00000000);
    jbxtxc = CreateDynamicObject(18762, 1379.822387, 1578.132934, 16.240308, 0.000000, 89.999984, 179.999908, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14581, "ab_mafiasuitea", "wood02S", 0x00000000);
    jbxtxc = CreateDynamicObject(19940, 1377.531127, 1577.321655, 17.040323, 0.000000, 90.000022, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0x00000000);
    jbxtxc = CreateDynamicObject(19940, 1377.531127, 1575.341918, 17.040323, 0.000000, 90.000022, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0x00000000);
    jbxtxc = CreateDynamicObject(19940, 1377.530029, 1573.341430, 17.040323, 0.000000, 90.000022, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0x00000000);
    jbxtxc = CreateDynamicObject(19940, 1378.532836, 1578.312011, 17.040323, 0.000022, 90.000015, 89.999900, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0x00000000);
    jbxtxc = CreateDynamicObject(19940, 1380.512573, 1578.312011, 17.040323, 0.000022, 90.000015, 89.999900, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0x00000000);
    jbxtxc = CreateDynamicObject(19940, 1378.532836, 1571.732177, 17.040323, 0.000022, 90.000015, 89.999900, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0x00000000);
    jbxtxc = CreateDynamicObject(19940, 1380.512573, 1571.732177, 17.040323, 0.000022, 90.000015, 89.999900, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0x00000000);
    jbxtxc = CreateDynamicObject(19940, 1377.531127, 1572.731567, 17.040323, 0.000000, 90.000022, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0x00000000);
    jbxtxc = CreateDynamicObject(2266, 1378.343750, 1573.369140, 17.130903, -7.599947, 0.000000, 89.999862, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    jbxtxc = CreateDynamicObject(19939, 1377.790039, 1573.352172, 16.870311, 0.000000, 180.000015, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    jbxtxc = CreateDynamicObject(19939, 1377.790039, 1573.352172, 16.910312, 89.999992, 360.000061, -89.999961, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    jbxtxc = CreateDynamicObject(2266, 1377.380859, 1573.409179, 17.002464, 6.199952, 0.000000, -89.999862, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(jbxtxc, 1, 10101, "2notherbuildsfe", "ferry_build14", 0xFF555555);
    jbxtxc = CreateDynamicObject(2266, 1378.343750, 1576.319091, 17.130903, -7.599939, 0.000000, 89.999839, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(jbxtxc, 1, 15040, "cuntcuts", "GB_novels06", 0x00000000);
    jbxtxc = CreateDynamicObject(19939, 1377.790039, 1576.302124, 16.870311, 0.000000, 180.000015, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    jbxtxc = CreateDynamicObject(19939, 1377.790039, 1576.302124, 16.910312, 89.999992, 360.000091, -89.999961, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    jbxtxc = CreateDynamicObject(2266, 1377.380859, 1576.359130, 17.002464, 6.199944, 0.000000, -89.999839, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(jbxtxc, 1, 10101, "2notherbuildsfe", "ferry_build14", 0xFF555555);
    jbxtxc = CreateDynamicObject(2253, 1382.050170, 1578.066040, 17.120321, 0.000000, 0.000007, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 2, 802, "gta_proc_bushland", "veg_bush3", 0x00000000);
    SetDynamicObjectMaterial(jbxtxc, 3, 802, "gta_proc_bushland", "veg_bush3", 0x00000000);
    jbxtxc = CreateDynamicObject(2253, 1382.050170, 1572.095581, 17.120321, 0.000000, 0.000007, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 2, 802, "gta_proc_bushland", "veg_bush3", 0x00000000);
    SetDynamicObjectMaterial(jbxtxc, 3, 802, "gta_proc_bushland", "veg_bush3", 0x00000000);
    jbxtxc = CreateDynamicObject(2357, 1398.191772, 1575.725830, 16.390310, 0.000000, 0.000007, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(2357, 1402.442260, 1575.725830, 16.390310, 0.000000, 0.000007, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(19377, 1400.427001, 1575.588134, 15.930309, 0.000000, 90.000007, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 10412, "hotel1", "carpet_red_256", 0x00000000);
    jbxtxc = CreateDynamicObject(1739, 1396.961303, 1574.001342, 16.856254, -0.000007, 0.000000, -89.999977, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2772, "airp_prop", "CJ_red_COUNTER", 0x00000000);
    SetDynamicObjectMaterial(jbxtxc, 1, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(1739, 1398.252563, 1574.001342, 16.856254, -0.000007, 0.000000, -89.999977, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2772, "airp_prop", "CJ_red_COUNTER", 0x00000000);
    SetDynamicObjectMaterial(jbxtxc, 1, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(1739, 1399.363281, 1574.001342, 16.856254, -0.000007, 0.000000, -89.999977, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2772, "airp_prop", "CJ_red_COUNTER", 0x00000000);
    SetDynamicObjectMaterial(jbxtxc, 1, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(1739, 1400.891479, 1574.001342, 16.856254, -0.000014, 0.000000, -89.999954, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2772, "airp_prop", "CJ_red_COUNTER", 0x00000000);
    SetDynamicObjectMaterial(jbxtxc, 1, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(1739, 1402.182739, 1574.001342, 16.856254, -0.000014, 0.000000, -89.999954, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2772, "airp_prop", "CJ_red_COUNTER", 0x00000000);
    SetDynamicObjectMaterial(jbxtxc, 1, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(1739, 1403.293457, 1574.001342, 16.856254, -0.000014, 0.000000, -89.999954, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2772, "airp_prop", "CJ_red_COUNTER", 0x00000000);
    SetDynamicObjectMaterial(jbxtxc, 1, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(1739, 1403.293579, 1577.442504, 16.856254, 0.000007, 0.000000, 89.999916, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2772, "airp_prop", "CJ_red_COUNTER", 0x00000000);
    SetDynamicObjectMaterial(jbxtxc, 1, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(1739, 1402.002319, 1577.442504, 16.856254, 0.000007, 0.000000, 89.999916, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2772, "airp_prop", "CJ_red_COUNTER", 0x00000000);
    SetDynamicObjectMaterial(jbxtxc, 1, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(1739, 1400.891601, 1577.442504, 16.856254, 0.000007, 0.000000, 89.999916, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2772, "airp_prop", "CJ_red_COUNTER", 0x00000000);
    SetDynamicObjectMaterial(jbxtxc, 1, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(1739, 1399.363403, 1577.442504, 16.856254, 0.000000, 0.000000, 89.999938, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2772, "airp_prop", "CJ_red_COUNTER", 0x00000000);
    SetDynamicObjectMaterial(jbxtxc, 1, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(1739, 1398.072143, 1577.442504, 16.856254, 0.000000, 0.000000, 89.999938, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2772, "airp_prop", "CJ_red_COUNTER", 0x00000000);
    SetDynamicObjectMaterial(jbxtxc, 1, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(1739, 1396.961425, 1577.442504, 16.856254, 0.000000, 0.000000, 89.999938, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2772, "airp_prop", "CJ_red_COUNTER", 0x00000000);
    SetDynamicObjectMaterial(jbxtxc, 1, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(19940, 1397.058593, 1575.745971, 16.790309, 0.000007, 0.000000, 89.999977, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14803, "bdupsnew", "Bdup2_Artex", 0x00000000);
    jbxtxc = CreateDynamicObject(19940, 1399.059082, 1575.745971, 16.790309, 0.000007, 0.000000, 89.999977, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14803, "bdupsnew", "Bdup2_Artex", 0x00000000);
    jbxtxc = CreateDynamicObject(19940, 1401.029785, 1575.745971, 16.790309, 0.000014, 0.000000, 89.999954, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14803, "bdupsnew", "Bdup2_Artex", 0x00000000);
    jbxtxc = CreateDynamicObject(19940, 1403.030273, 1575.745971, 16.790309, 0.000014, 0.000000, 89.999954, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14803, "bdupsnew", "Bdup2_Artex", 0x00000000);
    jbxtxc = CreateDynamicObject(19940, 1403.580810, 1575.745971, 16.790004, 0.000014, 0.000000, 89.999954, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14803, "bdupsnew", "Bdup2_Artex", 0x00000000);
    jbxtxc = CreateDynamicObject(3494, 1400.492797, 1563.233276, 19.740314, 0.000000, 0.000020, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14623, "mafcasmain", "ab_tileStar", 0xFFBBBBBB);
    jbxtxc = CreateDynamicObject(3494, 1400.492797, 1587.483520, 19.740314, 0.000000, 0.000020, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14623, "mafcasmain", "ab_tileStar", 0xFFBBBBBB);
    jbxtxc = CreateDynamicObject(3494, 1407.343383, 1563.233276, 19.740314, 0.000000, 0.000029, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14623, "mafcasmain", "ab_tileStar", 0xFFBBBBBB);
    jbxtxc = CreateDynamicObject(3494, 1407.343383, 1587.483520, 19.740314, 0.000000, 0.000029, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14623, "mafcasmain", "ab_tileStar", 0xFFBBBBBB);
    jbxtxc = CreateDynamicObject(3494, 1407.343383, 1569.173095, 19.740314, 0.000000, 0.000029, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14623, "mafcasmain", "ab_tileStar", 0xFFBBBBBB);
    jbxtxc = CreateDynamicObject(3494, 1407.343383, 1582.123046, 19.740314, 0.000000, 0.000029, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14623, "mafcasmain", "ab_tileStar", 0xFFBBBBBB);
    jbxtxc = CreateDynamicObject(8661, 1398.775512, 1575.578979, 16.000005, 0.000007, 0.000007, 89.999977, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 10412, "hotel1", "carpet_red_256", 0x00000000);
    jbxtxc = CreateDynamicObject(8661, 1374.075805, 1566.108886, 23.780315, 0.000000, 179.999984, -179.999984, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14748, "sfhsm1", "ah_pnwainscot6", 0x00000000);
    jbxtxc = CreateDynamicObject(8661, 1374.075805, 1583.429687, 23.770004, 0.000000, 179.999984, -179.999984, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14748, "sfhsm1", "ah_pnwainscot6", 0x00000000);
    jbxtxc = CreateDynamicObject(18980, 1393.600463, 1575.256591, 24.290306, 0.000007, 90.000000, 89.999977, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14581, "ab_mafiasuitea", "wood02S", 0x00000000);
    jbxtxc = CreateDynamicObject(18980, 1406.420532, 1563.636962, 24.290306, 0.000000, 89.999992, 179.999954, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14581, "ab_mafiasuitea", "wood02S", 0x00000000);
    jbxtxc = CreateDynamicObject(18980, 1406.420532, 1587.146850, 24.290306, 0.000000, 89.999992, 179.999954, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14581, "ab_mafiasuitea", "wood02S", 0x00000000);
    jbxtxc = CreateDynamicObject(18980, 1407.080322, 1575.256591, 24.290306, 0.000007, 90.000000, 89.999977, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14581, "ab_mafiasuitea", "wood02S", 0x00000000);
    jbxtxc = CreateDynamicObject(8661, 1426.545532, 1566.108886, 23.780315, 0.000000, 179.999984, -179.999938, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14748, "sfhsm1", "ah_pnwainscot6", 0x00000000);
    jbxtxc = CreateDynamicObject(8661, 1426.545532, 1583.429687, 23.780006, 0.000000, 179.999984, -179.999938, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14748, "sfhsm1", "ah_pnwainscot6", 0x00000000);
    jbxtxc = CreateDynamicObject(8661, 1406.295166, 1597.509521, 23.783004, 0.000000, 179.999984, -179.999938, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14748, "sfhsm1", "ah_pnwainscot6", 0x00000000);
    jbxtxc = CreateDynamicObject(8661, 1406.295166, 1554.159667, 23.783004, 0.000000, 179.999984, -179.999938, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14748, "sfhsm1", "ah_pnwainscot6", 0x00000000);
    jbxtxc = CreateDynamicObject(8661, 1401.715087, 1573.949584, 24.783000, -0.000007, 179.999984, -89.999961, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5401, "jeffers4_lae", "stainwinLAe", 0x00000000);
    jbxtxc = CreateDynamicObject(19377, 1388.732055, 1588.089599, 18.880315, 0.000000, 0.000007, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(18981, 1401.312255, 1592.879638, 18.880315, 0.000007, 0.000000, 89.999977, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    jbxtxc = CreateDynamicObject(19696, 1396.761718, 1596.483764, 18.600006, 0.000000, 179.999984, -90.000007, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14581, "ab_mafiasuitea", "wood02S", 0x00000000);
    jbxtxc = CreateDynamicObject(19696, 1396.761718, 1596.483764, 18.870004, 0.000000, 179.999984, -90.000007, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14581, "ab_mafiasuitea", "wood02S", 0x00000000);
    jbxtxc = CreateDynamicObject(19456, 1396.788330, 1591.558105, 24.750305, 89.999992, 0.000000, -89.999977, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14581, "ab_mafiasuitea", "wood02S", 0x00000000);
    jbxtxc = CreateDynamicObject(19696, 1396.761718, 1596.483764, 19.190004, 0.000000, 179.999984, -90.000007, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14581, "ab_mafiasuitea", "wood02S", 0x00000000);
    jbxtxc = CreateDynamicObject(19696, 1403.702392, 1596.483764, 18.560005, -0.000007, 179.999984, -89.999984, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14581, "ab_mafiasuitea", "wood02S", 0x00000000);
    jbxtxc = CreateDynamicObject(19696, 1403.702392, 1596.483764, 18.830003, -0.000007, 179.999984, -89.999984, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14581, "ab_mafiasuitea", "wood02S", 0x00000000);
    jbxtxc = CreateDynamicObject(19456, 1403.729003, 1591.558105, 24.750305, 89.999992, 0.000011, -89.999969, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14581, "ab_mafiasuitea", "wood02S", 0x00000000);
    jbxtxc = CreateDynamicObject(19696, 1403.702392, 1596.483764, 19.110006, -0.000007, 179.999984, -89.999984, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14581, "ab_mafiasuitea", "wood02S", 0x00000000);
    jbxtxc = CreateDynamicObject(19696, 1403.725341, 1554.298339, 18.570005, 0.000022, 179.999984, 89.999923, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14581, "ab_mafiasuitea", "wood02S", 0x00000000);
    jbxtxc = CreateDynamicObject(19696, 1403.725341, 1554.298339, 18.840002, 0.000022, 179.999984, 89.999923, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14581, "ab_mafiasuitea", "wood02S", 0x00000000);
    jbxtxc = CreateDynamicObject(19456, 1403.698730, 1559.223999, 24.750305, 89.999992, 154.444061, -64.444099, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14581, "ab_mafiasuitea", "wood02S", 0x00000000);
    jbxtxc = CreateDynamicObject(19696, 1403.725341, 1554.298339, 19.110006, 0.000022, 179.999984, 89.999923, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14581, "ab_mafiasuitea", "wood02S", 0x00000000);
    jbxtxc = CreateDynamicObject(19696, 1396.784667, 1554.298339, 18.520004, 0.000014, 179.999984, 89.999885, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14581, "ab_mafiasuitea", "wood02S", 0x00000000);
    jbxtxc = CreateDynamicObject(19696, 1396.784667, 1554.298339, 18.810003, 0.000014, 179.999984, 89.999885, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14581, "ab_mafiasuitea", "wood02S", 0x00000000);
    jbxtxc = CreateDynamicObject(19456, 1396.758056, 1559.223999, 24.750305, 89.999992, 154.425949, -64.425971, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14581, "ab_mafiasuitea", "wood02S", 0x00000000);
    jbxtxc = CreateDynamicObject(19696, 1396.784667, 1554.298339, 19.110006, 0.000014, 179.999984, 89.999885, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14581, "ab_mafiasuitea", "wood02S", 0x00000000);
    jbxtxc = CreateDynamicObject(18981, 1401.312255, 1558.019531, 18.880315, 0.000007, 0.000000, 89.999977, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    jbxtxc = CreateDynamicObject(19377, 1390.332275, 1591.561645, 18.710317, 0.000007, 0.000000, 89.999977, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(19377, 1410.171997, 1591.541625, 18.710317, 0.000007, 0.000000, 89.999977, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(19456, 1400.358764, 1591.558105, 20.750316, 89.999992, 0.000000, -89.999977, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14623, "mafcasmain", "ab_tileStar", 0xFFBBBBBB);
    jbxtxc = CreateDynamicObject(19373, 1388.567382, 1567.775512, 23.384933, 5.200022, 179.999908, -0.000337, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19456, 1400.268676, 1559.238281, 20.750316, 89.999992, 0.000000, -89.999977, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14623, "mafcasmain", "ab_tileStar", 0xFFBBBBBB);
    jbxtxc = CreateDynamicObject(19377, 1410.171997, 1559.211547, 18.710317, 0.000007, 0.000000, 89.999977, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(19377, 1390.202026, 1559.211547, 18.710317, 0.000007, 0.000000, 89.999977, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(19377, 1388.792358, 1562.221679, 18.710317, 0.000000, -0.000007, 179.999954, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19696, 1413.607299, 1579.206542, 18.820007, 0.000022, 179.999984, 179.999801, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14581, "ab_mafiasuitea", "wood02S", 0x00000000);
    jbxtxc = CreateDynamicObject(19456, 1408.681640, 1579.179931, 24.750305, 89.999992, 240.295394, -60.295467, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14581, "ab_mafiasuitea", "wood02S", 0x00000000);
    jbxtxc = CreateDynamicObject(19696, 1413.607299, 1579.206542, 19.110006, 0.000022, 179.999984, 179.999801, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14581, "ab_mafiasuitea", "wood02S", 0x00000000);
    jbxtxc = CreateDynamicObject(19696, 1413.607299, 1572.265869, 18.520004, 0.000014, 179.999984, 179.999771, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14581, "ab_mafiasuitea", "wood02S", 0x00000000);
    jbxtxc = CreateDynamicObject(19696, 1413.607299, 1572.265869, 18.810003, 0.000014, 179.999984, 179.999771, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14581, "ab_mafiasuitea", "wood02S", 0x00000000);
    jbxtxc = CreateDynamicObject(19456, 1408.681640, 1572.239257, 24.750305, 89.999992, 240.276290, -60.276348, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14581, "ab_mafiasuitea", "wood02S", 0x00000000);
    jbxtxc = CreateDynamicObject(19696, 1413.607299, 1572.265869, 19.110006, 0.000014, 179.999984, 179.999771, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14581, "ab_mafiasuitea", "wood02S", 0x00000000);
    jbxtxc = CreateDynamicObject(18981, 1409.886108, 1576.793457, 18.880315, 0.000007, -0.000014, 179.999862, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    jbxtxc = CreateDynamicObject(19456, 1408.667358, 1575.749877, 20.750316, 89.999992, 64.471206, -64.471214, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5401, "jeffers4_lae", "stainwinLAe", 0x00000000);
    jbxtxc = CreateDynamicObject(19377, 1408.694091, 1585.653198, 18.710317, 0.000007, -0.000014, 179.999862, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(19377, 1408.694091, 1565.683227, 18.710317, 0.000007, -0.000014, 179.999862, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(19377, 1408.694091, 1595.223022, 18.710317, 0.000007, -0.000014, 179.999862, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(19377, 1408.694091, 1556.062744, 18.710321, 0.000007, -0.000014, 179.999862, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(970, 1383.878662, 1565.994750, 16.750324, 0.000000, 0.000007, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2755, "ab_dojowall", "ab_trellis", 0x00000000);
    jbxtxc = CreateDynamicObject(970, 1383.878662, 1584.764648, 16.750324, 0.000000, 0.000014, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2755, "ab_dojowall", "ab_trellis", 0x00000000);
    jbxtxc = CreateDynamicObject(14410, 1379.464965, 1563.652099, 12.790323, 0.000000, 0.000014, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14760, "sfhosemed2", "AH_walltile2", 0x00000000);
    SetDynamicObjectMaterial(jbxtxc, 1, 14760, "sfhosemed2", "AH_walltile2", 0xFFBBBBBB);
    jbxtxc = CreateDynamicObject(14410, 1377.274291, 1563.640014, 12.790003, 0.000000, 0.000014, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14760, "sfhosemed2", "AH_walltile2", 0x00000000);
    SetDynamicObjectMaterial(jbxtxc, 1, 14760, "sfhosemed2", "AH_walltile2", 0xFFBBBBBB);
    jbxtxc = CreateDynamicObject(8661, 1380.835693, 1555.959594, 14.670305, 0.000000, 0.000014, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14754, "sfhsb3", "ah_wpaper10", 0x00000000);
    jbxtxc = CreateDynamicObject(19377, 1385.292114, 1562.221679, 18.710317, 0.000000, -0.000007, 179.999954, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19377, 1370.522338, 1565.671508, 10.830320, -0.000007, 0.000000, -89.999977, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14760, "sfhosemed2", "ah_pnwainscot3", 0x00000000);
    jbxtxc = CreateDynamicObject(19377, 1360.882568, 1565.671508, 10.830320, -0.000007, 0.000000, -89.999977, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14760, "sfhosemed2", "ah_pnwainscot3", 0x00000000);
    jbxtxc = CreateDynamicObject(19377, 1359.642700, 1566.031616, 21.280315, -0.000007, 0.000000, -89.999977, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1375.242431, 1565.134277, 14.220295, 0.000000, 0.000007, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14760, "sfhosemed2", "ah_pnwainscot3", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1381.622314, 1565.134277, 14.220295, 0.000000, 0.000007, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14760, "sfhosemed2", "ah_pnwainscot3", 0x00000000);
    jbxtxc = CreateDynamicObject(18764, 1384.144165, 1566.107543, 12.830307, 0.000000, 0.000007, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    jbxtxc = CreateDynamicObject(19373, 1383.112182, 1565.671508, 14.340319, -0.000007, 0.000000, -89.999977, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14760, "sfhosemed2", "ah_pnwainscot3", 0x00000000);
    jbxtxc = CreateDynamicObject(19377, 1385.292114, 1557.622070, 18.710317, -0.000007, 0.000000, -89.999977, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(19377, 1371.712158, 1557.622070, 18.710317, -0.000007, 0.000000, -89.999977, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(19377, 1379.112182, 1557.619995, 22.440322, -0.000007, 0.000000, -89.999977, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(19857, 1375.450561, 1557.587402, 14.930292, 0.000000, 0.000045, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    SetDynamicObjectMaterial(jbxtxc, 1, 5401, "jeffers4_lae", "stainwinLAe", 0x00000000);
    jbxtxc = CreateDynamicObject(1499, 1379.958374, 1557.583129, 14.678438, 0.000000, -0.000014, 179.999908, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14581, "ab_mafiasuitea", "cof_wood2", 0x00000000);
    SetDynamicObjectMaterial(jbxtxc, 1, 14581, "ab_mafiasuitea", "wood02S", 0x00000000);
    jbxtxc = CreateDynamicObject(1499, 1376.947753, 1593.483154, 14.678438, 0.000000, 0.000014, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14581, "ab_mafiasuitea", "cof_wood2", 0x00000000);
    SetDynamicObjectMaterial(jbxtxc, 1, 14581, "ab_mafiasuitea", "wood02S", 0x00000000);
    jbxtxc = CreateDynamicObject(19857, 1376.950317, 1557.580078, 17.410306, 0.000000, 180.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    SetDynamicObjectMaterial(jbxtxc, 1, 5401, "jeffers4_lae", "stainwinLAe", 0x00000000);
    jbxtxc = CreateDynamicObject(19857, 1379.952880, 1557.587402, 14.930292, 0.000000, 0.000059, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    SetDynamicObjectMaterial(jbxtxc, 1, 5401, "jeffers4_lae", "stainwinLAe", 0x00000000);
    jbxtxc = CreateDynamicObject(19857, 1381.434204, 1557.587402, 17.410306, 0.000000, 180.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    SetDynamicObjectMaterial(jbxtxc, 1, 5401, "jeffers4_lae", "stainwinLAe", 0x00000000);
    jbxtxc = CreateDynamicObject(1499, 1379.957763, 1593.483154, 14.678438, 0.000000, 0.000000, 179.999954, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14581, "ab_mafiasuitea", "cof_wood2", 0x00000000);
    SetDynamicObjectMaterial(jbxtxc, 1, 14581, "ab_mafiasuitea", "wood02S", 0x00000000);
    jbxtxc = CreateDynamicObject(8661, 1428.735473, 1575.578979, 16.000307, 0.000000, 0.000014, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14754, "sfhsb3", "ah_wpaper10", 0x00000000);
    jbxtxc = CreateDynamicObject(19377, 1362.082519, 1557.622070, 18.710317, -0.000007, 0.000000, -89.999977, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(8661, 1380.835693, 1527.700927, 14.680006, 0.000000, 0.000014, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14387, "dr_gsnew", "la_flair1", 0x00000000);
    jbxtxc = CreateDynamicObject(19929, 1385.551513, 1540.664428, 14.650303, 0.000000, 0.000022, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 1, 14760, "sfhosemed2", "ah_pnwainscot3", 0xFF706353);
    jbxtxc = CreateDynamicObject(19929, 1385.551513, 1543.274536, 14.650303, 0.000000, 0.000022, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 1, 14760, "sfhosemed2", "ah_pnwainscot3", 0xFF706353);
    jbxtxc = CreateDynamicObject(19929, 1370.991210, 1543.274536, 14.650303, 0.000000, -0.000022, 179.999710, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 1, 14760, "sfhosemed2", "ah_pnwainscot3", 0xFF706353);
    jbxtxc = CreateDynamicObject(19929, 1370.991210, 1540.664428, 14.650303, 0.000000, -0.000022, 179.999710, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 1, 14760, "sfhosemed2", "ah_pnwainscot3", 0xFF706353);
    jbxtxc = CreateDynamicObject(19373, 1384.445678, 1544.631103, 13.840307, -0.000014, 179.999984, -90.000053, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14760, "sfhosemed2", "ah_pnwainscot3", 0xFF706353);
    jbxtxc = CreateDynamicObject(19373, 1381.235717, 1544.631103, 13.840307, -0.000014, 179.999984, -90.000053, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14760, "sfhosemed2", "ah_pnwainscot3", 0xFF706353);
    jbxtxc = CreateDynamicObject(19373, 1372.164794, 1544.631103, 13.840307, -0.000014, 179.999984, -90.000076, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14760, "sfhosemed2", "ah_pnwainscot3", 0xFF706353);
    jbxtxc = CreateDynamicObject(19373, 1375.295776, 1544.631103, 13.840307, -0.000014, 179.999984, -90.000076, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14760, "sfhosemed2", "ah_pnwainscot3", 0xFF706353);
    jbxtxc = CreateDynamicObject(19929, 1385.551513, 1537.824096, 14.650303, 0.000000, 0.000029, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 1, 14760, "sfhosemed2", "ah_pnwainscot3", 0xFF706353);
    jbxtxc = CreateDynamicObject(19929, 1370.991210, 1537.824096, 14.650303, 0.000000, -0.000029, 179.999664, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 1, 14760, "sfhosemed2", "ah_pnwainscot3", 0xFF706353);
    jbxtxc = CreateDynamicObject(19929, 1382.401611, 1532.364013, 14.650303, -0.000022, 0.000014, -89.999931, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 1, 14760, "sfhosemed2", "ah_pnwainscot3", 0xFF706353);
    jbxtxc = CreateDynamicObject(19929, 1379.561523, 1532.364013, 14.650303, -0.000022, 0.000014, -89.999931, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 1, 14760, "sfhosemed2", "ah_pnwainscot3", 0xFF706353);
    jbxtxc = CreateDynamicObject(19929, 1376.732055, 1532.364013, 14.650303, -0.000029, 0.000014, -89.999908, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 1, 14760, "sfhosemed2", "ah_pnwainscot3", 0xFF706353);
    jbxtxc = CreateDynamicObject(19929, 1373.891967, 1532.364013, 14.650303, -0.000029, 0.000014, -89.999908, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 1, 14760, "sfhosemed2", "ah_pnwainscot3", 0xFF706353);
    jbxtxc = CreateDynamicObject(19377, 1388.752807, 1552.811767, 18.710317, 0.000000, 0.000007, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(19377, 1388.752807, 1543.252441, 18.710317, 0.000000, 0.000007, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(19377, 1388.752807, 1533.642578, 18.710317, 0.000000, 0.000007, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(19377, 1367.301269, 1552.811767, 18.710317, 0.000000, 0.000014, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(19377, 1367.301269, 1543.252441, 18.710317, 0.000000, 0.000014, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(19377, 1367.301269, 1533.642578, 18.710317, 0.000000, 0.000014, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(19377, 1367.757446, 1528.747436, 18.710317, 0.000014, 0.000014, 89.999923, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(19377, 1377.376831, 1528.747436, 18.710317, 0.000014, 0.000014, 89.999923, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(19377, 1390.886596, 1528.747436, 18.710317, 0.000014, 0.000014, 89.999923, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(8661, 1381.765991, 1537.749023, 20.520313, 0.000000, 179.999984, -90.000007, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14387, "dr_gsnew", "mp_gs_border1", 0x00000000);
    jbxtxc = CreateDynamicObject(8661, 1365.126831, 1537.749023, 20.520313, 0.000000, 179.999984, -90.000007, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14387, "dr_gsnew", "mp_gs_border1", 0x00000000);
    jbxtxc = CreateDynamicObject(19937, 1388.531005, 1555.358764, 16.550308, 89.999992, 180.000015, -89.999969, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(19937, 1388.531005, 1555.358764, 18.400308, 89.999992, 180.000015, -89.999969, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(19937, 1388.531005, 1553.049316, 16.550308, 89.999992, 180.000061, -89.999961, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(19937, 1388.531005, 1553.049316, 18.400308, 89.999992, 180.000061, -89.999961, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(19937, 1389.551513, 1554.189453, 19.140321, 0.000000, 270.000000, 0.000020, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(19937, 1389.551513, 1554.189453, 15.810005, 0.000000, 270.000000, 0.000020, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(19434, 1388.735229, 1553.675537, 17.380300, 0.000000, 0.000014, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5401, "jeffers4_lae", "stainwinLAe", 0x00000000);
    jbxtxc = CreateDynamicObject(19434, 1388.715209, 1554.626220, 17.380300, 0.000000, 0.000014, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5401, "jeffers4_lae", "stainwinLAe", 0x00000000);
    jbxtxc = CreateDynamicObject(19937, 1388.531005, 1549.708374, 16.550308, 89.999992, 180.000030, -89.999961, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(19937, 1388.531005, 1549.708374, 18.400308, 89.999992, 180.000030, -89.999961, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(19937, 1388.531005, 1547.398925, 16.550308, 89.999992, 180.000076, -89.999961, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(19937, 1388.531005, 1547.398925, 18.400308, 89.999992, 180.000076, -89.999961, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(19937, 1389.551513, 1548.539062, 19.140321, 0.000000, 270.000000, 0.000020, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(19937, 1389.551513, 1548.539062, 15.810005, 0.000000, 270.000000, 0.000020, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(19434, 1388.735229, 1548.025146, 17.380300, 0.000000, 0.000022, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5401, "jeffers4_lae", "stainwinLAe", 0x00000000);
    jbxtxc = CreateDynamicObject(19434, 1388.715209, 1548.975830, 17.380300, 0.000000, 0.000022, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5401, "jeffers4_lae", "stainwinLAe", 0x00000000);
    jbxtxc = CreateDynamicObject(19937, 1388.531005, 1543.218261, 16.550308, 89.999992, 180.000030, -89.999961, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(19937, 1388.531005, 1543.218261, 18.400308, 89.999992, 180.000030, -89.999961, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(19937, 1388.531005, 1540.908813, 16.550308, 89.999992, 180.000076, -89.999961, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(19937, 1388.531005, 1540.908813, 18.400308, 89.999992, 180.000076, -89.999961, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(19937, 1389.551513, 1542.048950, 19.140321, 0.000000, 270.000000, 0.000020, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(19937, 1389.551513, 1542.048950, 15.810005, 0.000000, 270.000000, 0.000020, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(19434, 1388.735229, 1541.535034, 17.380300, 0.000000, 0.000022, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5401, "jeffers4_lae", "stainwinLAe", 0x00000000);
    jbxtxc = CreateDynamicObject(19434, 1388.715209, 1542.485717, 17.380300, 0.000000, 0.000022, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5401, "jeffers4_lae", "stainwinLAe", 0x00000000);
    jbxtxc = CreateDynamicObject(19937, 1388.531005, 1537.567871, 16.550308, 89.999992, 180.000045, -89.999961, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(19937, 1388.531005, 1537.567871, 18.400308, 89.999992, 180.000045, -89.999961, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(19937, 1388.531005, 1535.258422, 16.550308, 89.999992, 180.000091, -89.999961, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(19937, 1388.531005, 1535.258422, 18.400308, 89.999992, 180.000091, -89.999961, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(19937, 1389.551513, 1536.398559, 19.140321, 0.000000, 270.000000, 0.000020, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(19937, 1389.551513, 1536.398559, 15.810005, 0.000000, 270.000000, 0.000020, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(19434, 1388.735229, 1535.884643, 17.380300, 0.000000, 0.000029, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5401, "jeffers4_lae", "stainwinLAe", 0x00000000);
    jbxtxc = CreateDynamicObject(19434, 1388.715209, 1536.835327, 17.380300, 0.000000, 0.000029, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5401, "jeffers4_lae", "stainwinLAe", 0x00000000);
    jbxtxc = CreateDynamicObject(19937, 1367.520874, 1535.258422, 16.550308, 89.999992, 314.934020, -44.934055, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(19937, 1367.520874, 1535.258422, 18.400308, 89.999992, 314.934020, -44.934055, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(19937, 1367.520874, 1537.567871, 16.550308, 89.999992, 314.901062, -44.901054, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(19937, 1367.520874, 1537.567871, 18.400308, 89.999992, 314.901062, -44.901054, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(19937, 1366.500366, 1536.427734, 19.140321, 0.000000, 270.000000, -179.999862, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(19937, 1366.500366, 1536.427734, 15.810005, 0.000000, 270.000000, -179.999862, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(19434, 1367.316650, 1536.941650, 17.380300, 0.000000, 0.000000, 179.999847, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5401, "jeffers4_lae", "stainwinLAe", 0x00000000);
    jbxtxc = CreateDynamicObject(19434, 1367.336669, 1535.990966, 17.380300, 0.000000, 0.000000, 179.999847, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5401, "jeffers4_lae", "stainwinLAe", 0x00000000);
    jbxtxc = CreateDynamicObject(19937, 1367.520874, 1540.908813, 16.550308, 89.999992, 314.934051, -44.934055, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(19937, 1367.520874, 1540.908813, 18.400308, 89.999992, 314.934051, -44.934062, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(19937, 1367.520874, 1543.218261, 16.550308, 89.999992, 314.901123, -44.901054, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(19937, 1367.520874, 1543.218261, 18.400308, 89.999992, 314.901123, -44.901054, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(19937, 1366.500366, 1542.078125, 19.140321, 0.000000, 270.000000, -179.999862, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(19937, 1366.500366, 1542.078125, 15.810005, 0.000000, 270.000000, -179.999862, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(19434, 1367.316650, 1542.592041, 17.380300, 0.000000, 0.000007, 179.999847, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5401, "jeffers4_lae", "stainwinLAe", 0x00000000);
    jbxtxc = CreateDynamicObject(19434, 1367.336669, 1541.641357, 17.380300, 0.000000, 0.000007, 179.999847, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5401, "jeffers4_lae", "stainwinLAe", 0x00000000);
    jbxtxc = CreateDynamicObject(19937, 1367.520874, 1547.398925, 16.550308, 89.999992, 314.934051, -44.934055, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(19937, 1367.520874, 1547.398925, 18.400308, 89.999992, 314.934051, -44.934055, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(19937, 1367.520874, 1549.708374, 16.550308, 89.999992, 314.901123, -44.901054, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(19937, 1367.520874, 1549.708374, 18.400308, 89.999992, 314.901123, -44.901054, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(19937, 1366.500366, 1548.568237, 19.140321, 0.000000, 270.000000, -179.999862, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(19937, 1366.500366, 1548.568237, 15.810005, 0.000000, 270.000000, -179.999862, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(19434, 1367.316650, 1549.082153, 17.380300, 0.000000, 0.000007, 179.999847, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5401, "jeffers4_lae", "stainwinLAe", 0x00000000);
    jbxtxc = CreateDynamicObject(19434, 1367.336669, 1548.131469, 17.380300, 0.000000, 0.000007, 179.999847, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5401, "jeffers4_lae", "stainwinLAe", 0x00000000);
    jbxtxc = CreateDynamicObject(19937, 1367.520874, 1553.049316, 16.550308, 89.999992, 314.901062, -44.901054, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(19937, 1367.520874, 1553.049316, 18.400308, 89.999992, 314.901062, -44.901054, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(19937, 1367.520874, 1555.358764, 16.550308, 89.999992, 314.901123, -44.901054, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(19937, 1367.520874, 1555.358764, 18.400308, 89.999992, 314.901123, -44.901054, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(19937, 1366.500366, 1554.218627, 19.140321, 0.000000, 270.000000, -179.999862, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(19937, 1366.500366, 1554.218627, 15.810005, 0.000000, 270.000000, -179.999862, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    jbxtxc = CreateDynamicObject(19434, 1367.316650, 1554.732543, 17.380300, 0.000000, 0.000014, 179.999847, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5401, "jeffers4_lae", "stainwinLAe", 0x00000000);
    jbxtxc = CreateDynamicObject(19434, 1367.336669, 1553.781860, 17.380300, 0.000000, 0.000014, 179.999847, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5401, "jeffers4_lae", "stainwinLAe", 0x00000000);
    jbxtxc = CreateDynamicObject(19447, 1378.432250, 1559.003540, 14.600298, 0.000000, 270.000000, -179.999984, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 10412, "hotel1", "carpet_red_256", 0x00000000);
    jbxtxc = CreateDynamicObject(19447, 1378.432250, 1549.423095, 14.600298, 0.000000, 270.000000, -179.999984, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 10412, "hotel1", "carpet_red_256", 0x00000000);
    jbxtxc = CreateDynamicObject(19447, 1378.432250, 1539.773071, 14.600298, 0.000000, 270.000000, -179.999984, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 10412, "hotel1", "carpet_red_256", 0x00000000);
    jbxtxc = CreateDynamicObject(1827, 1377.722900, 1528.426757, 17.920316, 89.999992, -89.999992, -90.000022, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 1, 2423, "cj_ff_counters", "CJ_Laminate1", 0x00000000);
    jbxtxc = CreateDynamicObject(19479, 1378.058959, 1528.994384, 18.590332, 0.000000, 0.000007, 89.999969, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(jbxtxc, 0, "PENGADILAN", 130, "Arial", 33, 1, 0xFFFFFFFF, 0x00000000, 1);
    jbxtxc = CreateDynamicObject(19479, 1378.058959, 1528.994384, 17.590322, 0.000000, 0.000007, 89.999969, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(jbxtxc, 0, "ATHERLIFE", 130, "Arial", 33, 1, 0xFFFFFFFF, 0x00000000, 1);
    jbxtxc = CreateDynamicObject(19377, 1366.722412, 1608.328613, 18.710317, -0.000007, 0.000000, 0.000022, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(19377, 1379.756835, 1528.727416, 18.710317, 0.000014, 0.000014, 89.999923, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(18886, 1378.156494, 1527.836059, 17.896234, 89.999992, 89.999992, -89.999977, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    jbxtxc = CreateDynamicObject(18886, 1379.769897, 1527.829956, 17.896234, 89.999992, 89.999992, -89.999977, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    jbxtxc = CreateDynamicObject(18886, 1376.550170, 1527.829956, 17.896234, 89.999992, 89.999992, -89.999977, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    jbxtxc = CreateDynamicObject(970, 1376.428588, 1546.829956, 15.230324, 0.000007, 0.000000, 89.999977, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2755, "ab_dojowall", "ab_trellis", 0x00000000);
    jbxtxc = CreateDynamicObject(970, 1380.498657, 1546.829956, 15.230324, 0.000007, 0.000000, 89.999977, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2755, "ab_dojowall", "ab_trellis", 0x00000000);
    jbxtxc = CreateDynamicObject(970, 1376.428588, 1552.791137, 15.230324, 0.000014, 0.000000, 89.999954, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2755, "ab_dojowall", "ab_trellis", 0x00000000);
    jbxtxc = CreateDynamicObject(970, 1380.498657, 1552.791137, 15.230324, 0.000014, 0.000000, 89.999954, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2755, "ab_dojowall", "ab_trellis", 0x00000000);
    jbxtxc = CreateDynamicObject(1532, 1368.647949, 1528.879516, 14.630297, 0.000000, 0.000007, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0x00000000);
    SetDynamicObjectMaterial(jbxtxc, 1, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    SetDynamicObjectMaterial(jbxtxc, 2, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    jbxtxc = CreateDynamicObject(1735, 1378.367553, 1538.741088, 14.636238, 0.000000, 0.000007, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    jbxtxc = CreateDynamicObject(11245, 1381.862060, 1528.927246, 18.182537, 0.000007, -5.700016, 89.999977, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 10810, "ap_build4e", "redwhite_stripe", 0x00000000);
    jbxtxc = CreateDynamicObject(11245, 1374.491943, 1528.927246, 18.182537, 0.000007, -5.700016, 89.999977, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 10810, "ap_build4e", "redwhite_stripe", 0x00000000);
    jbxtxc = CreateDynamicObject(8661, 1380.835693, 1547.651000, 14.680006, 0.000000, 0.000014, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14387, "dr_gsnew", "la_flair1", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1387.586303, 1544.631103, 13.840307, -0.000014, 179.999984, -90.000053, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14760, "sfhosemed2", "ah_pnwainscot3", 0xFF706353);
    jbxtxc = CreateDynamicObject(19373, 1368.976196, 1544.631103, 13.840307, -0.000014, 179.999984, -90.000053, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14760, "sfhosemed2", "ah_pnwainscot3", 0xFF706353);
    jbxtxc = CreateDynamicObject(19447, 1362.326782, 1563.564453, 19.340316, -89.999992, -134.573593, 135.426116, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19447, 1362.326782, 1560.264160, 19.340316, -89.999992, -134.573593, 135.426116, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19447, 1362.326782, 1591.084960, 19.340316, -89.999992, -154.178115, 115.821571, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19447, 1362.326782, 1587.784667, 19.340316, -89.999992, -154.178115, 115.821571, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19447, 1375.028076, 1589.424438, 19.340316, -89.999992, -89.574386, 90.425300, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19434, 1375.848876, 1588.067260, 20.874431, -48.800006, 179.999801, -90.000221, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19718, 1384.098632, 1589.300781, 19.780323, 0.000045, 0.000051, 89.999832, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5710, "cemetery_law", "brickgrey", 0x00000000);
    jbxtxc = CreateDynamicObject(3261, 1384.172729, 1591.044555, 18.430309, 0.000051, -0.000045, 179.999542, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5710, "cemetery_law", "brickgrey", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1386.049438, 1588.074707, 22.212623, -34.799991, 179.999801, -90.000267, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1384.473754, 1588.067260, 22.628992, -14.800013, 179.999801, -90.000267, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1382.671020, 1588.050292, 22.464942, 5.199973, 179.999801, -90.000259, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1381.486450, 1588.064697, 21.741638, 25.199970, 179.999801, -90.000213, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(11245, 1383.557861, 1572.204956, 21.548496, 0.000000, -5.700023, 179.999954, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 10810, "ap_build4e", "redwhite_stripe", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1381.571044, 1588.067260, 21.671226, 50.199951, 179.999801, -90.000205, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19434, 1380.886230, 1590.997680, 20.874431, -48.799900, 179.999908, 89.999534, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(11245, 1383.557861, 1578.025390, 21.548496, 0.000000, -5.700023, 179.999954, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 10810, "ap_build4e", "redwhite_stripe", 0x00000000);
    jbxtxc = CreateDynamicObject(11245, 1383.557861, 1584.395629, 21.548496, 0.000000, -5.700023, 179.999954, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 10810, "ap_build4e", "redwhite_stripe", 0x00000000);
    jbxtxc = CreateDynamicObject(11245, 1383.557861, 1566.275390, 21.548496, 0.000000, -5.700023, 179.999954, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 10810, "ap_build4e", "redwhite_stripe", 0x00000000);
    jbxtxc = CreateDynamicObject(14410, 1377.399780, 1587.562255, 12.790323, 0.000000, 0.000000, 179.999847, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14760, "sfhosemed2", "AH_walltile2", 0x00000000);
    SetDynamicObjectMaterial(jbxtxc, 1, 14760, "sfhosemed2", "AH_walltile2", 0xFFBBBBBB);
    jbxtxc = CreateDynamicObject(14410, 1379.590454, 1587.574340, 12.790003, 0.000000, 0.000000, 179.999847, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14760, "sfhosemed2", "AH_walltile2", 0x00000000);
    SetDynamicObjectMaterial(jbxtxc, 1, 14760, "sfhosemed2", "AH_walltile2", 0xFFBBBBBB);
    jbxtxc = CreateDynamicObject(19373, 1381.622314, 1586.080078, 14.220295, 0.000000, -0.000007, 179.999847, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14760, "sfhosemed2", "ah_pnwainscot3", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1375.242431, 1586.080078, 14.220295, 0.000000, -0.000007, 179.999847, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14760, "sfhosemed2", "ah_pnwainscot3", 0x00000000);
    jbxtxc = CreateDynamicObject(8661, 1380.835693, 1595.168823, 14.670305, 0.000000, 0.000014, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14754, "sfhsb3", "ah_wpaper10", 0x00000000);
    jbxtxc = CreateDynamicObject(19377, 1385.292114, 1587.991210, 18.710317, 0.000000, -0.000007, 179.999954, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19377, 1370.611816, 1585.571166, 10.880318, -0.000007, 0.000000, -89.999977, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14760, "sfhosemed2", "ah_pnwainscot3", 0x00000000);
    jbxtxc = CreateDynamicObject(19377, 1360.981933, 1585.571166, 10.880318, -0.000007, 0.000000, -89.999977, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14760, "sfhosemed2", "ah_pnwainscot3", 0x00000000);
    jbxtxc = CreateDynamicObject(19377, 1359.642700, 1585.251708, 21.280315, -0.000007, 0.000000, -89.999977, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(19377, 1361.142089, 1589.181762, 18.710317, 0.000000, -0.000007, 179.999954, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(19377, 1385.292114, 1593.492065, 18.710317, -0.000014, 0.000000, -89.999954, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(19377, 1371.712158, 1593.492065, 18.710317, -0.000014, 0.000000, -89.999954, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(19377, 1377.750610, 1593.510009, 22.430324, -0.000014, 0.000000, -89.999954, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(19377, 1362.082519, 1593.492065, 18.710317, -0.000014, 0.000000, -89.999954, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(19857, 1379.943237, 1593.458129, 14.930292, 0.000000, 0.000067, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    SetDynamicObjectMaterial(jbxtxc, 1, 5401, "jeffers4_lae", "stainwinLAe", 0x00000000);
    jbxtxc = CreateDynamicObject(19857, 1381.453735, 1593.458129, 17.410306, 0.000000, 180.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    SetDynamicObjectMaterial(jbxtxc, 1, 5401, "jeffers4_lae", "stainwinLAe", 0x00000000);
    jbxtxc = CreateDynamicObject(19857, 1375.471679, 1593.450805, 14.930292, 0.000000, 0.000090, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    SetDynamicObjectMaterial(jbxtxc, 1, 5401, "jeffers4_lae", "stainwinLAe", 0x00000000);
    jbxtxc = CreateDynamicObject(19857, 1376.962524, 1593.450805, 17.410306, 0.000000, 180.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    SetDynamicObjectMaterial(jbxtxc, 1, 5401, "jeffers4_lae", "stainwinLAe", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1382.335083, 1590.990234, 22.212623, -34.799903, 179.999908, 89.999519, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1383.910766, 1590.997680, 22.628992, -14.799919, 179.999908, 89.999511, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1385.713500, 1591.014648, 22.464942, 5.200066, 179.999908, 89.999496, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19718, 1372.449218, 1589.300781, 19.780323, 0.000059, 0.000051, 89.999786, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5710, "cemetery_law", "brickgrey", 0x00000000);
    jbxtxc = CreateDynamicObject(3261, 1372.523315, 1591.044555, 18.430309, 0.000051, -0.000059, 179.999450, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5710, "cemetery_law", "brickgrey", 0x00000000);
    jbxtxc = CreateDynamicObject(19447, 1381.787109, 1589.424438, 19.340316, -89.999992, -88.298881, 91.700851, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19447, 1380.857543, 1589.424438, 19.340316, -89.999992, -88.298881, 91.700851, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19434, 1381.678344, 1588.067260, 20.874431, -48.799995, 179.999801, -90.000267, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19718, 1378.278686, 1589.300781, 19.780323, 0.000045, 0.000051, 89.999832, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5710, "cemetery_law", "brickgrey", 0x00000000);
    jbxtxc = CreateDynamicObject(3261, 1378.352783, 1591.044555, 18.430309, 0.000051, -0.000045, 179.999542, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5710, "cemetery_law", "brickgrey", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1380.229492, 1588.074707, 22.212623, -34.799991, 179.999801, -90.000267, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1378.653808, 1588.067260, 22.628992, -14.800013, 179.999801, -90.000267, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1376.851074, 1588.050292, 22.464942, 5.199973, 179.999801, -90.000259, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1375.666503, 1588.064697, 21.741638, 25.199970, 179.999801, -90.000213, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1383.155639, 1585.502319, 14.330299, 0.000007, 0.000000, 89.999977, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1386.316040, 1585.502319, 14.330299, 0.000007, 0.000000, 89.999977, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19447, 1378.432250, 1592.042846, 14.600298, 0.000000, 270.000000, -179.999984, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 10412, "hotel1", "carpet_red_256", 0x00000000);
    jbxtxc = CreateDynamicObject(19447, 1378.432250, 1601.651733, 14.600298, 0.000000, -89.999984, -0.000029, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 10412, "hotel1", "carpet_red_256", 0x00000000);
    jbxtxc = CreateDynamicObject(1499, 1382.233154, 1594.052978, 14.678438, 0.000000, 0.000000, 449.999938, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14581, "ab_mafiasuitea", "cof_wood2", 0x00000000);
    SetDynamicObjectMaterial(jbxtxc, 1, 14581, "ab_mafiasuitea", "wood02S", 0x00000000);
    jbxtxc = CreateDynamicObject(19377, 1382.211425, 1611.800048, 18.710317, 0.000000, -0.000007, 179.999954, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(19447, 1378.432250, 1611.292236, 14.600298, 0.000000, -89.999984, -0.000029, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 10412, "hotel1", "carpet_red_256", 0x00000000);
    jbxtxc = CreateDynamicObject(8661, 1380.835693, 1615.157958, 14.670305, 0.000000, 0.000014, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14754, "sfhsb3", "ah_wpaper10", 0x00000000);
    jbxtxc = CreateDynamicObject(19377, 1377.651123, 1612.250488, 18.710317, -0.000007, 0.000000, -89.999977, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(19377, 1374.691650, 1607.850463, 18.710317, 0.000000, -0.000007, 179.999954, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(19482, 1374.678222, 1597.134765, 17.320323, 0.000000, -0.000007, 179.999923, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x70333333);
    jbxtxc = CreateDynamicObject(11729, 1374.722534, 1603.270507, 13.726236, 0.000000, 360.000000, -179.999893, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    jbxtxc = CreateDynamicObject(11729, 1374.722534, 1603.270507, 15.586233, 0.000000, 360.000000, -179.999893, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    jbxtxc = CreateDynamicObject(11729, 1374.719970, 1603.270507, 17.376230, -89.999992, 270.000000, 89.999992, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    jbxtxc = CreateDynamicObject(11729, 1374.722534, 1601.439941, 13.726236, 0.000000, 360.000000, -179.999847, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    jbxtxc = CreateDynamicObject(11729, 1374.722534, 1601.439941, 15.586233, 0.000000, 360.000000, -179.999847, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    jbxtxc = CreateDynamicObject(11729, 1374.722534, 1600.159912, 14.516237, 0.000000, 360.000000, -179.999755, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    jbxtxc = CreateDynamicObject(11729, 1374.722534, 1600.159912, 16.376234, 0.000000, 360.000000, -179.999755, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    jbxtxc = CreateDynamicObject(11729, 1374.722534, 1600.089843, 18.176240, -89.999992, 283.368469, 103.368492, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    jbxtxc = CreateDynamicObject(11729, 1374.722534, 1598.229736, 18.176240, -89.999992, 283.368469, 103.368492, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    jbxtxc = CreateDynamicObject(11729, 1374.722534, 1596.400146, 18.176240, -89.999992, 276.773986, 96.773986, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    jbxtxc = CreateDynamicObject(11729, 1374.722534, 1594.579833, 14.516237, 0.000000, 360.000000, -179.999710, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    jbxtxc = CreateDynamicObject(11729, 1374.719970, 1594.579833, 16.376234, 0.000000, 360.000000, -179.999710, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    jbxtxc = CreateDynamicObject(19929, 1374.721313, 1595.754272, 14.650303, 0.000000, -0.000029, 179.999664, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 1, 14760, "sfhosemed2", "ah_pnwainscot3", 0xFF706353);
    jbxtxc = CreateDynamicObject(19929, 1374.721313, 1598.995361, 14.650303, 0.000000, -0.000029, 179.999664, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 1, 14760, "sfhosemed2", "ah_pnwainscot3", 0xFF706353);
    jbxtxc = CreateDynamicObject(19929, 1374.739990, 1597.425048, 14.650303, 0.000000, -0.000029, 179.999664, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 1, 14760, "sfhosemed2", "ah_pnwainscot3", 0xFF706353);
    jbxtxc = CreateDynamicObject(19377, 1374.691650, 1598.221069, 23.540313, 0.000000, -0.000007, 179.999954, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(8661, 1374.075805, 1613.480468, 19.370006, 0.000000, 179.999984, -90.000007, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14748, "sfhsm1", "ah_pnwainscot6", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1388.552978, 1566.590942, 22.661628, 25.200014, 179.999908, -0.000334, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19372, 1378.477050, 1612.170043, 15.846235, 0.000007, 0.000000, 89.999977, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02b_law", 0x00000000);
    jbxtxc = CreateDynamicObject(18066, 1374.690307, 1595.493530, 16.140300, 0.000007, 180.000000, 89.999977, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0x00000000);
    SetDynamicObjectMaterial(jbxtxc, 1, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    jbxtxc = CreateDynamicObject(18066, 1374.690307, 1596.912841, 16.140300, 0.000007, 180.000000, 89.999977, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0x00000000);
    SetDynamicObjectMaterial(jbxtxc, 1, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    jbxtxc = CreateDynamicObject(18066, 1374.690307, 1598.302734, 16.140300, 0.000007, 180.000000, 89.999977, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    SetDynamicObjectMaterial(jbxtxc, 1, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    jbxtxc = CreateDynamicObject(18066, 1374.690307, 1599.702636, 16.140300, 0.000007, 180.000000, 89.999977, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0x00000000);
    SetDynamicObjectMaterial(jbxtxc, 1, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    jbxtxc = CreateDynamicObject(19434, 1374.691650, 1600.891113, 16.550325, 0.000000, -0.000007, 179.999954, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(19434, 1374.691650, 1600.891113, 13.060331, 0.000000, -0.000007, 179.999954, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(19434, 1374.689941, 1602.481689, 18.880327, 0.000000, -0.000007, 179.999954, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(19434, 1374.691650, 1594.210815, 16.550325, 0.000000, -0.000022, 179.999862, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(19434, 1374.691650, 1594.210815, 13.060331, 0.000000, -0.000022, 179.999862, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(11729, 1374.719970, 1595.079711, 16.376234, 0.000000, 360.000000, -179.999710, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    jbxtxc = CreateDynamicObject(11729, 1374.719970, 1595.079711, 14.576231, 0.000000, 360.000000, -179.999710, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    jbxtxc = CreateDynamicObject(19427, 1374.687500, 1597.649780, 17.073135, 90.000000, 0.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 1419, "break_fence3", "CJ_FRAME_Glass", 0x00000000);
    jbxtxc = CreateDynamicObject(19372, 1368.961547, 1595.138916, 16.440315, 0.000000, -0.000007, 179.999954, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(19377, 1366.561401, 1612.239746, 18.710317, -0.000007, 0.000000, -89.999977, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(19372, 1368.961547, 1598.328857, 16.440315, 0.000000, -0.000007, 179.999954, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(19372, 1373.182495, 1600.878417, 16.440315, -0.000007, 0.000000, -89.999977, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(19372, 1368.382690, 1600.878417, 16.440315, -0.000007, 0.000000, -89.999977, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(19372, 1366.742309, 1603.258544, 16.450321, 0.000000, 0.000007, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(19372, 1366.742309, 1600.078735, 16.450321, 0.000000, 0.000007, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(19372, 1368.942504, 1599.188354, 16.450321, 0.000000, 0.000007, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(8661, 1364.385742, 1613.480468, 18.189989, 0.000000, 179.999984, -90.000007, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14748, "sfhsm1", "ah_pnwainscot6", 0x00000000);
    jbxtxc = CreateDynamicObject(19447, 1364.367919, 1575.523437, 17.670318, 0.000000, 0.000007, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 6060, "shops2_law", "biffoffwin_law", 0x00000000);
    jbxtxc = CreateDynamicObject(19447, 1362.797851, 1575.523437, 19.490320, 0.000000, 90.000007, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    jbxtxc = CreateDynamicObject(19447, 1362.797851, 1570.634033, 14.770318, 89.999992, 360.000000, -89.999992, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    jbxtxc = CreateDynamicObject(19447, 1362.797851, 1580.414062, 14.770318, 89.999992, 360.000000, -89.999992, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    jbxtxc = CreateDynamicObject(19434, 1377.637939, 1560.910522, 15.856234, 0.000000, 0.000007, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    jbxtxc = CreateDynamicObject(19434, 1379.279052, 1560.910522, 15.856234, 0.000000, 0.000007, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    jbxtxc = CreateDynamicObject(962, 1378.622558, 1561.174194, 17.250310, 0.000000, 0.000000, -89.999977, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(jbxtxc, 1, 14577, "casinovault01", "vaultWall", 0x00000000);
    SetDynamicObjectMaterial(jbxtxc, 2, 16640, "a51", "a51_panels1", 0x00000000);
    jbxtxc = CreateDynamicObject(962, 1378.622558, 1560.513549, 17.240310, 0.000000, 0.000000, -89.999977, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(jbxtxc, 1, 14577, "casinovault01", "vaultWall", 0x00000000);
    SetDynamicObjectMaterial(jbxtxc, 2, 16640, "a51", "a51_panels1", 0x00000000);
    jbxtxc = CreateDynamicObject(11748, 1379.263305, 1561.712890, 16.286241, 0.000007, 90.000000, 89.999977, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 10765, "airportgnd_sfse", "white", 0xFF00FF00);
    jbxtxc = CreateDynamicObject(11748, 1379.263305, 1561.712890, 15.996235, 0.000007, 90.000000, 89.999977, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 10765, "airportgnd_sfse", "white", 0xFFFF0000);
    jbxtxc = CreateDynamicObject(638, 1383.584960, 1564.931762, 15.680317, 0.000007, 0.000000, 89.999977, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 1, 802, "gta_proc_bushland", "veg_bush3", 0x00000000);
    jbxtxc = CreateDynamicObject(18764, 1384.144165, 1585.168212, 12.830307, 0.000000, 0.000014, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    jbxtxc = CreateDynamicObject(638, 1383.584960, 1586.512695, 15.680317, 0.000014, 0.000000, 89.999954, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 1, 802, "gta_proc_bushland", "veg_bush3", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1388.550415, 1566.675537, 22.591217, 50.200016, 179.999908, -0.000356, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19447, 1386.977172, 1571.701904, 20.260307, -89.999992, 0.000086, 89.999961, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(18980, 1388.283447, 1578.021118, 16.000310, 0.000000, 180.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14623, "mafcasmain", "ab_tileStar", 0xFFBBBBBB);
    jbxtxc = CreateDynamicObject(18980, 1385.713378, 1578.021118, 16.000310, 0.000000, 180.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14623, "mafcasmain", "ab_tileStar", 0xFFBBBBBB);
    jbxtxc = CreateDynamicObject(18980, 1388.283447, 1572.231445, 16.000310, 0.000000, 180.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14623, "mafcasmain", "ab_tileStar", 0xFFBBBBBB);
    jbxtxc = CreateDynamicObject(18980, 1385.713378, 1572.231445, 16.000310, 0.000000, 180.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14623, "mafcasmain", "ab_tileStar", 0xFFBBBBBB);
    jbxtxc = CreateDynamicObject(19447, 1375.028076, 1561.903930, 19.340316, -89.999992, -89.149116, 90.850593, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19434, 1375.848876, 1560.546752, 20.874431, -48.799999, 179.999801, -90.000244, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19718, 1384.098632, 1561.780273, 19.780323, 0.000037, 0.000051, 89.999855, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5710, "cemetery_law", "brickgrey", 0x00000000);
    jbxtxc = CreateDynamicObject(3261, 1384.172729, 1563.524047, 18.430309, 0.000051, -0.000037, 179.999588, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5710, "cemetery_law", "brickgrey", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1386.049438, 1560.554199, 22.212623, -34.799983, 179.999801, -90.000289, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1384.473754, 1560.546752, 22.628992, -14.800006, 179.999801, -90.000289, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1382.671020, 1560.529785, 22.464942, 5.199979, 179.999801, -90.000282, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1381.486450, 1560.544189, 21.741638, 25.199977, 179.999801, -90.000236, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1381.571044, 1560.546752, 21.671226, 50.199962, 179.999801, -90.000228, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19434, 1380.886230, 1563.477172, 20.874431, -48.799911, 179.999908, 89.999557, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1382.335083, 1563.469726, 22.212623, -34.799911, 179.999908, 89.999542, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1383.910766, 1563.477172, 22.628992, -14.799926, 179.999908, 89.999534, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1385.713500, 1563.494140, 22.464942, 5.200057, 179.999908, 89.999519, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19718, 1372.449218, 1561.780273, 19.780323, 0.000051, 0.000051, 89.999809, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5710, "cemetery_law", "brickgrey", 0x00000000);
    jbxtxc = CreateDynamicObject(3261, 1372.523315, 1563.524047, 18.430309, 0.000051, -0.000051, 179.999496, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5710, "cemetery_law", "brickgrey", 0x00000000);
    jbxtxc = CreateDynamicObject(19447, 1381.787109, 1561.903930, 19.340316, -89.999992, -86.600997, 93.398750, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19447, 1380.857543, 1561.903930, 19.340316, -89.999992, -86.600997, 93.398750, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19434, 1381.678344, 1560.546752, 20.874431, -48.799991, 179.999801, -90.000289, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19718, 1378.278686, 1561.780273, 19.780323, 0.000037, 0.000051, 89.999855, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5710, "cemetery_law", "brickgrey", 0x00000000);
    jbxtxc = CreateDynamicObject(3261, 1378.352783, 1563.524047, 18.430309, 0.000051, -0.000037, 179.999588, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5710, "cemetery_law", "brickgrey", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1380.229492, 1560.554199, 22.212623, -34.799983, 179.999801, -90.000289, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1378.653808, 1560.546752, 22.628992, -14.800006, 179.999801, -90.000289, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1376.851074, 1560.529785, 22.464942, 5.199979, 179.999801, -90.000282, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1375.666503, 1560.544189, 21.741638, 25.199977, 179.999801, -90.000236, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1375.751098, 1560.546752, 21.671226, 50.199962, 179.999801, -90.000228, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19434, 1375.066284, 1563.477172, 20.874431, -48.799911, 179.999908, 89.999557, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1376.515136, 1563.469726, 22.212623, -34.799911, 179.999908, 89.999542, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1378.090820, 1563.477172, 22.628992, -14.799926, 179.999908, 89.999534, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1379.893554, 1563.494140, 22.464942, 5.200057, 179.999908, 89.999519, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1381.078125, 1563.479736, 21.741638, 25.200042, 179.999908, 89.999526, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1380.993530, 1563.477172, 21.671226, 50.200042, 179.999908, 89.999504, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19447, 1375.967163, 1561.903930, 19.340316, -89.999992, -86.600997, 93.398750, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1374.400024, 1560.554199, 22.212623, -34.799999, 179.999801, -90.000244, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1372.824340, 1560.546752, 22.628992, -14.800020, 179.999801, -90.000244, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1371.021606, 1560.529785, 22.464942, 5.199965, 179.999801, -90.000236, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1369.837036, 1560.544189, 21.741638, 25.199960, 179.999801, -90.000190, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1369.921630, 1560.546752, 21.671226, 50.199939, 179.999801, -90.000183, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19434, 1369.236816, 1563.477172, 20.874431, -48.799892, 179.999908, 89.999511, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1370.685668, 1563.469726, 22.212623, -34.799896, 179.999908, 89.999496, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1372.261352, 1563.477172, 22.628992, -14.799909, 179.999908, 89.999488, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1374.064086, 1563.494140, 22.464942, 5.200072, 179.999908, 89.999473, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1375.248657, 1563.479736, 21.741638, 25.200054, 179.999908, 89.999481, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1375.164062, 1563.477172, 21.671226, 50.200054, 179.999908, 89.999458, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19447, 1370.137695, 1561.903930, 19.340316, -89.999992, -89.149116, 90.850593, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19447, 1369.047363, 1561.903930, 19.340316, -89.999992, -89.787033, 90.212631, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19434, 1369.868164, 1560.546752, 20.874431, -48.800010, 179.999801, -90.000198, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19718, 1366.468505, 1561.780273, 19.780323, 0.000068, 0.000051, 89.999763, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5710, "cemetery_law", "brickgrey", 0x00000000);
    jbxtxc = CreateDynamicObject(3261, 1366.542602, 1563.524047, 18.430309, 0.000051, -0.000068, 179.999404, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5710, "cemetery_law", "brickgrey", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1368.419311, 1560.554199, 22.212623, -34.800006, 179.999801, -90.000198, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1366.843627, 1560.546752, 22.628992, -14.800032, 179.999801, -90.000198, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1365.040893, 1560.529785, 22.464942, 5.199950, 179.999801, -90.000190, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1363.856323, 1560.544189, 21.741638, 25.199941, 179.999801, -90.000144, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1363.940917, 1560.546752, 21.671226, 50.199920, 179.999801, -90.000137, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19434, 1363.256103, 1563.477172, 20.874431, -48.799869, 179.999908, 89.999465, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1364.704956, 1563.469726, 22.212623, -34.799873, 179.999908, 89.999450, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1366.280639, 1563.477172, 22.628992, -14.799892, 179.999908, 89.999443, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1368.083374, 1563.494140, 22.464942, 5.200088, 179.999908, 89.999427, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1369.267944, 1563.479736, 21.741638, 25.200065, 179.999908, 89.999435, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1369.183349, 1563.467163, 21.671226, 50.200061, 179.999908, 89.999412, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19447, 1364.156982, 1561.903930, 19.340316, -89.999992, -89.787033, 90.212631, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(18980, 1381.293212, 1563.170654, 16.000310, 0.000000, 180.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14623, "mafcasmain", "ab_tileStar", 0xFFBBBBBB);
    jbxtxc = CreateDynamicObject(18980, 1381.293212, 1560.650756, 16.000310, 0.000000, 180.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14623, "mafcasmain", "ab_tileStar", 0xFFBBBBBB);
    jbxtxc = CreateDynamicObject(18980, 1375.522338, 1563.170654, 16.000310, 0.000000, 180.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14623, "mafcasmain", "ab_tileStar", 0xFFBBBBBB);
    jbxtxc = CreateDynamicObject(18980, 1375.522338, 1560.650756, 16.000310, 0.000000, 180.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14623, "mafcasmain", "ab_tileStar", 0xFFBBBBBB);
    jbxtxc = CreateDynamicObject(18980, 1369.562500, 1563.170654, 16.000310, 0.000000, 180.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14623, "mafcasmain", "ab_tileStar", 0xFFBBBBBB);
    jbxtxc = CreateDynamicObject(18980, 1369.562500, 1560.650756, 16.000310, 0.000000, 180.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14623, "mafcasmain", "ab_tileStar", 0xFFBBBBBB);
    jbxtxc = CreateDynamicObject(19373, 1375.751098, 1588.067260, 21.671226, 50.199951, 179.999801, -90.000205, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19434, 1375.066284, 1590.997680, 20.874431, -48.799900, 179.999908, 89.999534, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1376.515136, 1590.990234, 22.212623, -34.799903, 179.999908, 89.999519, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1378.090820, 1590.997680, 22.628992, -14.799919, 179.999908, 89.999511, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1379.893554, 1591.014648, 22.464942, 5.200066, 179.999908, 89.999496, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1381.078125, 1591.000244, 21.741638, 25.200048, 179.999908, 89.999504, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1380.993530, 1590.997680, 21.671226, 50.200046, 179.999908, 89.999481, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19447, 1375.967163, 1589.424438, 19.340316, -89.999992, -88.298881, 91.700851, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1374.400024, 1588.074707, 22.212623, -34.800003, 179.999801, -90.000221, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1372.824340, 1588.067260, 22.628992, -14.800027, 179.999801, -90.000221, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1371.021606, 1588.050292, 22.464942, 5.199956, 179.999801, -90.000213, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1369.837036, 1588.064697, 21.741638, 25.199951, 179.999801, -90.000167, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1369.921630, 1588.067260, 21.671226, 50.199928, 179.999801, -90.000160, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19434, 1369.236816, 1590.997680, 20.874431, -48.799880, 179.999908, 89.999488, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1370.685668, 1590.990234, 22.212623, -34.799884, 179.999908, 89.999473, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1372.261352, 1590.997680, 22.628992, -14.799901, 179.999908, 89.999465, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1374.064086, 1591.014648, 22.464942, 5.200079, 179.999908, 89.999450, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1375.248657, 1591.000244, 21.741638, 25.200059, 179.999908, 89.999458, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1375.164062, 1590.997680, 21.671226, 50.200057, 179.999908, 89.999435, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19447, 1370.137695, 1589.424438, 19.340316, -89.999992, -89.574386, 90.425300, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19447, 1369.047363, 1589.424438, 19.340316, -89.999992, -89.893348, 90.106292, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19434, 1369.868164, 1588.067260, 20.874431, -48.800014, 179.999801, -90.000175, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19718, 1366.468505, 1589.300781, 19.780323, 0.000075, 0.000051, 89.999740, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5710, "cemetery_law", "brickgrey", 0x00000000);
    jbxtxc = CreateDynamicObject(3261, 1366.542602, 1591.044555, 18.430309, 0.000051, -0.000075, 179.999359, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5710, "cemetery_law", "brickgrey", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1368.419311, 1588.074707, 22.212623, -34.800010, 179.999801, -90.000175, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1366.843627, 1588.067260, 22.628992, -14.800043, 179.999801, -90.000175, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1365.040893, 1588.050292, 22.464942, 5.199942, 179.999801, -90.000167, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1363.856323, 1588.064697, 21.741638, 25.199932, 179.999801, -90.000122, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1363.940917, 1588.067260, 21.671226, 50.199909, 179.999801, -90.000114, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19434, 1363.256103, 1590.997680, 20.874431, -48.799861, 179.999908, 89.999443, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1364.704956, 1590.990234, 22.212623, -34.799865, 179.999908, 89.999427, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1366.280639, 1590.997680, 22.628992, -14.799882, 179.999908, 89.999420, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1368.083374, 1591.014648, 22.464942, 5.200095, 179.999908, 89.999404, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1369.267944, 1591.000244, 21.741638, 25.200071, 179.999908, 89.999412, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1369.183349, 1590.997680, 21.671226, 50.200069, 179.999908, 89.999389, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(19447, 1364.156982, 1589.424438, 19.340316, -89.999992, -89.893348, 90.106292, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18234, "cuntwbtxcs_t", "offwhitebrix", 0x00000000);
    jbxtxc = CreateDynamicObject(18980, 1381.293212, 1590.691162, 16.000310, 0.000000, 180.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14623, "mafcasmain", "ab_tileStar", 0xFFBBBBBB);
    jbxtxc = CreateDynamicObject(18980, 1381.293212, 1588.171264, 16.000310, 0.000000, 180.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14623, "mafcasmain", "ab_tileStar", 0xFFBBBBBB);
    jbxtxc = CreateDynamicObject(18980, 1375.522338, 1590.691162, 16.000310, 0.000000, 180.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14623, "mafcasmain", "ab_tileStar", 0xFFBBBBBB);
    jbxtxc = CreateDynamicObject(18980, 1375.522338, 1588.171264, 16.000310, 0.000000, 180.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14623, "mafcasmain", "ab_tileStar", 0xFFBBBBBB);
    jbxtxc = CreateDynamicObject(18980, 1369.562500, 1590.691162, 16.000310, 0.000000, 180.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14623, "mafcasmain", "ab_tileStar", 0xFFBBBBBB);
    jbxtxc = CreateDynamicObject(18980, 1369.562500, 1588.171264, 16.000310, 0.000000, 180.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14623, "mafcasmain", "ab_tileStar", 0xFFBBBBBB);
    jbxtxc = CreateDynamicObject(19377, 1385.302124, 1597.601196, 18.710317, 0.000000, -0.000007, 179.999954, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(19373, 1386.292236, 1565.671508, 14.340319, -0.000007, 0.000000, -89.999977, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14760, "sfhosemed2", "ah_pnwainscot3", 0x00000000);
    jbxtxc = CreateDynamicObject(18763, 1365.458007, 1582.762207, 13.700317, 0.000000, 0.000007, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 4829, "airport_las", "Grass_128HV", 0x00000000);
    jbxtxc = CreateDynamicObject(18763, 1365.788330, 1582.221679, 13.700003, 0.000000, 0.000007, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    jbxtxc = CreateDynamicObject(18763, 1365.458007, 1568.491455, 13.700317, 0.000000, 0.000014, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 4829, "airport_las", "Grass_128HV", 0x00000000);
    jbxtxc = CreateDynamicObject(18763, 1365.797729, 1583.201904, 13.700003, 0.000000, 0.000007, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    jbxtxc = CreateDynamicObject(18763, 1365.788330, 1567.950927, 13.700003, 0.000000, 0.000014, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    jbxtxc = CreateDynamicObject(18763, 1365.797729, 1568.931152, 13.700003, 0.000000, 0.000014, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    jbxtxc = CreateDynamicObject(11245, 1366.978271, 1583.275634, 21.875976, 0.000000, -5.700007, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 10810, "ap_build4e", "redwhite_stripe", 0x00000000);
    jbxtxc = CreateDynamicObject(11245, 1366.978271, 1567.465820, 21.875976, 0.000000, -5.700007, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 10810, "ap_build4e", "redwhite_stripe", 0x00000000);
    jbxtxc = CreateDynamicObject(19479, 1364.488159, 1575.694335, 21.090316, 0.000000, 0.000007, -0.000029, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(jbxtxc, 0, "PEMERINTAH ATHERLIFE", 130, "Quartz MS", 42, 1, 0xFFFFFFFF, 0x00000000, 1);
    jbxtxc = CreateDynamicObject(19434, 1364.398071, 1571.083984, 21.090316, 89.999992, 89.999992, -90.000022, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    jbxtxc = CreateDynamicObject(19434, 1364.398071, 1574.573730, 21.090316, 89.999992, 89.999992, -90.000022, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    jbxtxc = CreateDynamicObject(19434, 1364.398071, 1577.043579, 21.090316, 89.999992, 89.999984, -89.999977, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    jbxtxc = CreateDynamicObject(19434, 1364.398071, 1580.533325, 21.090316, 89.999992, 89.999984, -89.999977, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    jbxtxc = CreateDynamicObject(19434, 1364.368041, 1575.593994, 22.280334, 89.999992, 89.999992, -89.999969, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 10765, "airportgnd_sfse", "white", 0xFFFF0000);
    jbxtxc = CreateDynamicObject(19434, 1364.380371, 1575.593994, 21.610324, 89.999992, 89.999992, -89.999969, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    jbxtxc = CreateDynamicObject(1499, 1376.947753, 1557.583129, 14.678438, 0.000000, 0.000014, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14581, "ab_mafiasuitea", "cof_wood2", 0x00000000);
    SetDynamicObjectMaterial(jbxtxc, 1, 14581, "ab_mafiasuitea", "wood02S", 0x00000000);
    jbxtxc = CreateDynamicObject(19377, 1382.211425, 1600.700317, 18.710317, 0.000000, -0.000007, 179.999954, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(19377, 1382.231445, 1600.430419, 18.710317, 0.000000, -0.000007, 179.999954, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(19353, 1383.760986, 1598.245239, 16.420719, 0.000000, 0.000000, 90.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 10041, "archybuild10", "whitedecosfe4", 0x00000000);
    jbxtxc = CreateDynamicObject(2259, 1384.732788, 1595.452514, 16.252834, 0.000000, 0.000000, 270.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    jbxtxc = CreateDynamicObject(19377, 1388.703491, 1607.221435, 18.710317, 0.000000, -0.000007, 179.999954, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(2205, 1385.848999, 1605.573120, 14.657891, 0.000000, 0.000000, 90.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 15034, "genhotelsave", "walp57S", 0x00000000);
    jbxtxc = CreateDynamicObject(19377, 1387.073730, 1612.001708, 18.710317, 0.000000, -0.000007, 269.999938, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(2314, 1386.916625, 1611.493652, 14.657938, 0.000000, 0.000000, 180.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14581, "ab_mafiasuitea", "ab_wood01", 0x00000000);
    jbxtxc = CreateDynamicObject(19377, 1387.073730, 1602.389892, 18.710317, 0.000000, -0.000007, 269.999938, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(11729, 1382.232666, 1605.260742, 15.786235, 0.000000, 360.000000, -179.999847, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    jbxtxc = CreateDynamicObject(11729, 1382.232666, 1605.260742, 13.966237, 0.000000, 360.000000, -179.999847, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    jbxtxc = CreateDynamicObject(11729, 1382.232666, 1607.220336, 15.796235, 0.000000, 360.000000, -179.999801, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    jbxtxc = CreateDynamicObject(11729, 1382.232666, 1607.220336, 13.976235, 0.000000, 360.000000, -179.999801, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    jbxtxc = CreateDynamicObject(11729, 1382.232666, 1607.220336, 17.516237, 0.000000, 450.000000, -89.999847, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    jbxtxc = CreateDynamicObject(2196, 1385.653076, 1605.811889, 15.596732, 0.000000, 0.000000, 32.100006, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    jbxtxc = CreateDynamicObject(8661, 1381.855468, 1613.480468, 19.380006, 0.000000, 179.999984, -90.000007, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14748, "sfhsm1", "ah_pnwainscot6", 0x00000000);
    jbxtxc = CreateDynamicObject(11729, 1382.232666, 1593.830566, 15.786235, 0.000000, 360.000000, -179.999801, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    jbxtxc = CreateDynamicObject(11729, 1382.232666, 1593.830566, 13.966237, 0.000000, 360.000000, -179.999801, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    jbxtxc = CreateDynamicObject(11729, 1382.232666, 1595.790161, 15.796235, 0.000000, 360.000000, -179.999755, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    jbxtxc = CreateDynamicObject(11729, 1382.232666, 1595.790161, 13.976235, 0.000000, 360.000000, -179.999755, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    jbxtxc = CreateDynamicObject(11729, 1382.232666, 1595.790161, 17.516237, -0.000007, 450.000000, -89.999824, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    jbxtxc = CreateDynamicObject(19377, 1382.241455, 1598.310424, 23.050308, 0.000000, -0.000007, 179.999954, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(19377, 1382.241455, 1610.150878, 23.050308, 0.000000, -0.000007, 179.999954, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(1499, 1372.852783, 1612.253295, 14.648243, 0.000000, 0.000000, 180.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    SetDynamicObjectMaterial(jbxtxc, 1, 14385, "trailerkb", "tr_wood1", 0x00000000);
    jbxtxc = CreateDynamicObject(8661, 1344.345336, 1575.578979, 16.010313, 0.000000, 0.000014, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14754, "sfhsb3", "ah_wpaper10", 0x00000000);
    jbxtxc = CreateDynamicObject(14867, 1370.266845, 1616.284790, 16.176389, 0.000000, 0.000000, 270.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14789, "ab_sfgymmain", "ab_wood02", 0x00000000);
    jbxtxc = CreateDynamicObject(2307, 1372.556030, 1613.848144, 14.655263, -0.000007, 0.000000, -89.999977, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 19598, "sfbuilding1", "darkwood1", 0x00000000);
    jbxtxc = CreateDynamicObject(19377, 1374.131347, 1617.090209, 18.710317, 0.000000, -0.000007, 179.999954, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(19377, 1369.201293, 1618.100097, 18.710317, 0.000000, -0.000007, 269.999938, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(19377, 1374.273193, 1612.210449, 22.410333, -0.000007, 0.000000, -89.999977, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(3034, 1372.708496, 1617.997680, 16.445661, 0.000000, 90.000007, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18641, "flashlight1", "torch-glass-64x64", 0x00000000);
    jbxtxc = CreateDynamicObject(3034, 1371.928466, 1617.987670, 16.445661, 0.000000, 90.000007, 0.000000, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 18641, "flashlight1", "torch-glass-64x64", 0x00000000);
    jbxtxc = CreateDynamicObject(19377, 1369.720581, 1617.090209, 18.710317, 0.000000, -0.000007, 179.999954, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(19377, 1381.886718, 1523.847045, 18.710317, 0.000014, 0.000014, 179.999923, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(19377, 1386.756469, 1518.957153, 18.710317, 0.000014, 0.000014, 269.999938, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(19377, 1396.386840, 1518.957153, 18.710317, 0.000014, 0.000014, 269.999938, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(19377, 1399.025634, 1523.856567, 18.710317, 0.000014, 0.000014, 359.999938, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(19377, 1400.516479, 1528.747436, 18.710317, 0.000014, 0.000014, 89.999923, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    jbxtxc = CreateDynamicObject(8661, 1401.737060, 1537.749023, 20.520313, 0.000000, 179.999984, -90.000007, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 14387, "dr_gsnew", "mp_gs_border1", 0x00000000);
    jbxtxc = CreateDynamicObject(19377, 1384.326293, 1528.737426, 22.420320, 0.000014, 0.000014, 89.999923, 99, 3, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(jbxtxc, 0, 5719, "sunrise10_lawn", "holbuild02d_law", 0x00000000);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(14726, 1364.886718, 1565.082763, 14.551321, -0.000044, 0.000000, -89.999855, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(14726, 1368.596679, 1565.082763, 14.551321, -0.000044, 0.000000, -89.999855, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(14726, 1372.346435, 1565.082763, 14.551321, -0.000044, 0.000000, -89.999855, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(14726, 1364.886718, 1583.644775, 14.551321, -0.000051, 0.000000, -89.999832, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(14726, 1368.596679, 1583.644775, 14.551321, -0.000051, 0.000000, -89.999832, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(14726, 1372.346435, 1583.644775, 14.551321, -0.000051, 0.000000, -89.999832, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(19456, 1410.312011, 1577.489990, 19.540311, 89.999992, 150.300140, -60.300243, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(19808, 1378.227905, 1573.247070, 16.880313, 0.000014, 0.000000, 96.399948, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(19808, 1378.240966, 1576.386352, 16.880313, 0.000022, 0.000000, 85.999923, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(19807, 1380.596679, 1578.022583, 16.930320, -0.000000, 0.000007, -14.999999, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(1663, 1379.779541, 1573.435913, 16.430318, -0.000007, 0.000000, -89.999977, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(1663, 1379.779541, 1576.216064, 16.430318, -0.000007, 0.000000, -89.999977, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 1374.896118, 1578.048583, 16.510314, 0.000007, 0.000000, 89.999977, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 1374.896118, 1575.328002, 16.510314, 0.000007, 0.000000, 89.999977, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 1374.896118, 1572.257446, 16.510314, 0.000007, 0.000000, 89.999977, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(19456, 1398.558349, 1593.188476, 19.540311, 89.999992, -90.000000, -89.999992, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(19456, 1395.068115, 1593.188476, 19.540311, 89.999992, -90.000000, -89.999992, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(19456, 1405.418945, 1593.188476, 19.540311, 89.999992, -90.000000, -89.999977, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(19456, 1402.008789, 1593.188476, 19.540311, 89.999992, -90.000000, -89.999977, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(19456, 1402.008789, 1557.593627, 19.540311, 89.999992, 64.448570, -64.448631, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(19456, 1405.418945, 1557.593627, 19.540311, 89.999992, 64.448570, -64.448631, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(19456, 1395.068115, 1557.593627, 19.540311, 89.999992, 64.437263, -64.437301, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(19456, 1398.478271, 1557.593627, 19.540311, 89.999992, 64.437263, -64.437301, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(19456, 1410.312011, 1580.900146, 19.540311, 89.999992, 150.300140, -60.300243, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(19456, 1410.312011, 1570.549316, 19.540311, 89.999992, 150.288238, -60.288307, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(19456, 1410.312011, 1573.959472, 19.540311, 89.999992, 150.288238, -60.288307, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(14726, 1384.707275, 1567.192871, 14.551321, -0.000036, 0.000000, 90.000061, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(14726, 1384.707275, 1585.872802, 14.551321, -0.000028, 0.000000, 90.000038, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(1714, 1381.952148, 1530.643798, 14.680006, 0.000000, -0.000007, 179.999954, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(1714, 1378.171386, 1530.643798, 14.680006, 0.000000, -0.000007, 179.999954, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(1714, 1374.601440, 1530.643798, 14.680006, 0.000000, -0.000007, 179.999954, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 1369.158935, 1548.173706, 15.105001, -0.000007, 0.000029, -0.000006, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 1373.640014, 1548.173706, 15.105001, -0.000007, 0.000029, -0.000006, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 1369.158935, 1553.464965, 15.105001, -0.000007, 0.000045, -0.000006, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 1369.158935, 1546.442504, 15.105001, -0.000007, 0.000022, -0.000006, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 1373.640014, 1546.442504, 15.105001, -0.000007, 0.000022, -0.000006, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 1373.640014, 1553.464965, 15.105001, -0.000007, 0.000045, -0.000006, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 1369.158935, 1551.733764, 15.105001, -0.000007, 0.000037, -0.000006, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 1373.640014, 1551.733764, 15.105001, -0.000007, 0.000037, -0.000006, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 1382.900146, 1548.173706, 15.105001, -0.000007, 0.000037, -0.000006, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 1387.381225, 1548.173706, 15.105001, -0.000007, 0.000037, -0.000006, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 1369.158935, 1556.724609, 15.105001, -0.000007, 0.000045, -0.000006, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 1373.640014, 1556.724609, 15.105001, -0.000007, 0.000045, -0.000006, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 1382.900146, 1553.464965, 15.105001, -0.000007, 0.000051, -0.000006, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 1382.900146, 1546.442504, 15.105001, -0.000007, 0.000029, -0.000006, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 1387.381225, 1546.442504, 15.105001, -0.000007, 0.000029, -0.000006, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 1387.381225, 1553.464965, 15.105001, -0.000007, 0.000051, -0.000006, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 1382.900146, 1551.733764, 15.105001, -0.000007, 0.000045, -0.000006, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 1387.381225, 1551.733764, 15.105001, -0.000007, 0.000045, -0.000006, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 1382.900146, 1556.724609, 15.105001, -0.000007, 0.000051, -0.000006, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 1387.381225, 1556.724609, 15.105001, -0.000007, 0.000051, -0.000006, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(1715, 1387.211914, 1537.650878, 14.680006, -0.000007, 0.000000, -89.999977, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(1715, 1387.211914, 1539.281127, 14.680006, -0.000007, 0.000000, -89.999977, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(1715, 1387.211914, 1541.071289, 14.680006, -0.000007, 0.000000, -89.999977, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(1715, 1369.429931, 1541.071289, 14.680006, -0.000007, 0.000000, 89.999961, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(1715, 1369.429931, 1539.441040, 14.680006, -0.000007, 0.000000, 89.999961, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(1715, 1369.429931, 1537.650878, 14.680006, -0.000007, 0.000000, 89.999961, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(638, 1396.843627, 1591.935546, 16.550001, 0.000000, 0.000000, 90.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(638, 1403.754028, 1591.935546, 16.550001, 0.000000, 0.000000, 90.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(638, 1403.703979, 1558.875610, 16.550001, 0.000007, -0.000007, -90.000038, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(638, 1396.743530, 1558.875610, 16.550001, 0.000007, -0.000007, -90.000038, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2200, 1366.688598, 1601.479492, 14.670305, 0.000029, 0.000000, 89.999908, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2200, 1366.688598, 1603.709838, 14.670305, 0.000029, 0.000000, 89.999908, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2200, 1369.078735, 1596.598144, 14.670305, 0.000036, 0.000000, 89.999885, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2200, 1369.078735, 1598.828491, 14.670305, 0.000036, 0.000000, 89.999885, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2200, 1369.078735, 1594.367553, 14.670305, 0.000036, 0.000000, 89.999885, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(1663, 1372.879272, 1599.476196, 15.080318, 0.000007, 0.000000, 89.999977, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(1663, 1372.879272, 1597.676269, 15.080318, 0.000007, 0.000000, 89.999977, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(1663, 1372.879272, 1595.805908, 15.080318, 0.000007, 0.000000, 89.999977, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 1376.226562, 1560.717041, 15.180307, 0.000000, -0.000007, 179.999954, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 1380.626586, 1560.717041, 15.180307, 0.000000, -0.000007, 179.999954, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(676, 1366.010253, 1582.888061, 16.090311, 0.000000, 0.000007, 0.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(804, 1365.665649, 1582.147705, 16.880329, 0.000000, 0.000007, 0.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(804, 1365.665649, 1583.407714, 16.610322, 0.000000, 0.000007, 0.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(676, 1365.980224, 1568.257812, 16.090311, 0.000000, 0.000014, 0.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(804, 1365.665649, 1567.517456, 16.880329, 0.000000, 0.000014, 0.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(804, 1365.665649, 1568.777465, 16.610322, 0.000000, 0.000014, 0.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2745, 1408.899902, 1579.161376, 17.190298, 0.000000, 0.000000, 270.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2745, 1408.899902, 1572.230224, 17.190298, 0.000000, 0.000000, 270.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2162, 1368.896240, 1612.109497, 14.660306, 0.000000, 0.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2514, 1384.957031, 1597.633422, 14.672795, 0.000000, 0.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2518, 1384.691772, 1595.952514, 14.950450, 0.000000, 0.000000, 270.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2517, 1382.815795, 1596.631713, 14.646837, 0.000000, 0.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(1671, 1387.301147, 1606.382812, 15.100487, 0.000000, 0.000000, 270.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(1704, 1382.798828, 1607.663208, 14.639233, 0.000000, 0.000000, 450.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(1704, 1382.798828, 1609.533447, 14.639233, 0.000000, 0.000000, 450.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(19786, 1386.194091, 1611.907470, 16.897108, 0.000000, 0.000000, 360.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2266, 1388.117187, 1607.706542, 16.885650, 0.000000, 0.000000, 270.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2263, 1383.623413, 1611.411621, 16.552131, 0.000000, 0.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2269, 1382.824462, 1604.075561, 16.489128, 0.000000, 0.000000, 90.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2267, 1388.588989, 1606.338623, 17.184436, 0.000000, 0.000000, 270.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2261, 1388.106933, 1604.976196, 16.796215, 0.000000, 0.000000, 270.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(948, 1384.065429, 1611.559570, 14.632913, 0.000000, 0.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2164, 1384.291137, 1602.501220, 14.658367, 0.000000, 0.000000, 180.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2174, 1386.448242, 1602.960327, 14.668387, 0.000000, 0.000000, 180.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2162, 1388.608154, 1610.124023, 14.668044, 0.000000, 0.000000, 270.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2167, 1388.591796, 1608.259521, 14.670706, 0.000000, 0.000000, 270.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2163, 1388.600097, 1609.267089, 17.322229, 0.000000, 180.000000, 270.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2894, 1385.752929, 1606.475219, 15.592387, 0.000000, 0.000000, 104.999992, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(19893, 1385.922851, 1605.748291, 15.595125, 0.000000, 0.000000, 108.299987, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2163, 1388.600097, 1604.586791, 14.672216, 0.000000, 360.000000, 270.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2165, 1370.763061, 1604.501098, 14.682783, 0.000000, 0.000000, 270.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2165, 1370.763061, 1607.330566, 14.682783, 0.000000, 0.000000, 270.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(1663, 1369.639160, 1603.886718, 15.080318, 0.000007, 0.000000, 89.999977, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(1663, 1369.639160, 1606.696533, 15.080318, 0.000007, 0.000000, 89.999977, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2277, 1367.303955, 1606.731445, 15.957401, 0.000000, 0.000000, 90.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2270, 1367.304199, 1607.740600, 15.914992, 0.000000, 0.000000, 90.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2270, 1367.304199, 1608.521362, 15.914992, 0.000000, 0.000000, 90.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2163, 1374.599975, 1605.138549, 17.303207, 0.000000, 180.000000, 270.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2163, 1374.599975, 1607.737304, 17.303207, 0.000000, 180.000000, 270.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2164, 1366.806396, 1609.532714, 14.667694, 0.000000, 0.000000, 90.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2001, 1367.070434, 1605.885864, 14.615146, 0.000000, 0.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2001, 1367.208862, 1611.765258, 14.670308, 0.000000, 0.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2161, 1374.600097, 1611.631835, 14.670307, 0.000000, 0.000000, 270.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2161, 1374.600097, 1610.302001, 14.670307, 0.000000, 0.000000, 270.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(19825, 1369.087036, 1597.657348, 17.369903, 0.000000, 0.000000, 90.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2372, 1373.995727, 1615.111572, 14.638015, 0.000000, 0.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2394, 1373.603759, 1613.309082, 16.451358, 0.000007, 0.000000, 89.999977, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2394, 1373.783935, 1615.728881, 15.361349, 0.000007, 0.000000, 269.999969, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2163, 1374.030517, 1615.297119, 17.303207, 0.000000, 180.000000, 270.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(1776, 1372.344726, 1593.072021, 15.750816, 0.000000, 0.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(1302, 1371.172241, 1593.122924, 14.654269, 0.000000, 0.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(1775, 1373.552246, 1592.947021, 15.737951, 0.000000, 0.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 1366.575439, 1593.050415, 15.070151, 0.000000, 0.000000, 90.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2197, 1369.325439, 1583.228271, 15.980316, 0.000000, 0.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2197, 1370.006103, 1583.228271, 15.980316, 0.000000, 0.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2197, 1370.686279, 1583.228271, 15.980316, 0.000000, 0.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(14455, 1370.379272, 1557.874633, 16.300296, 0.000000, 0.000000, 360.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(14455, 1364.299438, 1557.874633, 16.300296, 0.000000, 0.000000, 360.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2357, 1389.473266, 1522.648437, 15.060001, 0.000015, -0.000015, 179.999832, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2357, 1389.473266, 1523.969604, 15.060001, 0.000015, -0.000015, 179.999832, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2357, 1393.722900, 1522.648437, 15.060001, 0.000022, -0.000015, 179.999816, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2357, 1393.722900, 1523.969604, 15.060001, 0.000022, -0.000015, 179.999816, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(19805, 1381.956298, 1522.256591, 16.648180, 0.000000, 0.000000, 450.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(19805, 1381.976318, 1524.525146, 16.648180, 0.000000, 0.000000, 450.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(19805, 1381.946289, 1522.256591, 17.908166, 0.000015, 0.000000, 89.999954, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(19805, 1381.966308, 1524.525146, 17.908166, 0.000015, 0.000000, 89.999954, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(1714, 1388.181884, 1525.804565, 14.660005, 0.000000, -0.000007, 359.999938, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(1714, 1389.811889, 1525.804565, 14.660005, 0.000000, -0.000007, 359.999938, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(1714, 1391.612426, 1525.804565, 14.660005, 0.000000, -0.000007, 359.999938, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(1714, 1393.282958, 1525.804565, 14.660005, 0.000000, -0.000007, 359.999938, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(1714, 1395.114135, 1525.804565, 14.660005, 0.000000, -0.000007, 359.999938, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(1714, 1395.114135, 1520.904418, 14.660005, 0.000007, -0.000006, 179.999816, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(1714, 1393.484130, 1520.904418, 14.660005, 0.000007, -0.000006, 179.999816, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(1714, 1391.683593, 1520.904418, 14.660005, 0.000007, -0.000006, 179.999816, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(1714, 1390.013061, 1520.904418, 14.660005, 0.000007, -0.000006, 179.999816, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(1714, 1388.181884, 1520.904418, 14.660005, 0.000007, -0.000006, 179.999816, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(1714, 1386.251708, 1523.324340, 14.660005, 0.000000, -0.000007, 449.999938, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(16779, 1391.879760, 1523.261474, 20.510040, 0.000000, 0.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(1232, 1391.755126, 1523.303222, 11.647110, 0.000000, 360.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2197, 1397.477905, 1525.534790, 14.672058, 0.000000, 0.000000, 270.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2197, 1397.477905, 1526.215209, 14.672058, 0.000000, 0.000000, 270.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2197, 1397.477905, 1526.905761, 14.672058, 0.000000, 0.000000, 270.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2001, 1398.229125, 1527.942138, 14.664829, 0.000000, 0.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2272, 1392.200195, 1528.162597, 17.499767, 0.000000, 0.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2270, 1389.243896, 1528.161865, 16.532667, 0.000000, 0.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2270, 1389.854492, 1528.161865, 16.942670, 0.000000, 0.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2270, 1390.444824, 1528.161865, 17.572673, 0.000000, 0.000000, 0.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2286, 1398.911010, 1523.803100, 17.912984, 0.000000, 0.000000, 270.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2164, 1397.566284, 1519.065795, 14.657299, 0.000000, 0.000000, 180.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2167, 1398.936523, 1521.031250, 14.668991, -0.000007, -0.000000, -89.999977, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(1808, 1398.485961, 1519.231567, 14.653567, 0.000000, 0.000000, 540.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2163, 1393.388427, 1519.049438, 18.327280, 0.000000, 180.000000, 180.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2163, 1388.997802, 1519.049438, 18.327283, 0.000000, 180.000000, 540.000000, 99, 3, -1, 200.00, 200.00); 
    CreateDynamicObject(2167, 1398.936523, 1521.961425, 14.668991, -0.000007, -0.000000, -89.999977, 99, 3, -1, 200.00, 200.00);
}