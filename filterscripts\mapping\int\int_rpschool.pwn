CreateRPSchoolInt()
{
    new STREAMER_TAG_OBJECT:xcsalcaawd;
    xcsalcaawd = CreateDynamicObject(18981, 1023.808288, 2445.222900, 300.000000, 360.000000, 90.000000, 0.000000, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    xcsalcaawd = CreateDynamicObject(18981, 1048.777587, 2445.222900, 300.000000, 360.000000, 90.000000, 0.000000, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    xcsalcaawd = CreateDynamicObject(18981, 1023.808288, 2420.249267, 300.000000, 0.000000, 90.000022, 0.000000, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    xcsalcaawd = CreateDynamicObject(18981, 1048.777587, 2420.249267, 300.000000, 0.000000, 90.000022, 0.000000, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    xcsalcaawd = CreateDynamicObject(18981, 1027.808959, 2419.061279, 301.540130, 0.000000, 90.000030, 0.000000, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 19267, "mapmarkers", "red-2", 0x00000000);
    xcsalcaawd = CreateDynamicObject(18981, 1044.777099, 2419.061279, 301.550140, 0.000000, 90.000030, 0.000000, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 19267, "mapmarkers", "red-2", 0x00000000);
    xcsalcaawd = CreateDynamicObject(18765, 1035.059082, 2452.592041, 299.098205, 0.000000, 0.000022, 0.000000, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    xcsalcaawd = CreateDynamicObject(18765, 1044.127807, 2452.592041, 299.088195, 0.000000, 0.000022, 0.000000, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    xcsalcaawd = CreateDynamicObject(14407, 1013.311950, 2433.392333, 298.842803, 0.000000, 0.000000, 0.000000, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1975, "texttest", "kb_red", 0x00000000);
    xcsalcaawd = CreateDynamicObject(14407, 1059.233276, 2433.392333, 298.842803, 0.000000, 0.000000, 0.000000, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1975, "texttest", "kb_red", 0x00000000);
    xcsalcaawd = CreateDynamicObject(18981, 1027.978637, 2431.089111, 289.510192, 90.000000, 90.000030, 0.000000, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_WOOD_DARK", 0x00000000);
    xcsalcaawd = CreateDynamicObject(18981, 1044.709716, 2431.109130, 289.510192, 90.000000, 90.000030, 0.000000, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_WOOD_DARK", 0x00000000);
    xcsalcaawd = CreateDynamicObject(14407, 1013.311950, 2430.213867, 300.342926, 0.000000, 0.000000, 0.000000, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1975, "texttest", "kb_red", 0x00000000);
    xcsalcaawd = CreateDynamicObject(14407, 1059.222290, 2430.213867, 300.342926, 0.000000, 0.000000, 0.000000, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1975, "texttest", "kb_red", 0x00000000);
    xcsalcaawd = CreateDynamicObject(18981, 1027.808959, 2416.870117, 303.010711, 0.000000, 90.000053, 0.000000, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 19267, "mapmarkers", "red-2", 0x00000000);
    xcsalcaawd = CreateDynamicObject(18981, 1044.777099, 2416.870117, 303.020721, 0.000000, 90.000053, 0.000000, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 19267, "mapmarkers", "red-2", 0x00000000);
    xcsalcaawd = CreateDynamicObject(18981, 1027.920654, 2425.716552, 292.299957, 89.999992, 180.000030, -89.999977, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_WOOD_DARK", 0x00000000);
    xcsalcaawd = CreateDynamicObject(18981, 1044.651733, 2425.736572, 292.299957, 89.999992, 180.000030, -89.999977, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_WOOD_DARK", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1056.422241, 2432.085693, 300.468170, 0.000000, -0.000007, 179.999954, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(19353, 1057.196655, 2431.922119, 300.301910, 0.000000, 0.000000, 0.000000, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1056.422241, 2429.885986, 301.948059, 0.000000, -0.000007, 179.999954, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1054.322998, 2432.085693, 300.468170, 0.000000, -0.000014, 179.999908, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1054.322998, 2429.885986, 301.948059, 0.000000, -0.000014, 179.999908, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1052.233520, 2432.085693, 300.468170, 0.000000, -0.000014, 179.999908, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1052.233520, 2429.885986, 301.948059, 0.000000, -0.000014, 179.999908, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1050.134277, 2432.085693, 300.468170, 0.000000, -0.000022, 179.999862, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1050.134277, 2429.885986, 301.948059, 0.000000, -0.000022, 179.999862, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1048.033447, 2432.085693, 300.468170, 0.000000, -0.000014, 179.999908, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1048.033447, 2429.885986, 301.948059, 0.000000, -0.000014, 179.999908, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1045.934204, 2432.085693, 300.468170, 0.000000, -0.000022, 179.999862, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1045.934204, 2429.885986, 301.948059, 0.000000, -0.000022, 179.999862, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1043.844726, 2432.085693, 300.468170, 0.000000, -0.000022, 179.999862, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1043.844726, 2429.885986, 301.948059, 0.000000, -0.000022, 179.999862, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1041.745483, 2432.085693, 300.468170, 0.000000, -0.000029, 179.999816, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1041.745483, 2429.885986, 301.948059, 0.000000, -0.000029, 179.999816, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1039.643066, 2432.085693, 300.468170, 0.000000, -0.000014, 179.999908, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1039.643066, 2429.885986, 301.948059, 0.000000, -0.000014, 179.999908, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1037.543823, 2432.085693, 300.468170, 0.000000, -0.000022, 179.999862, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1037.543823, 2429.885986, 301.948059, 0.000000, -0.000022, 179.999862, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1035.454345, 2432.085693, 300.468170, 0.000000, -0.000022, 179.999862, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1035.454345, 2429.885986, 301.948059, 0.000000, -0.000022, 179.999862, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1033.355102, 2432.085693, 300.468170, 0.000000, -0.000029, 179.999816, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1033.355102, 2429.885986, 301.948059, 0.000000, -0.000029, 179.999816, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1031.254272, 2432.085693, 300.468170, 0.000000, -0.000022, 179.999862, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1031.254272, 2429.885986, 301.948059, 0.000000, -0.000022, 179.999862, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1029.155029, 2432.085693, 300.468170, 0.000000, -0.000029, 179.999816, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1029.155029, 2429.885986, 301.948059, 0.000000, -0.000029, 179.999816, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1027.065551, 2432.085693, 300.468170, 0.000000, -0.000029, 179.999816, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1027.065551, 2429.885986, 301.948059, 0.000000, -0.000029, 179.999816, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1024.966308, 2432.085693, 300.468170, 0.000000, -0.000037, 179.999771, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1024.966308, 2429.885986, 301.948059, 0.000000, -0.000037, 179.999771, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1022.865356, 2432.085693, 300.468170, 0.000000, -0.000037, 179.999771, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1022.865356, 2429.885986, 301.948059, 0.000000, -0.000037, 179.999771, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1020.775878, 2432.085693, 300.468170, 0.000000, -0.000037, 179.999771, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1020.775878, 2429.885986, 301.948059, 0.000000, -0.000037, 179.999771, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1018.676635, 2432.085693, 300.468170, 0.000000, -0.000045, 179.999725, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1018.676635, 2429.885986, 301.948059, 0.000000, -0.000045, 179.999725, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(19353, 1015.395935, 2431.922119, 300.301910, 0.000000, 0.000000, 0.000000, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    xcsalcaawd = CreateDynamicObject(14407, 1059.222290, 2427.034912, 301.862731, 0.000000, 0.000000, 0.000000, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1975, "texttest", "kb_red", 0x00000000);
    xcsalcaawd = CreateDynamicObject(14407, 1013.321716, 2427.034912, 301.862731, 0.000000, 0.000000, 0.000000, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1975, "texttest", "kb_red", 0x00000000);
    xcsalcaawd = CreateDynamicObject(18981, 1027.808959, 2413.707031, 304.520202, 0.000000, 90.000083, 0.000000, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 19267, "mapmarkers", "red-2", 0x00000000);
    xcsalcaawd = CreateDynamicObject(18981, 1044.777099, 2413.707031, 304.530212, 0.000000, 90.000083, 0.000000, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 19267, "mapmarkers", "red-2", 0x00000000);
    xcsalcaawd = CreateDynamicObject(18981, 1027.920654, 2422.505859, 294.019836, 89.999992, 180.000061, -89.999961, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_WOOD_DARK", 0x00000000);
    xcsalcaawd = CreateDynamicObject(18981, 1044.651733, 2422.525878, 294.019836, 89.999992, 180.000061, -89.999961, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_WOOD_DARK", 0x00000000);
    xcsalcaawd = CreateDynamicObject(14407, 1059.222290, 2423.835449, 303.372619, 0.000000, 0.000000, 0.000000, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1975, "texttest", "kb_red", 0x00000000);
    xcsalcaawd = CreateDynamicObject(14407, 1013.331359, 2423.835449, 303.372619, 0.000000, 0.000000, 0.000000, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1975, "texttest", "kb_red", 0x00000000);
    xcsalcaawd = CreateDynamicObject(18981, 1027.808959, 2410.504394, 306.040405, 0.000000, 90.000099, 0.000000, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 19267, "mapmarkers", "red-2", 0x00000000);
    xcsalcaawd = CreateDynamicObject(18981, 1044.777099, 2410.504394, 306.050415, 0.000000, 90.000099, 0.000000, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 19267, "mapmarkers", "red-2", 0x00000000);
    xcsalcaawd = CreateDynamicObject(18981, 1027.920654, 2428.878173, 291.009124, 89.999992, 180.000091, -89.999961, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_WOOD_DARK", 0x00000000);
    xcsalcaawd = CreateDynamicObject(18981, 1044.651733, 2428.898193, 291.009124, 89.999992, 180.000091, -89.999961, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_WOOD_DARK", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1056.422241, 2426.703857, 303.488250, 0.000000, -0.000029, 179.999816, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1056.422241, 2423.503662, 304.968139, 0.000000, -0.000037, 179.999771, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1054.322998, 2426.703857, 303.488250, 0.000000, -0.000037, 179.999771, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1054.322998, 2423.503662, 304.968139, 0.000000, -0.000045, 179.999725, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1052.233520, 2426.703857, 303.488250, 0.000000, -0.000037, 179.999771, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1052.233520, 2423.503662, 304.968139, 0.000000, -0.000045, 179.999725, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1050.134277, 2426.703857, 303.488250, 0.000000, -0.000045, 179.999725, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1050.134277, 2423.503662, 304.968139, 0.000000, -0.000052, 179.999679, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1048.033447, 2426.703857, 303.488250, 0.000000, -0.000037, 179.999771, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1048.033447, 2423.503662, 304.968139, 0.000000, -0.000045, 179.999725, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1045.934204, 2426.703857, 303.488250, 0.000000, -0.000045, 179.999725, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1045.934204, 2423.503662, 304.968139, 0.000000, -0.000052, 179.999679, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1043.844726, 2426.703857, 303.488250, 0.000000, -0.000045, 179.999725, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1043.844726, 2423.503662, 304.968139, 0.000000, -0.000052, 179.999679, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1041.745483, 2426.703857, 303.488250, 0.000000, -0.000052, 179.999679, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1041.745483, 2423.503662, 304.968139, 0.000000, -0.000060, 179.999633, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1039.643066, 2426.703857, 303.488250, 0.000000, -0.000037, 179.999771, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1039.643066, 2423.503662, 304.968139, 0.000000, -0.000045, 179.999725, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1037.543823, 2426.703857, 303.488250, 0.000000, -0.000045, 179.999725, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1037.543823, 2423.503662, 304.968139, 0.000000, -0.000052, 179.999679, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1035.454345, 2426.703857, 303.488250, 0.000000, -0.000045, 179.999725, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1035.454345, 2423.503662, 304.968139, 0.000000, -0.000052, 179.999679, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1033.355102, 2426.703857, 303.488250, 0.000000, -0.000052, 179.999679, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1033.355102, 2423.503662, 304.968139, 0.000000, -0.000060, 179.999633, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1031.254272, 2426.703857, 303.488250, 0.000000, -0.000045, 179.999725, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1031.254272, 2423.503662, 304.968139, 0.000000, -0.000052, 179.999679, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1029.155029, 2426.703857, 303.488250, 0.000000, -0.000052, 179.999679, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1029.155029, 2423.503662, 304.968139, 0.000000, -0.000060, 179.999633, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1027.065551, 2426.703857, 303.488250, 0.000000, -0.000052, 179.999679, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1027.065551, 2423.503662, 304.968139, 0.000000, -0.000060, 179.999633, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1024.966308, 2426.703857, 303.488250, 0.000000, -0.000060, 179.999633, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1024.966308, 2423.503662, 304.968139, 0.000000, -0.000068, 179.999588, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1022.865356, 2426.703857, 303.488250, 0.000000, -0.000060, 179.999633, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1022.865356, 2423.503662, 304.968139, 0.000000, -0.000068, 179.999588, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1020.775878, 2426.703857, 303.488250, 0.000000, -0.000060, 179.999633, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1020.775878, 2423.503662, 304.968139, 0.000000, -0.000068, 179.999588, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1018.676635, 2426.703857, 303.488250, 0.000000, -0.000068, 179.999588, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1018.676635, 2423.503662, 304.968139, 0.000000, -0.000075, 179.999542, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(18981, 1048.787597, 2419.343261, 300.000000, 0.000000, 180.000000, 89.999977, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 6871, "vegascourtbld", "marbletilewal1_256", 0x00000000);
    xcsalcaawd = CreateDynamicObject(18981, 1023.808349, 2419.343261, 300.000000, 0.000000, 180.000000, 89.999977, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 6871, "vegascourtbld", "marbletilewal1_256", 0x00000000);
    xcsalcaawd = CreateDynamicObject(18981, 1010.878967, 2432.234375, 300.000000, 0.000000, 180.000000, 179.999954, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 6871, "vegascourtbld", "marbletilewal1_256", 0x00000000);
    xcsalcaawd = CreateDynamicObject(18981, 1061.698852, 2432.234375, 300.000000, 0.000000, 180.000000, 179.999954, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 6871, "vegascourtbld", "marbletilewal1_256", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1056.422241, 2420.362060, 306.538146, 0.000000, -0.000052, 179.999679, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1054.322998, 2420.362060, 306.538146, 0.000000, -0.000060, 179.999633, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1052.233520, 2420.362060, 306.538146, 0.000000, -0.000060, 179.999633, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1050.134277, 2420.362060, 306.538146, 0.000000, -0.000068, 179.999588, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1048.033447, 2420.362060, 306.538146, 0.000000, -0.000060, 179.999633, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1045.934204, 2420.362060, 306.538146, 0.000000, -0.000068, 179.999588, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1043.844726, 2420.362060, 306.538146, 0.000000, -0.000068, 179.999588, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1041.745483, 2420.362060, 306.538146, 0.000000, -0.000075, 179.999542, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1039.643066, 2420.362060, 306.538146, 0.000000, -0.000060, 179.999633, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1037.543823, 2420.362060, 306.538146, 0.000000, -0.000068, 179.999588, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1035.454345, 2420.362060, 306.538146, 0.000000, -0.000068, 179.999588, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1033.355102, 2420.362060, 306.538146, 0.000000, -0.000075, 179.999542, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1031.254272, 2420.362060, 306.538146, 0.000000, -0.000068, 179.999588, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1029.155029, 2420.362060, 306.538146, 0.000000, -0.000075, 179.999542, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1027.065551, 2420.362060, 306.538146, 0.000000, -0.000075, 179.999542, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1024.966308, 2420.362060, 306.538146, 0.000000, -0.000082, 179.999496, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1022.865356, 2420.362060, 306.538146, 0.000000, -0.000082, 179.999496, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1020.775878, 2420.362060, 306.538146, 0.000000, -0.000082, 179.999496, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(1702, 1018.676635, 2420.362060, 306.538146, 0.000000, -0.000091, 179.999450, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1355, "break_s_bins", "CJ_RED_LEATHER", 0x00000000);
    xcsalcaawd = CreateDynamicObject(19353, 1057.196655, 2428.720458, 301.781768, 0.000000, 0.000014, 0.000000, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    xcsalcaawd = CreateDynamicObject(19353, 1015.395935, 2428.720458, 301.781768, 0.000000, 0.000014, 0.000000, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    xcsalcaawd = CreateDynamicObject(19353, 1057.196655, 2425.520996, 303.301605, 0.000000, 0.000029, 0.000000, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    xcsalcaawd = CreateDynamicObject(19353, 1015.395935, 2425.520996, 303.301605, 0.000000, 0.000029, 0.000000, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    xcsalcaawd = CreateDynamicObject(19353, 1057.196655, 2422.339843, 304.811828, 0.000000, 0.000045, 0.000000, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    xcsalcaawd = CreateDynamicObject(19353, 1015.395935, 2422.339843, 304.811828, 0.000000, 0.000045, 0.000000, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    xcsalcaawd = CreateDynamicObject(18981, 1010.878967, 2457.225097, 300.000000, 0.000000, 180.000000, 179.999908, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 6871, "vegascourtbld", "marbletilewal1_256", 0x00000000);
    xcsalcaawd = CreateDynamicObject(18981, 1061.698852, 2457.225097, 300.000000, 0.000000, 180.000000, 179.999908, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 6871, "vegascourtbld", "marbletilewal1_256", 0x00000000);
    xcsalcaawd = CreateDynamicObject(18981, 1048.708740, 2458.175292, 300.000000, 0.000000, 180.000000, 269.999908, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 6871, "vegascourtbld", "marbletilewal1_256", 0x00000000);
    xcsalcaawd = CreateDynamicObject(18981, 1023.729003, 2458.175292, 300.000000, 0.000000, 180.000000, 269.999908, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 6871, "vegascourtbld", "marbletilewal1_256", 0x00000000);
    xcsalcaawd = CreateDynamicObject(18981, 1023.819519, 2445.285888, 312.920288, -0.000014, 270.000000, -90.000022, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    xcsalcaawd = CreateDynamicObject(18981, 1048.788574, 2445.285888, 312.920288, -0.000014, 270.000000, -90.000022, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    xcsalcaawd = CreateDynamicObject(18981, 1023.819519, 2423.596435, 312.910278, -0.000022, 270.000000, -90.000000, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    xcsalcaawd = CreateDynamicObject(18981, 1048.788574, 2423.596435, 312.910278, -0.000022, 270.000000, -90.000000, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    xcsalcaawd = CreateDynamicObject(18765, 1025.099365, 2452.592041, 299.098205, 0.000000, 0.000022, 0.000000, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    xcsalcaawd = CreateDynamicObject(19912, 1031.703369, 2447.750244, 304.350128, 0.000000, 0.000000, 0.000000, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 10765, "airportgnd_sfse", "white", 0x60FFFFFF);
    SetDynamicObjectMaterial(xcsalcaawd, 1, 10765, "airportgnd_sfse", "white", 0x60FFFFFF);
    SetDynamicObjectMaterial(xcsalcaawd, 2, 10765, "airportgnd_sfse", "white", 0x60FFFFFF);
    xcsalcaawd = CreateDynamicObject(19912, 1043.182495, 2447.750244, 304.350128, 0.000000, 0.000000, 0.000000, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 10765, "airportgnd_sfse", "white", 0x60FFFFFF);
    SetDynamicObjectMaterial(xcsalcaawd, 1, 10765, "airportgnd_sfse", "white", 0x60FFFFFF);
    SetDynamicObjectMaterial(xcsalcaawd, 2, 10765, "airportgnd_sfse", "white", 0x60FFFFFF);
    xcsalcaawd = CreateDynamicObject(19912, 1049.081787, 2447.740234, 304.340118, 0.000000, 0.000000, 0.000000, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 10765, "airportgnd_sfse", "white", 0x60FFFFFF);
    SetDynamicObjectMaterial(xcsalcaawd, 1, 10765, "airportgnd_sfse", "white", 0x60FFFFFF);
    SetDynamicObjectMaterial(xcsalcaawd, 2, 10765, "airportgnd_sfse", "white", 0x60FFFFFF);
    xcsalcaawd = CreateDynamicObject(19912, 1020.173034, 2447.750244, 304.350128, 0.000000, 0.000000, 270.000000, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, -1, "none", "none", 0x60FFFFFF);
    SetDynamicObjectMaterial(xcsalcaawd, 1, 10765, "airportgnd_sfse", "white", 0x60FFFFFF);
    SetDynamicObjectMaterial(xcsalcaawd, 2, 10765, "airportgnd_sfse", "white", 0x60FFFFFF);
    xcsalcaawd = CreateDynamicObject(19912, 1049.032958, 2447.750244, 304.350128, 0.000000, 0.000000, 270.000000, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 10765, "airportgnd_sfse", "white", 0x60FFFFFF);
    SetDynamicObjectMaterial(xcsalcaawd, 1, 10765, "airportgnd_sfse", "white", 0x60FFFFFF);
    SetDynamicObjectMaterial(xcsalcaawd, 2, 10765, "airportgnd_sfse", "white", 0x60FFFFFF);
    xcsalcaawd = CreateDynamicObject(18766, 1016.296691, 2441.099365, 300.021514, 90.000000, 0.000000, 0.000000, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 19267, "mapmarkers", "red-2", 0x00000000);
    xcsalcaawd = CreateDynamicObject(18766, 1026.256347, 2441.099365, 300.021514, 90.000000, 0.000000, 0.000000, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 19267, "mapmarkers", "red-2", 0x00000000);
    xcsalcaawd = CreateDynamicObject(18766, 1036.257324, 2441.099365, 300.021514, 89.999992, 89.999992, -89.999992, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 19267, "mapmarkers", "red-2", 0x00000000);
    xcsalcaawd = CreateDynamicObject(18766, 1046.217041, 2441.099365, 300.021514, 89.999992, 89.999992, -89.999992, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 19267, "mapmarkers", "red-2", 0x00000000);
    xcsalcaawd = CreateDynamicObject(18766, 1056.196777, 2441.099365, 300.021514, 89.999992, 89.999992, -89.999992, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 19267, "mapmarkers", "red-2", 0x00000000);
    xcsalcaawd = CreateDynamicObject(4735, 1035.130493, 2457.648681, 307.582214, 0.000000, 0.000000, 90.699989, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    xcsalcaawd = CreateDynamicObject(14407, 1059.222290, 2417.714355, 303.362609, 0.000000, 0.000000, 180.000000, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1975, "texttest", "kb_red", 0x00000000);
    xcsalcaawd = CreateDynamicObject(14407, 1013.331359, 2422.714355, 303.352600, 0.000000, 0.000000, 0.000000, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 0, 1975, "texttest", "kb_red", 0x00000000);
    xcsalcaawd = CreateDynamicObject(640, 1044.481323, 2448.059570, 302.090057, 0.000000, 0.000000, 270.000000, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 1, 4992, "airportdetail", "bevflower1", 0x00000000);
    xcsalcaawd = CreateDynamicObject(640, 1035.481201, 2448.059570, 302.090057, 0.000000, 0.000000, 270.000000, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 1, 4992, "airportdetail", "bevflower1", 0x00000000);
    xcsalcaawd = CreateDynamicObject(640, 1025.250854, 2448.059570, 302.090057, 0.000000, 0.000000, 270.000000, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(xcsalcaawd, 1, 4992, "airportdetail", "bevflower1", 0x00000000);
    xcsalcaawd = CreateDynamicObject(19480, 1036.885131, 2419.898925, 311.321655, -0.000015, 0.000000, 90.000045, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(xcsalcaawd, 0, "ATHERLIFE MENYATUKAN BANGSA", 120, "Arial", 25, 1, 0xFFFFFFFF, 0x00000000, 1);
    xcsalcaawd = CreateDynamicObject(19480, 1035.744628, 2447.578857, 301.011474, -0.000022, 0.000000, -89.999931, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(xcsalcaawd, 0, "ING MADYA MANGUN KARSA", 120, "Arial", 18, 1, 0xFF000000, 0x00000000, 1);
    xcsalcaawd = CreateDynamicObject(19480, 1044.653808, 2447.578857, 301.021453, -0.000022, 0.000000, -89.999931, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(xcsalcaawd, 0, "TUT WURI HANDAYANI", 120, "Arial", 18, 1, 0xFF000000, 0x00000000, 1);
    xcsalcaawd = CreateDynamicObject(19480, 1036.885131, 2419.898925, 310.031463, -0.000015, 0.000000, 90.000045, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(xcsalcaawd, 0, "MASA DEPAN ADALAH MILIK KITA", 120, "Arial", 25, 1, 0xFFFFFFFF, 0x00000000, 1);
    xcsalcaawd = CreateDynamicObject(19480, 1025.665039, 2447.578857, 301.011474, -0.000022, 0.000000, -89.999931, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(xcsalcaawd, 0, "ING NGARSA SUNG TULADHA", 120, "Arial", 18, 1, 0xFF000000, 0x00000000, 1);
    xcsalcaawd = CreateDynamicObject(19480, 1036.885131, 2419.898925, 308.781188, -0.000015, 0.000000, 90.000045, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(xcsalcaawd, 0, "ATHERLIFE JUGA RUMAH KITA", 120, "Arial", 25, 1, 0xFFFFFFFF, 0x00000000, 1);
    xcsalcaawd = CreateDynamicObject(19480, 1061.174560, 2436.250976, 311.131072, -0.000015, 0.000000, 540.000061, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(xcsalcaawd, 0, "Ilmu yang bermanfaat harus dibagikan", 120, "Arial", 25, 1, 0xFFFFFFFF, 0x00000000, 1);
    xcsalcaawd = CreateDynamicObject(19480, 1061.174560, 2434.940429, 310.261077, -0.000015, 0.000000, 540.000061, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(xcsalcaawd, 0, "kepada siapapun yang menghendakinya", 120, "Arial", 25, 1, 0xFFFFFFFF, 0x00000000, 1);
    xcsalcaawd = CreateDynamicObject(19480, 1061.174560, 2434.940429, 309.311004, -0.000015, 0.000000, 540.000061, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(xcsalcaawd, 0, "tiada kata terlambat untuk belajar", 120, "Arial", 25, 1, 0xFFFFFFFF, 0x00000000, 1);
    xcsalcaawd = CreateDynamicObject(19480, 1011.394592, 2439.161376, 310.480895, -0.000015, 0.000000, 720.000061, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(xcsalcaawd, 0, "Mengingatkan dan mengedukasi pelanggar aturan", 120, "Arial", 25, 1, 0xFFFFFFFF, 0x00000000, 1);
    xcsalcaawd = CreateDynamicObject(19480, 1011.394592, 2440.391845, 309.450897, -0.000015, 0.000000, 720.000061, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(xcsalcaawd, 0, "lebih baik daripada mengadukannya kepada admin", 120, "Arial", 25, 1, 0xFFFFFFFF, 0x00000000, 1);
    xcsalcaawd = CreateDynamicObject(19480, 1011.394592, 2438.369628, 308.430847, -0.000015, 0.000000, 720.000061, 256, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(xcsalcaawd, 0, "jadilah pribadi yang mengedukasi itu", 120, "Arial", 25, 1, 0xFFFFFFFF, 0x00000000, 1);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(1536, 1061.191772, 2446.237060, 300.420898, 0.000000, 0.000000, 90.000000, 256, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1536, 1061.191772, 2449.246337, 300.420898, 0.000000, 0.000000, 270.000000, 256, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11711, 1061.199707, 2447.722656, 303.377227, 0.000000, 0.000000, 90.000000, 256, 0, -1, 200.00, 200.00); 
}