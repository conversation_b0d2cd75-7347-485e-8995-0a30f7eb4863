CreateSpawnInt()
{
    //LOS SANTOS
    // static LSAINTXT;
    // LSAINTXT = CreateDynamicObject(8661, 1756.918334, -2518.860595, 19.286909, 0.000000, 0.000007, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 14803, "bdupsnew", "Bdup2_Artex", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(18764, 1754.344848, -2516.690673, 17.816917, 0.000000, 0.000044, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 4829, "airport_las", "Grass_128HV", 0x00000000);
    // LSAINTXT = CreateDynamicObject(18766, 1756.373535, -2516.700927, 15.536044, 0.000044, 90.000000, 89.999862, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    // SetDynamicObjectMaterial(LSAINTXT, 1, -1, "none", "none", 0xFF4E4945);
    // LSAINTXT = CreateDynamicObject(3632, 1756.545410, -2519.260498, 20.056932, 0.000000, 0.000044, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    // LSAINTXT = CreateDynamicObject(18766, 1754.032958, -2519.071777, 15.536916, 0.000000, 89.999954, 179.999725, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    // SetDynamicObjectMaterial(LSAINTXT, 1, -1, "none", "none", 0xFF4E4945);
    // LSAINTXT = CreateDynamicObject(3632, 1756.545410, -2514.199707, 20.056932, 0.000000, 0.000044, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    // LSAINTXT = CreateDynamicObject(18766, 1754.032958, -2514.362060, 15.536916, 0.000000, 89.999954, 179.999725, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    // SetDynamicObjectMaterial(LSAINTXT, 1, -1, "none", "none", 0xFF4E4945);
    // LSAINTXT = CreateDynamicObject(18766, 1751.704223, -2516.759033, 15.536044, -0.000044, 90.000000, -89.999832, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    // SetDynamicObjectMaterial(LSAINTXT, 1, -1, "none", "none", 0xFF4E4945);
    // LSAINTXT = CreateDynamicObject(3632, 1751.532348, -2514.199462, 20.056932, 0.000000, -0.000044, 179.999618, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    // LSAINTXT = CreateDynamicObject(3632, 1751.532348, -2519.260253, 20.056932, 0.000000, -0.000044, 179.999618, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    // LSAINTXT = CreateDynamicObject(970, 1756.556884, -2516.801757, 20.126916, 89.999992, 180.000030, -89.999961, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // LSAINTXT = CreateDynamicObject(970, 1754.166259, -2519.253662, 20.126916, 89.999992, 90.000045, -89.999961, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19373, 1751.466918, -2515.486816, 19.286911, 0.000000, 90.000015, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0x00000000);
    // LSAINTXT = CreateDynamicObject(970, 1753.924682, -2514.179931, 20.126916, 89.999992, 266.598205, -86.598289, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // LSAINTXT = CreateDynamicObject(8661, 1756.918334, -2498.872070, 19.286909, 0.000000, 0.000007, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 14803, "bdupsnew", "Bdup2_Artex", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(8661, 1756.918334, -2538.840576, 19.286909, 0.000000, 0.000007, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 14803, "bdupsnew", "Bdup2_Artex", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(971, 1772.395141, -2506.447998, 20.786895, 0.000029, 0.000000, 89.999908, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 2, 18878, "ferriswheel", "railing3", 0xFF333333);
    // SetDynamicObjectMaterial(LSAINTXT, 3, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 4, 19480, "signsurf", "sign", 0x00000000);
    // LSAINTXT = CreateDynamicObject(971, 1772.405151, -2506.447998, 20.786895, 0.000029, 0.000000, 89.999908, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 2, 14581, "ab_mafiasuitea", "barbersmir1", 0x90FFFFFF);
    // SetDynamicObjectMaterial(LSAINTXT, 3, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 4, 19480, "signsurf", "sign", 0x00000000);
    // LSAINTXT = CreateDynamicObject(971, 1772.395141, -2506.447998, 27.896932, 0.000037, 0.000000, 89.999885, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 2, 18878, "ferriswheel", "railing3", 0xFF333333);
    // SetDynamicObjectMaterial(LSAINTXT, 3, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 4, 19480, "signsurf", "sign", 0x00000000);
    // LSAINTXT = CreateDynamicObject(971, 1772.405151, -2506.447998, 27.896932, 0.000037, 0.000000, 89.999885, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 2, 14581, "ab_mafiasuitea", "barbersmir1", 0x90FFFFFF);
    // SetDynamicObjectMaterial(LSAINTXT, 3, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 4, 19480, "signsurf", "sign", 0x00000000);
    // LSAINTXT = CreateDynamicObject(971, 1772.395141, -2528.045410, 20.786895, 0.000037, 0.000000, 89.999885, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 2, 18878, "ferriswheel", "railing3", 0xFF333333);
    // SetDynamicObjectMaterial(LSAINTXT, 3, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 4, 19480, "signsurf", "sign", 0x00000000);
    // LSAINTXT = CreateDynamicObject(971, 1772.405151, -2528.045410, 20.786895, 0.000037, 0.000000, 89.999885, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 2, 14581, "ab_mafiasuitea", "barbersmir1", 0x90FFFFFF);
    // SetDynamicObjectMaterial(LSAINTXT, 3, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 4, 19480, "signsurf", "sign", 0x00000000);
    // LSAINTXT = CreateDynamicObject(971, 1772.395141, -2528.045410, 27.896932, 0.000045, 0.000000, 89.999862, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 2, 18878, "ferriswheel", "railing3", 0xFF333333);
    // SetDynamicObjectMaterial(LSAINTXT, 3, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 4, 19480, "signsurf", "sign", 0x00000000);
    // LSAINTXT = CreateDynamicObject(971, 1772.405151, -2528.045410, 27.896932, 0.000045, 0.000000, 89.999862, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 2, 14581, "ab_mafiasuitea", "barbersmir1", 0x90FFFFFF);
    // SetDynamicObjectMaterial(LSAINTXT, 3, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 4, 19480, "signsurf", "sign", 0x00000000);
    // LSAINTXT = CreateDynamicObject(975, 1772.395141, -2520.500000, 19.106908, -0.000014, 270.000000, -89.999961, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 16640, "a51", "a51_glass", 0xFF333333);
    // SetDynamicObjectMaterial(LSAINTXT, 2, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 4, 19480, "signsurf", "sign", 0x00000000);
    // LSAINTXT = CreateDynamicObject(975, 1772.445190, -2520.500000, 19.106908, -0.000014, 270.000000, -89.999961, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 14581, "ab_mafiasuitea", "barbersmir1", 0x90FFFFFF);
    // SetDynamicObjectMaterial(LSAINTXT, 2, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 4, 19480, "signsurf", "sign", 0x00000000);
    // LSAINTXT = CreateDynamicObject(975, 1772.395141, -2517.308105, 19.106908, -0.000022, 270.000000, -89.999938, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 16640, "a51", "a51_glass", 0xFF333333);
    // SetDynamicObjectMaterial(LSAINTXT, 2, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 4, 19480, "signsurf", "sign", 0x00000000);
    // LSAINTXT = CreateDynamicObject(975, 1772.445190, -2517.308105, 19.106908, -0.000022, 270.000000, -89.999938, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 14581, "ab_mafiasuitea", "barbersmir1", 0x90FFFFFF);
    // SetDynamicObjectMaterial(LSAINTXT, 2, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 4, 19480, "signsurf", "sign", 0x00000000);
    // LSAINTXT = CreateDynamicObject(975, 1772.395141, -2514.125732, 19.106908, -0.000029, 270.000000, -89.999916, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 16640, "a51", "a51_glass", 0xFF333333);
    // SetDynamicObjectMaterial(LSAINTXT, 2, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 4, 19480, "signsurf", "sign", 0x00000000);
    // LSAINTXT = CreateDynamicObject(975, 1772.445190, -2514.125732, 19.106908, -0.000029, 270.000000, -89.999916, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 14581, "ab_mafiasuitea", "barbersmir1", 0x90FFFFFF);
    // SetDynamicObjectMaterial(LSAINTXT, 2, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 4, 19480, "signsurf", "sign", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19443, 1772.428710, -2511.653320, 20.996902, 0.000000, 0.000007, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16640, "a51", "scratchedmetal", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19443, 1772.418701, -2511.653320, 24.396894, 0.000000, 0.000007, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16640, "a51", "scratchedmetal", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19443, 1772.428710, -2514.164062, 24.366893, 89.999992, 89.999992, -89.999992, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16640, "a51", "scratchedmetal", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19443, 1772.428710, -2517.663330, 24.366893, 89.999992, 89.999992, -89.999992, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16640, "a51", "scratchedmetal", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19443, 1772.427978, -2520.264404, 24.366893, 89.999992, 89.999992, -89.999992, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16640, "a51", "scratchedmetal", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19443, 1772.428710, -2522.805175, 20.996902, 0.000000, 0.000014, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16640, "a51", "scratchedmetal", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19443, 1772.428710, -2522.805175, 24.396894, 0.000000, 0.000014, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16640, "a51", "scratchedmetal", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19443, 1772.428710, -2512.612792, 26.926912, 89.999992, 89.999992, -89.999977, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16640, "a51", "scratchedmetal", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19443, 1772.428710, -2516.112060, 26.926912, 89.999992, 89.999992, -89.999977, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16640, "a51", "scratchedmetal", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19443, 1772.427978, -2521.865234, 26.926912, 89.999992, 89.999992, -89.999977, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16640, "a51", "scratchedmetal", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19443, 1772.427978, -2518.404296, 26.926912, 89.999992, 89.999992, -89.999977, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16640, "a51", "scratchedmetal", 0x00000000);
    // LSAINTXT = CreateDynamicObject(971, 1772.395141, -2515.160156, 29.636903, 0.000037, 0.000000, 89.999885, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 2, 18878, "ferriswheel", "railing3", 0xFF333333);
    // SetDynamicObjectMaterial(LSAINTXT, 3, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 4, 19480, "signsurf", "sign", 0x00000000);
    // LSAINTXT = CreateDynamicObject(971, 1772.405151, -2515.160156, 29.636903, 0.000037, 0.000000, 89.999885, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 2, 14581, "ab_mafiasuitea", "barbersmir1", 0x90FFFFFF);
    // SetDynamicObjectMaterial(LSAINTXT, 3, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 4, 19480, "signsurf", "sign", 0x00000000);
    // LSAINTXT = CreateDynamicObject(971, 1772.415161, -2519.227294, 29.636903, 0.000051, 0.000000, 89.999839, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 2, 18878, "ferriswheel", "railing3", 0xFF333333);
    // SetDynamicObjectMaterial(LSAINTXT, 3, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 4, 19480, "signsurf", "sign", 0x00000000);
    // LSAINTXT = CreateDynamicObject(971, 1772.425170, -2519.227294, 29.636903, 0.000051, 0.000000, 89.999839, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 2, 14581, "ab_mafiasuitea", "barbersmir1", 0x90FFFFFF);
    // SetDynamicObjectMaterial(LSAINTXT, 3, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 4, 19480, "signsurf", "sign", 0x00000000);
    // LSAINTXT = CreateDynamicObject(18980, 1767.773193, -2504.832763, 20.678901, 14.999999, 5.000039, -0.000009, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    // LSAINTXT = CreateDynamicObject(18980, 1765.269531, -2504.832275, 20.678903, 14.999999, -4.999958, -0.000009, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    // LSAINTXT = CreateDynamicObject(18980, 1766.520874, -2504.823486, 20.646919, 23.299989, 0.000040, -0.000015, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    // LSAINTXT = CreateDynamicObject(18980, 1744.438964, -2529.588867, 20.678901, 14.999999, 5.000021, 179.999847, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    // LSAINTXT = CreateDynamicObject(18980, 1746.942626, -2529.589355, 20.678903, 14.999999, -4.999976, 179.999847, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    // LSAINTXT = CreateDynamicObject(18980, 1745.691284, -2529.598144, 20.646919, 23.299985, 0.000024, 179.999862, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    // LSAINTXT = CreateDynamicObject(18980, 1746.942626, -2504.832763, 20.678901, 14.999999, 5.000030, -0.000007, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    // LSAINTXT = CreateDynamicObject(18980, 1744.438964, -2504.832275, 20.678903, 14.999999, -4.999968, -0.000007, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    // LSAINTXT = CreateDynamicObject(18980, 1745.690307, -2504.823486, 20.646919, 23.299991, 0.000031, -0.000012, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    // LSAINTXT = CreateDynamicObject(18980, 1765.269531, -2529.588867, 20.678901, 14.999999, 5.000013, 179.999847, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    // LSAINTXT = CreateDynamicObject(18980, 1767.773193, -2529.589355, 20.678903, 14.999999, -4.999983, 179.999847, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    // LSAINTXT = CreateDynamicObject(18980, 1766.521850, -2529.598144, 20.646919, 23.299987, 0.000014, 179.999877, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    // LSAINTXT = CreateDynamicObject(18981, 1759.912719, -2532.950927, 18.956884, 0.000007, 0.000000, 89.999977, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16093, "a51_ext", "ws_whitewall2_top", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(18981, 1759.912719, -2501.551513, 18.956884, 0.000007, 0.000000, 89.999977, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16093, "a51_ext", "ws_whitewall2_top", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(11714, 1757.147949, -2502.031250, 20.556900, 0.000014, 0.000000, 89.999954, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 8391, "ballys01", "vgncorpdoor1_512", 0x00000000);
    // LSAINTXT = CreateDynamicObject(11714, 1754.176635, -2502.031250, 20.556900, 0.000014, 0.000000, 89.999954, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 8391, "ballys01", "vgncorpdoor1_512", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19443, 1752.649414, -2501.541503, 20.176921, 0.000000, 0.000007, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, -1, "none", "none", 0xFF4E4945);
    // LSAINTXT = CreateDynamicObject(19373, 1751.466918, -2517.968261, 19.285999, 0.000000, 90.000015, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0x00000000);
    // LSAINTXT = CreateDynamicObject(8324, 1772.738891, -2517.475097, 26.256921, -0.000007, 0.000000, -89.399986, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterialText(LSAINTXT, 1, "ATHERLIFE INTERNATIONAL AIRPORT", 120, "Calibri", 20, 1, 0xFF333333, 0x00000000, 1);
    // LSAINTXT = CreateDynamicObject(1297, 1767.983642, -2525.215087, 31.286954, 0.000000, 171.999984, -179.999938, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 19480, "signsurf", "sign", 0x00000000);
    // LSAINTXT = CreateDynamicObject(18766, 1745.960815, -2534.542968, 16.426906, 0.000004, 90.000007, 44.999988, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    // SetDynamicObjectMaterial(LSAINTXT, 1, -1, "none", "none", 0xFF4E4945);
    // LSAINTXT = CreateDynamicObject(18766, 1745.960815, -2534.542968, 26.426904, 0.000004, 90.000007, 44.999988, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    // SetDynamicObjectMaterial(LSAINTXT, 1, -1, "none", "none", 0xFF4E4945);
    // LSAINTXT = CreateDynamicObject(18981, 1756.341918, -2536.452392, 18.956884, 0.000007, 0.000000, 89.999977, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16093, "a51_ext", "ws_whitewall2_top", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(19937, 1746.666015, -2539.887695, 19.432029, 89.999992, 269.318817, -89.318679, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(19939, 1746.421386, -2538.731689, 20.282922, 0.000033, 89.999992, 179.999588, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    // LSAINTXT = CreateDynamicObject(19940, 1746.620849, -2538.221923, 19.502918, 89.999992, 449.318786, -89.318679, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    // LSAINTXT = CreateDynamicObject(19940, 1746.621582, -2539.902343, 19.502922, 89.999992, 449.318786, -89.318679, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    // LSAINTXT = CreateDynamicObject(19937, 1746.635009, -2538.252441, 19.252660, 79.999984, 0.000306, -0.000302, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(19937, 1746.635253, -2539.905273, 19.249666, 79.999984, -0.000306, -179.999526, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(19477, 1746.365234, -2539.072265, 19.822931, 0.000033, 0.000000, 179.999633, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterialText(LSAINTXT, 0, "CHECK-IN", 130, "Palatino Linotype", 30, 1, 0xFF333333, 0x00000000, 1);
    // LSAINTXT = CreateDynamicObject(19939, 1746.421386, -2539.422363, 20.282922, 0.000033, 89.999992, 179.999588, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    // LSAINTXT = CreateDynamicObject(19937, 1746.666259, -2539.257080, 19.432928, 89.999992, 269.318817, -89.318679, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(19939, 1746.627075, -2539.031005, 19.666912, 0.000000, 0.000022, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF00B8D4);
    // LSAINTXT = CreateDynamicObject(18980, 1746.929687, -2543.028564, 20.551191, 14.999944, 5.000021, -89.999954, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    // LSAINTXT = CreateDynamicObject(18980, 1746.851806, -2541.412353, 20.534702, 14.999944, -4.999976, -89.999954, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    // LSAINTXT = CreateDynamicObject(18980, 1747.556884, -2542.195068, 20.646919, 23.299934, 0.000024, -89.999954, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19937, 1746.666015, -2546.197998, 19.432029, 89.999992, 269.659454, -89.659309, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(19939, 1746.421386, -2545.041992, 20.282922, 0.000033, 89.999984, 179.999542, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    // LSAINTXT = CreateDynamicObject(19940, 1746.620849, -2544.532226, 19.502918, 89.999992, 449.659423, -89.659309, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    // LSAINTXT = CreateDynamicObject(19940, 1746.621582, -2546.212646, 19.502922, 89.999992, 449.659423, -89.659309, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    // LSAINTXT = CreateDynamicObject(19937, 1746.635009, -2544.562744, 19.252660, 79.999984, 0.000349, -0.000345, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(19937, 1746.635253, -2546.215576, 19.249666, 79.999984, -0.000349, -179.999450, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(19477, 1746.365234, -2545.382568, 19.822931, 0.000033, -0.000006, 179.999588, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterialText(LSAINTXT, 0, "CHECK-IN", 130, "Palatino Linotype", 30, 1, 0xFF333333, 0x00000000, 1);
    // LSAINTXT = CreateDynamicObject(19939, 1746.421386, -2545.732666, 20.282922, 0.000033, 89.999984, 179.999542, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    // LSAINTXT = CreateDynamicObject(19937, 1746.666259, -2545.567382, 19.432928, 89.999992, 269.659454, -89.659309, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(19939, 1746.627075, -2545.341308, 19.666912, 0.000000, 0.000029, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF00B8D4);
    // LSAINTXT = CreateDynamicObject(18980, 1746.929687, -2549.338867, 20.551191, 14.999935, 5.000021, -89.999931, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    // LSAINTXT = CreateDynamicObject(18980, 1746.851806, -2547.722656, 20.534702, 14.999935, -4.999976, -89.999931, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    // LSAINTXT = CreateDynamicObject(18980, 1747.556884, -2548.505371, 20.646919, 23.299924, 0.000024, -89.999931, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    // LSAINTXT = CreateDynamicObject(8661, 1756.918334, -2558.812255, 19.286909, 0.000000, 0.000007, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 14803, "bdupsnew", "Bdup2_Artex", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(19937, 1746.666015, -2552.569824, 19.432029, 89.999992, 269.659454, -89.659309, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(19939, 1746.421386, -2551.413818, 20.282922, 0.000033, 89.999984, 179.999542, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    // LSAINTXT = CreateDynamicObject(19940, 1746.620849, -2550.904052, 19.502918, 89.999992, 449.659423, -89.659309, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    // LSAINTXT = CreateDynamicObject(19940, 1746.621582, -2552.584472, 19.502922, 89.999992, 449.659423, -89.659309, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    // LSAINTXT = CreateDynamicObject(19937, 1746.635009, -2550.934570, 19.252660, 79.999984, 0.000349, -0.000345, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(19937, 1746.635253, -2552.587402, 19.249666, 79.999984, -0.000349, -179.999450, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(19477, 1746.365234, -2551.754394, 19.822931, 0.000033, -0.000006, 179.999588, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterialText(LSAINTXT, 0, "CHECK-IN", 130, "Palatino Linotype", 30, 1, 0xFF333333, 0x00000000, 1);
    // LSAINTXT = CreateDynamicObject(19939, 1746.421386, -2552.104492, 20.282922, 0.000033, 89.999984, 179.999542, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    // LSAINTXT = CreateDynamicObject(19937, 1746.666259, -2551.939208, 19.432928, 89.999992, 269.659454, -89.659309, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(19939, 1746.627075, -2551.713134, 19.666912, 0.000000, 0.000029, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF00B8D4);
    // LSAINTXT = CreateDynamicObject(18980, 1746.929687, -2555.710693, 20.551191, 14.999935, 5.000021, -89.999931, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    // LSAINTXT = CreateDynamicObject(18980, 1746.851806, -2554.094482, 20.534702, 14.999935, -4.999976, -89.999931, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    // LSAINTXT = CreateDynamicObject(18980, 1747.556884, -2554.877197, 20.646919, 23.299924, 0.000024, -89.999931, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19937, 1746.666015, -2558.880126, 19.432029, 89.999992, 269.829803, -89.829635, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(19939, 1746.421386, -2557.724121, 20.282922, 0.000033, 89.999977, 179.999496, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    // LSAINTXT = CreateDynamicObject(19940, 1746.620849, -2557.214355, 19.502918, 89.999992, 449.829772, -89.829635, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    // LSAINTXT = CreateDynamicObject(19940, 1746.621582, -2558.894775, 19.502922, 89.999992, 449.829772, -89.829635, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    // LSAINTXT = CreateDynamicObject(19937, 1746.635009, -2557.244873, 19.252660, 79.999984, 0.000394, -0.000388, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(19937, 1746.635253, -2558.897705, 19.249666, 79.999984, -0.000394, -179.999374, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(19477, 1746.365234, -2558.064697, 19.822931, 0.000033, -0.000012, 179.999542, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterialText(LSAINTXT, 0, "CHECK-IN", 130, "Palatino Linotype", 30, 1, 0xFF333333, 0x00000000, 1);
    // LSAINTXT = CreateDynamicObject(19939, 1746.421386, -2558.414794, 20.282922, 0.000033, 89.999977, 179.999496, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    // LSAINTXT = CreateDynamicObject(19937, 1746.666259, -2558.249511, 19.432928, 89.999992, 269.829803, -89.829635, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(19939, 1746.627075, -2558.023437, 19.666912, 0.000000, 0.000037, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF00B8D4);
    // LSAINTXT = CreateDynamicObject(18980, 1736.791625, -2548.411376, 19.006887, -0.000014, 90.000000, -90.000007, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    // LSAINTXT = CreateDynamicObject(18980, 1736.791625, -2523.461669, 19.006887, -0.000014, 90.000000, -90.000007, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    // LSAINTXT = CreateDynamicObject(18980, 1736.791625, -2498.462402, 19.006887, -0.000014, 90.000000, -90.000007, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    // LSAINTXT = CreateDynamicObject(18981, 1754.341430, -2549.321777, 18.956884, 0.000000, -0.000007, 179.999954, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16093, "a51_ext", "ws_whitewall2_top", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(18981, 1754.211303, -2549.291748, 12.206885, 0.000000, -0.000007, 179.999954, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    // LSAINTXT = CreateDynamicObject(18980, 1754.192626, -2549.290527, 20.956918, 0.000007, 90.000000, 89.999977, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16640, "a51", "scratchedmetal", 0xFF777777);
    // LSAINTXT = CreateDynamicObject(18981, 1756.621704, -2516.481201, 28.616870, 0.000000, 90.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    // LSAINTXT = CreateDynamicObject(2645, 1746.896362, -2532.869384, 21.216909, -0.000004, -0.000004, -135.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 2817, "gb_bedrmrugs01", "GB_livingrug03", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19787, 1745.977539, -2533.747802, 21.236919, -0.000004, -0.000004, -135.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 1, 2772, "airp_prop", "CJ_AIRP_S_2", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19787, 1744.754028, -2534.972412, 21.236919, -0.000004, -0.000004, -135.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 1, 2772, "airp_prop", "CJ_AIRP_S_1", 0x00000000);
    // LSAINTXT = CreateDynamicObject(18981, 1756.481933, -2536.530029, 12.206885, -0.000007, 0.000000, -89.999977, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    // LSAINTXT = CreateDynamicObject(18980, 1756.532592, -2536.548095, 20.956918, 0.000000, 89.999992, 179.999954, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16640, "a51", "scratchedmetal", 0xFF777777);
    // LSAINTXT = CreateDynamicObject(18981, 1755.952026, -2560.580078, 18.956884, 0.000000, 0.000000, -89.999969, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16093, "a51_ext", "ws_whitewall2_top", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(18981, 1756.192382, -2560.502441, 12.206885, 0.000000, 0.000000, 89.999938, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    // LSAINTXT = CreateDynamicObject(18980, 1756.341918, -2560.484375, 20.956918, 0.000000, 90.000000, -0.000059, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16640, "a51", "scratchedmetal", 0xFF777777);
    // LSAINTXT = CreateDynamicObject(2200, 1753.467773, -2537.504394, 19.296909, -0.000007, 0.000000, -89.999977, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 16640, "a51", "scratchedmetal", 0xFF555555);
    // LSAINTXT = CreateDynamicObject(2200, 1753.467773, -2539.715087, 19.296909, -0.000007, 0.000000, -89.999977, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 16640, "a51", "scratchedmetal", 0xFF555555);
    // LSAINTXT = CreateDynamicObject(2200, 1753.467773, -2541.926269, 19.296909, -0.000014, 0.000000, -89.999954, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 16640, "a51", "scratchedmetal", 0xFF555555);
    // LSAINTXT = CreateDynamicObject(8661, 1736.300537, -2548.654052, 12.596906, 0.000014, 0.000000, 89.999931, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 14803, "bdupsnew", "Bdup2_Artex", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(8661, 1736.300537, -2508.684570, 12.596905, 0.000014, 0.000000, 89.999931, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 14803, "bdupsnew", "Bdup2_Artex", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(2200, 1753.467773, -2553.843017, 19.296909, -0.000014, 0.000000, -89.999954, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 16640, "a51", "scratchedmetal", 0xFF555555);
    // LSAINTXT = CreateDynamicObject(2200, 1753.467773, -2556.054199, 19.296909, -0.000022, 0.000000, -89.999931, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 16640, "a51", "scratchedmetal", 0xFF555555);
    // LSAINTXT = CreateDynamicObject(2200, 1753.467773, -2558.264892, 19.296909, -0.000022, 0.000000, -89.999931, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 16640, "a51", "scratchedmetal", 0xFF555555);
    // LSAINTXT = CreateDynamicObject(11714, 1753.666381, -2546.988525, 20.556900, 0.000007, -0.000014, 179.999877, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 6284, "bev_law2", "lasjmposh2", 0x00000000);
    // LSAINTXT = CreateDynamicObject(11714, 1753.666381, -2549.968261, 20.556900, 0.000007, -0.000014, 179.999877, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 6284, "bev_law2", "lasjmposh2", 0x00000000);
    // LSAINTXT = CreateDynamicObject(18766, 1746.009033, -2499.942138, 16.426906, 0.000014, 89.999992, 134.999984, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    // SetDynamicObjectMaterial(LSAINTXT, 1, -1, "none", "none", 0xFF4E4945);
    // LSAINTXT = CreateDynamicObject(19787, 1745.213867, -2499.925292, 21.236919, -0.000014, 0.000004, -45.000003, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 1, 2772, "airp_prop", "CJ_AIRP_S_2", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19787, 1746.438476, -2501.148681, 21.236919, -0.000014, 0.000004, -45.000003, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 1, 2772, "airp_prop", "CJ_AIRP_S_1", 0x00000000);
    // LSAINTXT = CreateDynamicObject(18766, 1746.009033, -2499.942138, 26.396919, 0.000014, 89.999992, 134.999984, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    // SetDynamicObjectMaterial(LSAINTXT, 1, -1, "none", "none", 0xFF4E4945);
    // LSAINTXT = CreateDynamicObject(18981, 1744.219848, -2542.682861, 6.716884, 0.000000, -0.000007, 179.999954, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF08996E);
    // LSAINTXT = CreateDynamicObject(19552, 1799.590454, -2506.161621, 19.246967, 0.000000, 0.000007, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19377, 1731.900634, -2565.644531, 19.186920, 0.000000, 90.000015, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 14803, "bdupsnew", "Bdup2_Artex", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(18981, 1743.951171, -2573.560058, 18.956884, 0.000007, 0.000007, 0.000006, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16093, "a51_ext", "ws_whitewall2_top", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(18981, 1738.720947, -2567.618164, 18.956884, 0.000014, 0.000000, 89.999984, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16093, "a51_ext", "ws_whitewall2_top", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(18981, 1739.221191, -2561.290283, 6.756884, 0.000014, 0.000000, 89.999984, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16093, "a51_ext", "ws_whitewall2_top", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(18981, 1737.200073, -2561.290283, 6.756884, 0.000014, 0.000000, 179.999984, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16093, "a51_ext", "ws_whitewall2_top", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(18981, 1749.810180, -2549.303466, 6.716884, 0.000000, -0.000007, 269.999938, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    // LSAINTXT = CreateDynamicObject(8661, 1756.918334, -2478.910888, 19.286909, 0.000000, 0.000007, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 14803, "bdupsnew", "Bdup2_Artex", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(19377, 1742.389648, -2536.363769, 12.511907, 0.000067, 90.000015, 89.999763, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16639, "a51_labs", "dam_terazzo", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(18981, 1749.221313, -2530.582275, 6.716884, 0.000000, -0.000007, 269.999938, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    // LSAINTXT = CreateDynamicObject(19377, 1742.389648, -2546.865478, 12.511908, 0.000067, 90.000015, 89.999763, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16639, "a51_labs", "dam_terazzo", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(19443, 1737.626220, -2531.841552, 14.431911, 0.000022, -0.000067, 179.999496, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // LSAINTXT = CreateDynamicObject(19443, 1737.626220, -2540.902587, 14.431909, 0.000022, -0.000067, 179.999496, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // LSAINTXT = CreateDynamicObject(19443, 1737.626220, -2533.402587, 11.571908, 0.000022, -0.000081, 179.999404, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // LSAINTXT = CreateDynamicObject(19443, 1737.626220, -2539.443115, 11.571908, 0.000022, -0.000081, 179.999404, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // LSAINTXT = CreateDynamicObject(1499, 1737.666992, -2537.853515, 12.531911, 0.000067, 0.000029, 89.999763, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 19787, "samplcdtvs1", "samplcdtv1screen", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 7088, "casinoshops1", "247sign2", 0x00000000);
    // LSAINTXT = CreateDynamicObject(1499, 1737.647705, -2534.983154, 12.531911, -0.000067, -0.000029, -89.999916, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 19787, "samplcdtvs1", "samplcdtv1screen", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 7088, "casinoshops1", "247sign2", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2679, 1737.583251, -2534.176513, 12.417854, 0.000067, 360.000000, 89.999763, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 10101, "2notherbuildsfe", "ferry_build14", 0xFFFFFFFF);
    // SetDynamicObjectMaterial(LSAINTXT, 2, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // SetDynamicObjectMaterial(LSAINTXT, 3, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // LSAINTXT = CreateDynamicObject(2679, 1737.583251, -2538.637695, 12.667854, 0.000067, 180.000000, 89.999763, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 10101, "2notherbuildsfe", "ferry_build14", 0xFFFFFFFF);
    // SetDynamicObjectMaterial(LSAINTXT, 2, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // SetDynamicObjectMaterial(LSAINTXT, 3, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // LSAINTXT = CreateDynamicObject(19477, 1737.599853, -2533.577392, 14.091902, 0.000007, -0.000067, 179.999511, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x80777777);
    // LSAINTXT = CreateDynamicObject(19477, 1737.599853, -2533.577392, 15.481904, 0.000007, -0.000067, 179.999511, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x80777777);
    // LSAINTXT = CreateDynamicObject(2679, 1737.583251, -2534.176513, 14.817849, 0.000067, 360.000000, 89.999763, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 10101, "2notherbuildsfe", "ferry_build14", 0xFFFFFFFF);
    // SetDynamicObjectMaterial(LSAINTXT, 2, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // SetDynamicObjectMaterial(LSAINTXT, 3, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // LSAINTXT = CreateDynamicObject(2679, 1737.583251, -2538.637695, 15.077855, 0.000067, 180.000000, 89.999763, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 10101, "2notherbuildsfe", "ferry_build14", 0xFFFFFFFF);
    // SetDynamicObjectMaterial(LSAINTXT, 2, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // SetDynamicObjectMaterial(LSAINTXT, 3, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // LSAINTXT = CreateDynamicObject(19477, 1737.599853, -2536.427978, 15.691912, -0.000007, 179.999984, -0.000012, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x80777777);
    // LSAINTXT = CreateDynamicObject(19477, 1737.599853, -2540.009521, 14.091902, 0.000014, -0.000067, 179.999496, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x80777777);
    // LSAINTXT = CreateDynamicObject(19377, 1741.259521, -2536.344726, 16.111000, 0.000067, 90.000015, 89.999763, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 8391, "ballys01", "ballywall02_128", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19443, 1737.626220, -2531.841552, 10.941922, 0.000022, -0.000067, 179.999496, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // LSAINTXT = CreateDynamicObject(19443, 1737.626220, -2540.902587, 10.951911, 0.000022, -0.000067, 179.999496, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // LSAINTXT = CreateDynamicObject(2679, 1737.583251, -2533.546630, 16.267852, 0.000067, 270.000000, 89.999763, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 10101, "2notherbuildsfe", "ferry_build14", 0xFFFFFFFF);
    // SetDynamicObjectMaterial(LSAINTXT, 2, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // SetDynamicObjectMaterial(LSAINTXT, 3, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // LSAINTXT = CreateDynamicObject(2679, 1737.583251, -2539.005615, 16.267852, 0.000067, 270.000000, 89.999763, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 10101, "2notherbuildsfe", "ferry_build14", 0xFFFFFFFF);
    // SetDynamicObjectMaterial(LSAINTXT, 2, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // SetDynamicObjectMaterial(LSAINTXT, 3, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // LSAINTXT = CreateDynamicObject(19377, 1742.309814, -2536.363769, 16.311916, 0.000067, 90.000015, 89.999763, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // LSAINTXT = CreateDynamicObject(19786, 1736.552368, -2536.672607, 16.551908, -0.000067, -0.000029, -89.999916, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 1, 7088, "casinoshops1", "247sign1", 0x00000000);
    // LSAINTXT = CreateDynamicObject(18981, 1749.810180, -2504.512207, 6.716882, 0.000000, -0.000007, 269.999938, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    // LSAINTXT = CreateDynamicObject(19377, 1741.258544, -2537.544433, 16.101037, 0.000067, 90.000015, 89.999763, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 8391, "ballys01", "ballywall02_128", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2679, 1737.583251, -2536.827636, 15.287854, 0.000067, 450.000000, 89.999763, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 10101, "2notherbuildsfe", "ferry_build14", 0xFFFFFFFF);
    // SetDynamicObjectMaterial(LSAINTXT, 2, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // SetDynamicObjectMaterial(LSAINTXT, 3, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // LSAINTXT = CreateDynamicObject(2679, 1737.583251, -2536.277099, 15.287854, 0.000067, 450.000000, 89.999763, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 10101, "2notherbuildsfe", "ferry_build14", 0xFFFFFFFF);
    // SetDynamicObjectMaterial(LSAINTXT, 2, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // SetDynamicObjectMaterial(LSAINTXT, 3, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // LSAINTXT = CreateDynamicObject(19477, 1737.599853, -2540.009521, 15.481904, 0.000014, -0.000067, 179.999496, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x80777777);
    // LSAINTXT = CreateDynamicObject(2531, 1743.035156, -2541.515380, 12.597848, -0.000045, -0.000045, -90.000076, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 4, 14582, "casmafbar", "beerfridge128", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2531, 1743.035156, -2539.524414, 12.597848, -0.000045, -0.000051, -90.000122, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 4, 14582, "casmafbar", "beerfridge128", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2679, 1737.693115, -2534.176513, 12.417854, 0.000067, 360.000000, 89.999763, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 10101, "2notherbuildsfe", "ferry_build14", 0xFFFFFFFF);
    // SetDynamicObjectMaterial(LSAINTXT, 2, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // SetDynamicObjectMaterial(LSAINTXT, 3, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // LSAINTXT = CreateDynamicObject(2679, 1737.693115, -2534.176513, 14.857855, 0.000067, 360.000000, 89.999763, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 10101, "2notherbuildsfe", "ferry_build14", 0xFFFFFFFF);
    // SetDynamicObjectMaterial(LSAINTXT, 2, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // SetDynamicObjectMaterial(LSAINTXT, 3, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // LSAINTXT = CreateDynamicObject(2717, 1737.545166, -2531.901855, 14.291913, 0.000067, 0.000014, 89.999763, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2717, 1737.545166, -2540.951904, 14.291913, 0.000067, 0.000014, 89.999763, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2531, 1743.035156, -2540.535400, 12.597848, -0.000045, -0.000051, -90.000122, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 4, 14582, "casmafbar", "beerfridge128", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2531, 1743.035156, -2537.552734, 12.597848, -0.000045, -0.000059, -90.000167, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 4, 14582, "casmafbar", "beerfridge128", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2679, 1737.693115, -2538.637695, 12.667854, 0.000067, 180.000000, 89.999763, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 10101, "2notherbuildsfe", "ferry_build14", 0xFFFFFFFF);
    // SetDynamicObjectMaterial(LSAINTXT, 2, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // SetDynamicObjectMaterial(LSAINTXT, 3, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // LSAINTXT = CreateDynamicObject(2679, 1737.693115, -2538.637695, 15.087854, 0.000067, 180.000000, 89.999763, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 10101, "2notherbuildsfe", "ferry_build14", 0xFFFFFFFF);
    // SetDynamicObjectMaterial(LSAINTXT, 2, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // SetDynamicObjectMaterial(LSAINTXT, 3, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // LSAINTXT = CreateDynamicObject(2679, 1737.693115, -2533.546630, 16.267852, -0.000067, 270.000000, -89.999824, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 10101, "2notherbuildsfe", "ferry_build14", 0xFFFFFFFF);
    // SetDynamicObjectMaterial(LSAINTXT, 2, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // SetDynamicObjectMaterial(LSAINTXT, 3, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // LSAINTXT = CreateDynamicObject(2679, 1737.693115, -2539.936767, 16.267852, -0.000067, 270.000000, -89.999824, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 10101, "2notherbuildsfe", "ferry_build14", 0xFFFFFFFF);
    // SetDynamicObjectMaterial(LSAINTXT, 2, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // SetDynamicObjectMaterial(LSAINTXT, 3, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // LSAINTXT = CreateDynamicObject(2679, 1737.693115, -2535.945312, 16.067848, -0.000067, 270.000000, -89.999824, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 10101, "2notherbuildsfe", "ferry_build14", 0xFFFFFFFF);
    // SetDynamicObjectMaterial(LSAINTXT, 2, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // SetDynamicObjectMaterial(LSAINTXT, 3, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // LSAINTXT = CreateDynamicObject(2679, 1737.692626, -2536.845947, 16.067848, -0.000067, 270.000000, -89.999824, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 10101, "2notherbuildsfe", "ferry_build14", 0xFFFFFFFF);
    // SetDynamicObjectMaterial(LSAINTXT, 2, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // SetDynamicObjectMaterial(LSAINTXT, 3, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // LSAINTXT = CreateDynamicObject(1347, 1737.137451, -2534.450927, 13.072553, 0.000067, 0.000007, 89.999763, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 12984, "sw_block11", "shoptopb128", 0x00000000);
    // LSAINTXT = CreateDynamicObject(1347, 1737.137451, -2538.371337, 13.072553, 0.000067, 0.000007, 89.999763, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 12984, "sw_block11", "shoptopb128", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2658, 1737.506591, -2534.568115, 14.392558, -0.000067, -0.000006, -89.999778, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 1733, "cj_commercial", "CJ_SPRUNK_FRONT", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2658, 1737.506591, -2538.228759, 14.392558, -0.000067, -0.000006, -89.999778, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 1733, "cj_commercial", "CJ_SPRUNK_FRONT", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19475, 1737.577636, -2539.059326, 14.152557, 0.000007, -0.000067, 179.999542, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19475, 1737.577636, -2539.669921, 14.152557, 0.000007, -0.000067, 179.999542, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19475, 1737.577636, -2533.088378, 14.152557, 0.000014, -0.000067, 179.999511, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19475, 1737.577636, -2533.698974, 14.152557, 0.000014, -0.000067, 179.999511, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2531, 1743.035156, -2542.526367, 12.597848, -0.000045, -0.000045, -90.000076, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 4, 14582, "casmafbar", "beerfridge128", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2531, 1743.035156, -2538.563720, 12.597848, -0.000045, -0.000059, -90.000167, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 4, 14582, "casmafbar", "beerfridge128", 0x00000000);
    // LSAINTXT = CreateDynamicObject(1431, 1738.189941, -2539.103271, 12.597847, 0.000051, 0.000000, 89.999839, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2212, "burger_tray", "burgerkids", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19377, 1742.389648, -2548.892333, 10.941911, 0.000038, 360.000000, 89.999855, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF08996E);
    // LSAINTXT = CreateDynamicObject(19377, 1742.389648, -2531.160888, 10.941911, 0.000038, 360.000000, 89.999855, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF08996E);
    // LSAINTXT = CreateDynamicObject(2543, 1740.106689, -2537.405517, 12.597847, -0.000045, 0.000082, -89.999832, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 2, 7088, "casinoshops1", "247sign1", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2543, 1740.106689, -2538.395996, 12.597847, -0.000045, 0.000082, -89.999832, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 2, 7088, "casinoshops1", "247sign1", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2541, 1741.147216, -2538.396240, 12.597847, 0.000045, -0.000067, 89.999328, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 2212, "burger_tray", "sprinkler", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 2, 2212, "burger_tray", "burgerside", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2541, 1741.147216, -2537.405761, 12.597847, 0.000045, -0.000067, 89.999328, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 2212, "burger_tray", "sprinkler", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 2, 2212, "burger_tray", "burgerside", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2542, 1741.008789, -2546.109863, 12.597847, 0.000045, 0.000090, 89.999832, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2542, 1741.008789, -2545.149414, 12.597847, 0.000045, 0.000090, 89.999832, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2541, 1740.078613, -2545.149414, 12.597847, -0.000045, -0.000074, -90.000411, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2541, 1740.078613, -2546.109863, 12.597847, -0.000045, -0.000074, -90.000411, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2579, 1740.520996, -2546.742431, 13.497844, -0.000081, 0.000045, 0.000220, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFF22B1C);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 2, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 3, 7088, "casinoshops1", "247sign1", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 4, 18632, "fishingrod", "handle2", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 5, 19625, "ciggy1", "ciggy1", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 6, 19897, "cigarettepacks", "cigarettepack1", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2585, 1740.624511, -2536.772949, 13.497844, -0.000075, -0.000045, -179.999465, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFF22B1C);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 2, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 3, 7088, "casinoshops1", "247sign1", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 4, 18632, "fishingrod", "handle2", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 5, 19625, "ciggy1", "ciggy1", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 6, 19897, "cigarettepacks", "cigarettepack1", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19477, 1737.599853, -2543.139404, 14.091902, 0.000014, -0.000081, 179.999404, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x80777777);
    // LSAINTXT = CreateDynamicObject(2582, 1739.843505, -2533.843750, 13.077850, 0.000000, 0.000052, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 1, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 2, 7088, "casinoshops1", "GB_nastybar19", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 3, 12848, "cunte_town1", "sprunk_temp", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 5, 18264, "cw2_cinemablockcs_t", "GB_Last of Mullets", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 6, 15040, "cuntcuts", "piratesign01_128", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 7, 7088, "casinoshops1", "247sign1", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19477, 1737.599853, -2543.139404, 15.481904, 0.000014, -0.000081, 179.999404, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x80777777);
    // LSAINTXT = CreateDynamicObject(2543, 1740.106689, -2539.356933, 12.597847, -0.000045, 0.000090, -89.999832, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 2, 7088, "casinoshops1", "247sign1", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2543, 1740.106689, -2540.347412, 12.597847, -0.000045, 0.000090, -89.999832, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 2, 7088, "casinoshops1", "247sign1", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2541, 1741.147216, -2540.347656, 12.597847, 0.000045, -0.000075, 89.999282, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2541, 1741.147216, -2539.357177, 12.597847, 0.000045, -0.000075, 89.999282, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2542, 1741.008789, -2544.158447, 12.597847, 0.000045, 0.000096, 89.999832, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2542, 1741.008789, -2543.197998, 12.597847, 0.000045, 0.000096, 89.999832, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2541, 1740.078613, -2543.197998, 12.597847, -0.000045, -0.000081, -90.000457, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 2767, "cb_details", "pattern1_cb", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 2, 2767, "cb_details", "fillets_cb", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 3, 8618, "ceasersign", "ceaserssign01_128", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2541, 1740.078613, -2544.158447, 12.597847, -0.000045, -0.000081, -90.000457, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 2767, "cb_details", "pattern1_cb", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 2, 2767, "cb_details", "fillets_cb", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 3, 8618, "ceasersign", "ceaserssign01_128", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2582, 1738.393066, -2533.843750, 13.077850, 0.000000, 0.000052, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 2, 7088, "casinoshops1", "GB_nastybar19", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 3, 12848, "cunte_town1", "sprunk_temp", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 5, 18264, "cw2_cinemablockcs_t", "GB_Last of Mullets", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 6, 15040, "cuntcuts", "piratesign01_128", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 7, 7088, "casinoshops1", "247sign1", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2626, 1741.553833, -2533.254150, 13.007846, 0.000000, 0.000052, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 2, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 3, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2579, 1738.410888, -2531.374023, 14.357849, 0.000000, 0.000052, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFF22B1C);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 2, 18632, "fishingrod", "line", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 3, 7088, "casinoshops1", "247sign1", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 4, 18632, "fishingrod", "handle2", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 5, 19625, "ciggy1", "ciggy1", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 6, 19897, "cigarettepacks", "cigarettepack1", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2579, 1739.561035, -2531.374023, 14.357849, 0.000000, 0.000052, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFF22B1C);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 2, 18632, "fishingrod", "line", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 3, 7088, "casinoshops1", "247sign1", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 4, 18632, "fishingrod", "handle2", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 5, 19625, "ciggy1", "ciggy1", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 6, 19897, "cigarettepacks", "cigarettepack1", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2578, 1740.681152, -2531.374023, 14.357849, 0.000000, 0.000052, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFF22B1C);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 2, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 3, 7088, "casinoshops1", "247sign1", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 4, 18632, "fishingrod", "handle2", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 5, 19625, "ciggy1", "ciggy1", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 6, 19897, "cigarettepacks", "cigarettepack1", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2578, 1741.801025, -2531.374023, 14.357849, 0.000000, 0.000052, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFF22B1C);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 2, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 3, 7088, "casinoshops1", "247sign1", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 4, 18632, "fishingrod", "handle2", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 5, 19625, "ciggy1", "ciggy1", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 6, 19897, "cigarettepacks", "cigarettepack1", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19475, 1737.577636, -2542.189208, 14.152557, 0.000007, -0.000081, 179.999450, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19475, 1737.577636, -2542.799804, 14.152557, 0.000007, -0.000081, 179.999450, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19475, 1737.577636, -2543.579589, 14.152557, 0.000007, -0.000090, 179.999404, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19475, 1737.577636, -2544.190185, 14.152557, 0.000007, -0.000090, 179.999404, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19475, 1737.577636, -2545.010986, 14.152557, 0.000007, -0.000096, 179.999359, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19475, 1737.577636, -2545.621582, 14.152557, 0.000007, -0.000096, 179.999359, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19477, 1737.599853, -2545.900146, 14.091902, 0.000014, -0.000090, 179.999359, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x80777777);
    // LSAINTXT = CreateDynamicObject(19477, 1737.599853, -2545.900146, 15.481904, 0.000014, -0.000090, 179.999359, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x80777777);
    // LSAINTXT = CreateDynamicObject(19443, 1737.596923, -2543.443115, 12.556907, 90.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // LSAINTXT = CreateDynamicObject(19443, 1737.596923, -2546.893798, 12.556907, 90.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // LSAINTXT = CreateDynamicObject(19447, 1737.570922, -2534.958496, 17.266916, 0.000000, 0.000000, 180.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // LSAINTXT = CreateDynamicObject(2531, 1743.035156, -2543.497070, 12.597848, -0.000052, -0.000045, -90.000053, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 4, 14582, "casmafbar", "beerfridge128", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19373, 1737.596923, -2547.674560, 13.786911, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // LSAINTXT = CreateDynamicObject(19443, 1737.596923, -2550.364013, 16.346918, 89.999992, 89.999992, -89.999992, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // LSAINTXT = CreateDynamicObject(19377, 1741.259521, -2543.554443, 16.131002, 0.000067, 90.000015, 89.999763, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 8391, "ballys01", "ballywall02_128", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19447, 1737.570922, -2544.237548, 17.266916, 0.000000, 0.000000, 180.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    // LSAINTXT = CreateDynamicObject(2531, 1743.035156, -2544.508056, 12.597848, -0.000052, -0.000045, -90.000053, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 4, 14582, "casmafbar", "beerfridge128", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19447, 1736.791503, -2544.017333, 17.796928, 0.000000, 0.000000, 180.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    // LSAINTXT = CreateDynamicObject(19447, 1736.790039, -2534.927246, 17.796928, 0.000000, 0.000000, 180.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    // LSAINTXT = CreateDynamicObject(18981, 1744.331420, -2492.300781, 6.706881, 0.000000, -0.000007, 179.999954, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    // LSAINTXT = CreateDynamicObject(8661, 1756.918334, -2458.922119, 19.286909, 0.000000, 0.000007, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 14803, "bdupsnew", "Bdup2_Artex", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(18981, 1756.341918, -2473.891601, 18.956884, 0.000022, 0.000000, 89.999931, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16093, "a51_ext", "ws_whitewall2_top", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(19937, 1746.666015, -2477.326904, 19.432029, 89.999992, 269.829803, -89.829635, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(19939, 1746.421386, -2476.170898, 20.282922, 0.000033, 89.999977, 179.999496, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    // LSAINTXT = CreateDynamicObject(19940, 1746.620849, -2475.661132, 19.502918, 89.999992, 449.829772, -89.829635, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    // LSAINTXT = CreateDynamicObject(19940, 1746.621582, -2477.341552, 19.502922, 89.999992, 449.829772, -89.829635, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    // LSAINTXT = CreateDynamicObject(19937, 1746.635009, -2475.691650, 19.252660, 79.999984, 0.000392, -0.000387, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(19937, 1746.635253, -2477.344482, 19.249666, 79.999984, -0.000392, -179.999374, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(19477, 1746.365234, -2476.511474, 19.822931, 0.000033, -0.000014, 179.999542, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterialText(LSAINTXT, 0, "CHECK-IN", 130, "Palatino Linotype", 30, 1, 0xFF333333, 0x00000000, 1);
    // LSAINTXT = CreateDynamicObject(19939, 1746.421386, -2476.861572, 20.282922, 0.000033, 89.999977, 179.999496, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    // LSAINTXT = CreateDynamicObject(19937, 1746.666259, -2476.696289, 19.432928, 89.999992, 269.829803, -89.829635, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(19939, 1746.627075, -2476.470214, 19.666912, 0.000000, 0.000037, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF00B8D4);
    // LSAINTXT = CreateDynamicObject(18980, 1746.929687, -2480.467773, 20.551191, 14.999925, 5.000020, -89.999908, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    // LSAINTXT = CreateDynamicObject(18980, 1746.851806, -2478.851562, 20.534702, 14.999925, -4.999976, -89.999908, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    // LSAINTXT = CreateDynamicObject(18980, 1747.556884, -2479.634277, 20.646919, 23.299915, 0.000024, -89.999908, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19937, 1746.666015, -2483.637207, 19.432029, 89.999992, 269.914978, -89.914794, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(19939, 1746.421386, -2482.481201, 20.282922, 0.000033, 89.999969, 179.999450, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    // LSAINTXT = CreateDynamicObject(19940, 1746.620849, -2481.971435, 19.502918, 89.999992, 449.914947, -89.914794, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    // LSAINTXT = CreateDynamicObject(19940, 1746.621582, -2483.651855, 19.502922, 89.999992, 449.914947, -89.914794, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    // LSAINTXT = CreateDynamicObject(19937, 1746.635009, -2482.001953, 19.252660, 79.999984, 0.000437, -0.000430, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(19937, 1746.635253, -2483.654785, 19.249666, 79.999984, -0.000437, -179.999298, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(2667, 1744.638183, -2531.281005, 14.387848, 0.000000, 0.000052, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2212, "burger_tray", "sprunk_cb", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2667, 1745.979248, -2531.281005, 14.387848, 0.000000, 0.000052, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19918, 1742.202148, -2533.426513, 13.517853, 0.000000, 0.000060, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2221, "donut_tray", "rustycoffeerap_rb", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19918, 1742.202148, -2533.106201, 13.517853, -0.000004, 0.000059, -5.499998, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2221, "donut_tray", "rustycoffeerap_rb", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19477, 1746.365234, -2482.821777, 19.822931, 0.000033, -0.000020, 179.999496, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterialText(LSAINTXT, 0, "CHECK-IN", 130, "Palatino Linotype", 30, 1, 0xFF333333, 0x00000000, 1);
    // LSAINTXT = CreateDynamicObject(18066, 1743.587768, -2539.194091, 15.417853, 0.000052, 0.000000, 89.999839, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 7088, "casinoshops1", "crapdoor1_256", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19939, 1746.421386, -2483.171875, 20.282922, 0.000033, 89.999969, 179.999450, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    // LSAINTXT = CreateDynamicObject(19937, 1746.666259, -2483.006591, 19.432928, 89.999992, 269.914978, -89.914794, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(11727, 1741.538330, -2540.490966, 16.037849, 89.999992, 90.000091, -89.999961, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // LSAINTXT = CreateDynamicObject(11727, 1741.538330, -2537.340576, 16.037849, 89.999992, 90.000091, -89.999961, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // LSAINTXT = CreateDynamicObject(11727, 1741.538330, -2533.530517, 16.037849, 89.999992, 90.000076, -89.999961, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // LSAINTXT = CreateDynamicObject(11727, 1745.189453, -2540.490966, 15.847847, 89.999992, 90.000122, -89.999961, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // LSAINTXT = CreateDynamicObject(11727, 1745.189453, -2537.340576, 15.847847, 89.999992, 90.000122, -89.999961, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // LSAINTXT = CreateDynamicObject(11727, 1745.189453, -2533.530517, 15.847847, 89.999992, 90.000106, -89.999961, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19939, 1746.627075, -2482.780517, 19.666912, 0.000000, 0.000044, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF00B8D4);
    // LSAINTXT = CreateDynamicObject(19834, 1743.565429, -2537.083251, 15.937847, 0.000059, 0.000000, 89.999816, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19834, 1743.565429, -2539.413818, 15.937847, 0.000059, 0.000000, 89.999816, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19834, 1743.575439, -2540.424316, 15.937847, 0.000059, 0.000000, 89.999816, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    // LSAINTXT = CreateDynamicObject(18980, 1746.929687, -2486.778076, 20.551191, 14.999917, 5.000020, -89.999885, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    // LSAINTXT = CreateDynamicObject(18980, 1746.851806, -2485.161865, 20.534702, 14.999917, -4.999976, -89.999885, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    // LSAINTXT = CreateDynamicObject(18980, 1747.556884, -2485.944580, 20.646919, 23.299905, 0.000024, -89.999885, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19937, 1746.666015, -2490.009033, 19.432029, 89.999992, 269.914978, -89.914794, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(19939, 1746.421386, -2488.853027, 20.282922, 0.000033, 89.999969, 179.999450, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    // LSAINTXT = CreateDynamicObject(19940, 1746.620849, -2488.343261, 19.502918, 89.999992, 449.914947, -89.914794, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    // LSAINTXT = CreateDynamicObject(19940, 1746.621582, -2490.023681, 19.502922, 89.999992, 449.914947, -89.914794, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    // LSAINTXT = CreateDynamicObject(19937, 1746.635009, -2488.373779, 19.252660, 79.999984, 0.000437, -0.000430, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(19937, 1746.635253, -2490.026611, 19.249666, 79.999984, -0.000437, -179.999298, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(19477, 1746.365234, -2489.193603, 19.822931, 0.000033, -0.000020, 179.999496, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterialText(LSAINTXT, 0, "CHECK-IN", 130, "Palatino Linotype", 30, 1, 0xFF333333, 0x00000000, 1);
    // LSAINTXT = CreateDynamicObject(19939, 1746.421386, -2489.543701, 20.282922, 0.000033, 89.999969, 179.999450, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    // LSAINTXT = CreateDynamicObject(19937, 1746.666259, -2489.378417, 19.432928, 89.999992, 269.914978, -89.914794, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(19939, 1746.627075, -2489.152343, 19.666912, 0.000000, 0.000044, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF00B8D4);
    // LSAINTXT = CreateDynamicObject(18980, 1746.929687, -2493.149902, 20.551191, 14.999917, 5.000020, -89.999885, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    // LSAINTXT = CreateDynamicObject(18980, 1746.851806, -2491.533691, 20.534702, 14.999917, -4.999976, -89.999885, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    // LSAINTXT = CreateDynamicObject(18980, 1747.556884, -2492.316406, 20.646919, 23.299905, 0.000024, -89.999885, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19937, 1746.666015, -2496.319335, 19.432029, 89.999992, 269.957580, -89.957374, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(19939, 1746.421386, -2495.163330, 20.282922, 0.000033, 89.999961, 179.999404, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    // LSAINTXT = CreateDynamicObject(19940, 1746.620849, -2494.653564, 19.502918, 89.999992, 449.957550, -89.957374, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    // LSAINTXT = CreateDynamicObject(19940, 1746.621582, -2496.333984, 19.502922, 89.999992, 449.957550, -89.957374, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    // LSAINTXT = CreateDynamicObject(19937, 1746.635009, -2494.684082, 19.252660, 79.999984, 0.000480, -0.000475, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(19937, 1746.635253, -2496.336914, 19.249666, 79.999984, -0.000480, -179.999221, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(19477, 1746.365234, -2495.503906, 19.822931, 0.000033, -0.000028, 179.999450, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterialText(LSAINTXT, 0, "CHECK-IN", 130, "Palatino Linotype", 30, 1, 0xFF333333, 0x00000000, 1);
    // LSAINTXT = CreateDynamicObject(19939, 1746.421386, -2495.854003, 20.282922, 0.000033, 89.999961, 179.999404, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    // LSAINTXT = CreateDynamicObject(19937, 1746.666259, -2495.688720, 19.432928, 89.999992, 269.957580, -89.957374, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(19939, 1746.627075, -2495.462646, 19.666912, 0.000000, 0.000051, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF00B8D4);
    // LSAINTXT = CreateDynamicObject(18981, 1754.341430, -2486.760986, 18.956884, 0.000000, -0.000022, 179.999862, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16093, "a51_ext", "ws_whitewall2_top", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(18981, 1754.211303, -2486.730957, 12.206885, 0.000000, -0.000022, 179.999862, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    // LSAINTXT = CreateDynamicObject(18980, 1754.192626, -2486.729736, 20.956918, 0.000022, 90.000000, 89.999931, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16640, "a51", "scratchedmetal", 0xFF777777);
    // LSAINTXT = CreateDynamicObject(18980, 1756.532592, -2473.987304, 20.956918, 0.000000, 89.999977, 179.999862, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16640, "a51", "scratchedmetal", 0xFF777777);
    // LSAINTXT = CreateDynamicObject(18981, 1756.372436, -2498.019287, 18.956884, -0.000022, 0.000000, -89.999900, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16093, "a51_ext", "ws_whitewall2_top", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(18981, 1756.612792, -2497.941650, 12.206885, 0.000022, 0.000000, 89.999870, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    // LSAINTXT = CreateDynamicObject(18980, 1756.762329, -2497.923583, 20.956918, 0.000000, 90.000022, -0.000059, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16640, "a51", "scratchedmetal", 0xFF777777);
    // LSAINTXT = CreateDynamicObject(2200, 1753.467773, -2474.943603, 19.296909, -0.000022, 0.000000, -89.999931, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 16640, "a51", "scratchedmetal", 0xFF555555);
    // LSAINTXT = CreateDynamicObject(2200, 1753.467773, -2477.154296, 19.296909, -0.000022, 0.000000, -89.999931, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 16640, "a51", "scratchedmetal", 0xFF555555);
    // LSAINTXT = CreateDynamicObject(2200, 1753.467773, -2479.365478, 19.296909, -0.000029, 0.000000, -89.999908, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 16640, "a51", "scratchedmetal", 0xFF555555);
    // LSAINTXT = CreateDynamicObject(2200, 1753.467773, -2491.282226, 19.296909, -0.000029, 0.000000, -89.999908, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 16640, "a51", "scratchedmetal", 0xFF555555);
    // LSAINTXT = CreateDynamicObject(2200, 1753.467773, -2493.493408, 19.296909, -0.000037, 0.000000, -89.999885, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 16640, "a51", "scratchedmetal", 0xFF555555);
    // LSAINTXT = CreateDynamicObject(2200, 1753.467773, -2495.704101, 19.296909, -0.000037, 0.000000, -89.999885, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 16640, "a51", "scratchedmetal", 0xFF555555);
    // LSAINTXT = CreateDynamicObject(11714, 1753.666381, -2484.427734, 20.556900, 0.000007, -0.000029, 179.999786, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 6284, "bev_law2", "lasjmposh2", 0x00000000);
    // LSAINTXT = CreateDynamicObject(11714, 1753.666381, -2487.407470, 20.556900, 0.000007, -0.000029, 179.999786, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 6284, "bev_law2", "lasjmposh2", 0x00000000);
    // LSAINTXT = CreateDynamicObject(18981, 1743.951171, -2461.899169, 18.956884, 0.000007, 0.000022, 0.000004, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16093, "a51_ext", "ws_whitewall2_top", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(18981, 1756.312622, -2473.959960, 12.206885, 0.000022, 0.000000, 89.999870, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    // LSAINTXT = CreateDynamicObject(19377, 1732.551269, -2468.989746, 19.186920, 0.000000, 90.000015, 179.999893, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 14803, "bdupsnew", "Bdup2_Artex", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(8661, 1736.300537, -2464.970458, 12.606905, 0.000014, 0.000000, 89.999931, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 6882, "vgnland", "hiwaygravel1_256", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(18980, 1736.791015, -2485.411132, 19.000000, -0.000014, 90.000000, -90.000007, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    // LSAINTXT = CreateDynamicObject(18763, 1735.811767, -2560.581054, 17.012849, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    // LSAINTXT = CreateDynamicObject(18763, 1735.811767, -2560.581054, 12.102846, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    // LSAINTXT = CreateDynamicObject(18763, 1728.940917, -2560.581054, 17.012849, 0.000000, 0.000014, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    // LSAINTXT = CreateDynamicObject(18763, 1728.940917, -2560.581054, 12.102846, 0.000000, 0.000014, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    // LSAINTXT = CreateDynamicObject(18763, 1735.811767, -2474.409423, 17.012849, 0.000000, 0.000007, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    // LSAINTXT = CreateDynamicObject(18763, 1735.811767, -2474.409423, 12.102846, 0.000000, 0.000007, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    // LSAINTXT = CreateDynamicObject(18763, 1728.940917, -2474.409423, 17.012849, 0.000000, 0.000022, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    // LSAINTXT = CreateDynamicObject(18763, 1728.940917, -2474.409423, 12.102846, 0.000000, 0.000022, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    // LSAINTXT = CreateDynamicObject(18981, 1739.221191, -2467.229248, 18.956884, 0.000014, 0.000000, 89.999984, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16093, "a51_ext", "ws_whitewall2_top", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(19913, 1726.325439, -2542.578613, 15.886907, 0.000007, 0.000000, 89.999977, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x0FFFFFFF);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 14581, "ab_mafiasuitea", "barbersmir1", 0x90BBBBBB);
    // LSAINTXT = CreateDynamicObject(1499, 1737.646972, -2495.911865, 12.531909, 0.000096, 0.000029, 89.999671, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 2732, "cj_zip_sign", "CJ_ZIP_2", 0x00000000);
    // LSAINTXT = CreateDynamicObject(1499, 1737.627685, -2493.041503, 12.531909, -0.000096, -0.000027, -89.999824, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 2732, "cj_zip_sign", "CJ_ZIP_2", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19477, 1737.579833, -2494.486328, 15.691910, -0.000007, 179.999984, -0.000011, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x80777777);
    // LSAINTXT = CreateDynamicObject(1897, 1737.596191, -2494.040771, 15.181913, -89.999992, 90.001960, 90.001632, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // LSAINTXT = CreateDynamicObject(1897, 1737.606201, -2494.921386, 15.181913, -89.999992, -89.998077, 90.001609, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // LSAINTXT = CreateDynamicObject(1897, 1737.596191, -2494.040771, 15.371914, -89.999992, 90.001754, 90.001403, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // LSAINTXT = CreateDynamicObject(1897, 1737.606201, -2494.921386, 15.371914, -89.999992, -89.998245, 90.001388, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // LSAINTXT = CreateDynamicObject(1897, 1737.612548, -2492.930175, 12.581977, 0.000018, 180.197845, -179.801406, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // LSAINTXT = CreateDynamicObject(1897, 1737.604736, -2492.930175, 14.771965, 0.000018, 180.197845, -179.801406, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // LSAINTXT = CreateDynamicObject(1897, 1737.634521, -2496.011962, 13.521950, 0.000000, 180.197845, 0.197916, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // LSAINTXT = CreateDynamicObject(1897, 1737.636962, -2496.011962, 14.131953, 0.000000, 180.197845, 0.197916, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19443, 1737.612792, -2496.747558, 14.246907, 0.000000, 0.000037, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    // LSAINTXT = CreateDynamicObject(19443, 1737.612792, -2492.237304, 14.246907, 0.000000, 0.000037, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    // LSAINTXT = CreateDynamicObject(19443, 1737.612060, -2493.176269, 16.116907, 89.999992, 90.000030, -89.999961, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    // LSAINTXT = CreateDynamicObject(19443, 1737.611083, -2495.796875, 16.116907, 89.999992, 90.000030, -89.999961, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    // LSAINTXT = CreateDynamicObject(1897, 1737.615722, -2491.490722, 14.011976, 0.000019, 180.197845, 0.198364, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // LSAINTXT = CreateDynamicObject(1897, 1737.621093, -2491.490722, 15.461972, 0.000019, 180.197845, 0.198364, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // LSAINTXT = CreateDynamicObject(1897, 1737.571655, -2488.928955, 13.261343, 0.000004, 180.197845, -89.801406, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // LSAINTXT = CreateDynamicObject(1897, 1737.571289, -2488.908935, 15.491713, -0.000037, 0.197876, -89.801506, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // LSAINTXT = CreateDynamicObject(1897, 1737.610473, -2489.969238, 16.481977, 89.999992, 285.132843, -104.736526, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // LSAINTXT = CreateDynamicObject(1897, 1737.605346, -2492.199218, 16.481977, 89.999992, 75.467193, -75.070930, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // LSAINTXT = CreateDynamicObject(1897, 1737.643676, -2491.768798, 13.031975, -89.999992, 271.241851, 90.845504, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // LSAINTXT = CreateDynamicObject(1897, 1737.625366, -2489.969238, 13.031975, -89.999992, 91.055061, 90.658668, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // LSAINTXT = CreateDynamicObject(1897, 1737.571655, -2488.178466, 13.261343, -0.000000, 180.197845, -89.801383, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19325, 1737.578857, -2488.061035, 15.016908, 0.000000, 179.999984, -179.999984, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x90FFFFFF);
    // LSAINTXT = CreateDynamicObject(1897, 1737.571289, -2488.158447, 15.491713, -0.000045, 0.197876, -89.801483, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // LSAINTXT = CreateDynamicObject(1897, 1737.621093, -2485.538330, 14.011976, 0.000018, 180.197845, -179.801422, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // LSAINTXT = CreateDynamicObject(1897, 1737.615722, -2485.538330, 15.461972, 0.000018, 180.197845, -179.801422, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // LSAINTXT = CreateDynamicObject(1897, 1737.610473, -2486.708740, 16.481977, 89.999992, 277.884460, -97.488128, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // LSAINTXT = CreateDynamicObject(1897, 1737.585327, -2488.938720, 16.481977, 89.999992, 532.807128, 7.589134, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // LSAINTXT = CreateDynamicObject(1897, 1737.669311, -2487.787597, 13.041972, -89.999992, 272.087005, 91.690689, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // LSAINTXT = CreateDynamicObject(1897, 1737.655395, -2486.708740, 13.031975, -89.999992, 91.713577, 91.317207, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19443, 1737.612060, -2487.325683, 17.406906, 89.999992, 90.000030, -89.999961, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    // LSAINTXT = CreateDynamicObject(19443, 1737.622070, -2487.281982, 12.116907, 89.999992, 90.000015, -89.999961, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    // LSAINTXT = CreateDynamicObject(19443, 1737.622070, -2490.761718, 12.116907, 89.999992, 90.000015, -89.999961, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    // LSAINTXT = CreateDynamicObject(19443, 1737.611450, -2490.794677, 17.406906, 89.999992, 90.000030, -89.999961, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    // LSAINTXT = CreateDynamicObject(19443, 1737.611450, -2494.284423, 17.406906, 89.999992, 90.000030, -89.999961, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    // LSAINTXT = CreateDynamicObject(19443, 1737.611450, -2497.763916, 17.406906, 89.999992, 90.000030, -89.999961, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    // LSAINTXT = CreateDynamicObject(19443, 1737.611450, -2501.264404, 17.406906, 89.999992, 90.000030, -89.999961, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    // LSAINTXT = CreateDynamicObject(19443, 1737.614990, -2502.903564, 17.406906, 89.999992, 90.000030, -89.999961, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    // LSAINTXT = CreateDynamicObject(1897, 1737.615722, -2503.453369, 14.011976, 0.000019, 180.197845, 0.198364, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // LSAINTXT = CreateDynamicObject(1897, 1737.621093, -2503.453369, 15.461972, 0.000019, 180.197845, 0.198364, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // LSAINTXT = CreateDynamicObject(1897, 1737.621093, -2500.761474, 14.011976, 0.000019, 180.197845, -179.801422, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // LSAINTXT = CreateDynamicObject(1897, 1737.615722, -2500.951660, 15.461972, -0.000014, 0.197882, 0.198424, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // LSAINTXT = CreateDynamicObject(1897, 1737.610473, -2501.931884, 16.481977, 89.999992, 277.884460, -97.488128, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // LSAINTXT = CreateDynamicObject(1897, 1737.599975, -2503.481445, 16.481977, 89.999992, 82.807128, -82.410865, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // LSAINTXT = CreateDynamicObject(1897, 1737.645874, -2503.541259, 13.031975, -89.999992, 270.819122, 90.422752, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // LSAINTXT = CreateDynamicObject(1897, 1737.625366, -2501.931884, 13.031975, -89.999992, 90.725738, 90.329322, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // LSAINTXT = CreateDynamicObject(1897, 1737.615722, -2500.192871, 14.011976, 0.000000, 180.197845, 0.198364, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19325, 1737.578857, -2500.023681, 15.016908, 0.000000, 179.999984, -179.999938, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x90FFFFFF);
    // LSAINTXT = CreateDynamicObject(1897, 1737.620727, -2500.002685, 15.461972, -0.000014, 360.197814, -179.801483, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // LSAINTXT = CreateDynamicObject(1897, 1737.621093, -2497.500976, 14.011976, 0.000018, 180.197845, -179.801376, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // LSAINTXT = CreateDynamicObject(1897, 1737.615722, -2497.500976, 15.461972, 0.000018, 180.197845, -179.801376, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // LSAINTXT = CreateDynamicObject(1897, 1737.610473, -2498.671386, 16.481977, 89.999992, 274.156311, -93.759963, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // LSAINTXT = CreateDynamicObject(1897, 1737.605346, -2500.901367, 16.481977, 89.999992, 86.585121, -86.188842, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // LSAINTXT = CreateDynamicObject(1897, 1737.669311, -2499.750244, 13.041972, -89.999992, 271.241851, 90.845504, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // LSAINTXT = CreateDynamicObject(1897, 1737.655395, -2498.671386, 13.031975, -89.999992, 91.055061, 90.658668, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19443, 1737.612792, -2504.188964, 14.246907, 0.000000, 0.000037, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    // LSAINTXT = CreateDynamicObject(19443, 1737.612792, -2504.188964, 17.526901, 0.000000, 0.000037, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    // LSAINTXT = CreateDynamicObject(19443, 1737.612792, -2484.807617, 14.246907, 0.000000, 0.000045, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    // LSAINTXT = CreateDynamicObject(19443, 1737.612792, -2484.807617, 17.526901, 0.000000, 0.000045, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    // LSAINTXT = CreateDynamicObject(19443, 1737.622070, -2499.158447, 12.116907, 89.999992, 90.000030, -89.999961, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    // LSAINTXT = CreateDynamicObject(19443, 1737.622070, -2502.638183, 12.116907, 89.999992, 90.000030, -89.999961, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    // LSAINTXT = CreateDynamicObject(19377, 1742.495849, -2485.030517, 14.026923, 0.000000, 0.000000, 90.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    // LSAINTXT = CreateDynamicObject(19377, 1742.495849, -2504.070312, 14.026923, 0.000000, 0.000000, 90.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    // LSAINTXT = CreateDynamicObject(18981, 1750.162109, -2492.300781, 12.106882, 0.000000, 89.999992, 179.999954, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 14865, "gf2", "mp_bobbie_wall", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19805, 1740.689697, -2485.224365, 14.906887, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2732, "cj_zip_sign", "CJ_ZIP_2", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2394, 1739.364379, -2485.582031, 13.326889, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2374, "cloth_track_t", "tshirterisyell", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 2374, "cloth_track_t", "tshirterisyell", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 2, 2374, "cloth_track_t", "tshirterisyell", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2394, 1739.364379, -2486.252685, 13.326889, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2374, "cloth_track_t", "trackytop1pro", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 2374, "cloth_track_t", "trackytop1pro", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 2, 2374, "cloth_track_t", "trackytop1pro", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2394, 1741.965820, -2485.582031, 13.326889, 0.000000, 0.000007, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2374, "cloth_track_t", "shirtshortblu", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 2374, "cloth_track_t", "shirtshortblu", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 2, 2374, "cloth_track_t", "shirtshortblu", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2394, 1741.965820, -2486.252685, 13.326889, 0.000000, 0.000007, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2374, "cloth_track_t", "sweatrstar", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 2374, "cloth_track_t", "sweatrstar", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 2, 2374, "cloth_track_t", "sweatrstar", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2411, 1738.738281, -2490.238281, 13.966883, -0.000007, 0.000000, -89.999977, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 14668, "711c", "gun_ceiling1128", 0x00000000);
    // LSAINTXT = CreateDynamicObject(18762, 1738.722534, -2490.267333, 11.166880, 0.000000, 0.000007, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2411, 1738.738281, -2487.877197, 13.966883, -0.000014, 0.000000, -89.999954, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 14668, "711c", "gun_ceiling1128", 0x00000000);
    // LSAINTXT = CreateDynamicObject(18762, 1738.722534, -2487.906250, 11.166880, 0.000000, 0.000014, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19805, 1740.689697, -2503.935546, 14.906887, 0.000000, 0.000022, 180.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2732, "cj_zip_sign", "CJ_ZIP_2", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2394, 1739.364379, -2502.812988, 13.326889, 0.000000, 0.000022, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2374, "cloth_track_t", "tshirterisyell", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 2374, "cloth_track_t", "tshirterisyell", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 2, 2374, "cloth_track_t", "tshirterisyell", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2394, 1739.364379, -2503.483642, 13.326889, 0.000000, 0.000022, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2374, "cloth_track_t", "trackytop1pro", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 2374, "cloth_track_t", "trackytop1pro", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 2, 2374, "cloth_track_t", "trackytop1pro", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2394, 1741.965820, -2502.812988, 13.326889, 0.000000, 0.000029, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2374, "cloth_track_t", "shirtshortblu", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 2374, "cloth_track_t", "shirtshortblu", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 2, 2374, "cloth_track_t", "shirtshortblu", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2394, 1741.965820, -2503.483642, 13.326889, 0.000000, 0.000029, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2374, "cloth_track_t", "sweatrstar", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 2374, "cloth_track_t", "sweatrstar", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 2, 2374, "cloth_track_t", "sweatrstar", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2411, 1738.738281, -2501.038085, 13.966883, -0.000014, 0.000000, -89.999954, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 14668, "711c", "gun_ceiling1128", 0x00000000);
    // LSAINTXT = CreateDynamicObject(18762, 1738.722534, -2501.067138, 11.166880, 0.000000, 0.000014, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2411, 1738.738281, -2498.677001, 13.966883, -0.000022, 0.000000, -89.999931, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 14668, "711c", "gun_ceiling1128", 0x00000000);
    // LSAINTXT = CreateDynamicObject(18762, 1738.722534, -2498.706054, 11.166880, 0.000000, 0.000022, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19937, 1742.252685, -2495.393066, 12.606884, 0.000000, 0.000014, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(19937, 1742.252685, -2493.524902, 12.606884, 0.000000, 0.000014, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(19937, 1741.982421, -2495.453125, 12.856884, 0.000000, 0.000007, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(19937, 1741.982421, -2493.532958, 12.856884, 0.000000, 0.000007, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(19861, 1743.832519, -2494.597900, 16.476882, 0.000000, 0.000000, -90.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterialText(LSAINTXT, 0, "Z I P", 130, "Palatino Linotype", 120, 1, 0xFFFFFFFF, 0x00000000, 1);
    // LSAINTXT = CreateDynamicObject(2400, 1743.755126, -2498.493408, 12.606884, 0.000000, 0.000000, -90.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 2423, "cj_ff_counters", "CJ_Laminate1", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2400, 1743.745117, -2498.493408, 10.946882, 0.000000, 0.000000, -90.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 16640, "a51", "scratchedmetal", 0x00000000);
    // LSAINTXT = CreateDynamicObject(18066, 1743.770385, -2499.957763, 14.946889, 0.000000, 0.000000, 90.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2732, "cj_zip_sign", "CJ_ZIP_2", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 16640, "a51", "scratchedmetal", 0x00000000);
    // LSAINTXT = CreateDynamicObject(1297, 1744.324096, -2498.189941, 16.156883, 180.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 19480, "signsurf", "sign", 0x00000000);
    // LSAINTXT = CreateDynamicObject(1297, 1744.324096, -2501.431640, 16.156883, 180.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 19480, "signsurf", "sign", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2400, 1743.755126, -2487.621582, 12.606884, -0.000007, 0.000000, -89.999977, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 2423, "cj_ff_counters", "CJ_Laminate1", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2400, 1743.745117, -2487.621582, 10.946882, -0.000007, 0.000000, -89.999977, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 16640, "a51", "scratchedmetal", 0x00000000);
    // LSAINTXT = CreateDynamicObject(18066, 1743.770385, -2489.085937, 14.946889, 0.000007, 0.000000, 89.999977, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2732, "cj_zip_sign", "CJ_ZIP_2", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 16640, "a51", "scratchedmetal", 0x00000000);
    // LSAINTXT = CreateDynamicObject(1297, 1744.324096, -2487.318115, 16.156883, 0.000000, 179.999984, -179.999984, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 19480, "signsurf", "sign", 0x00000000);
    // LSAINTXT = CreateDynamicObject(1297, 1744.324096, -2490.559814, 16.156883, 0.000000, 179.999984, -179.999984, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 19480, "signsurf", "sign", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19373, 1740.395019, -2496.505859, 17.586887, 0.000000, 90.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19373, 1740.395019, -2499.665771, 17.586887, 0.000000, 90.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19373, 1740.395019, -2493.325195, 17.586887, 0.000000, 90.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19373, 1740.395019, -2490.164794, 17.586887, 0.000000, 90.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19860, 1743.844726, -2491.290527, 13.826884, 0.000000, 0.000000, -90.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 12978, "ce_payspray", "sf_spray2", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2654, 1743.553222, -2497.151855, 12.776885, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2732, "cj_zip_sign", "CJ_ZIP_2", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 2732, "cj_zip_sign", "CJ_ZIP_2", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 2, 2732, "cj_zip_sign", "CJ_ZIP_2", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2654, 1743.553222, -2502.753417, 12.776885, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2732, "cj_zip_sign", "CJ_ZIP_2", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 2732, "cj_zip_sign", "CJ_ZIP_2", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 2, 2732, "cj_zip_sign", "CJ_ZIP_2", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19805, 1737.490966, -2494.405273, 16.766897, 0.000000, 0.000022, 270.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2875, "cj_gash", "CJ_ZIP_3", 0x00000000);
    // LSAINTXT = CreateDynamicObject(18981, 1751.659790, -2528.473388, 6.716882, 0.000000, -0.000007, 359.999938, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16093, "a51_ext", "ws_whitewall2_top", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(8661, 1756.299682, -2508.684570, 12.596905, 0.000014, 0.000000, 89.999931, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 14803, "bdupsnew", "Bdup2_Artex", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(18981, 1726.339721, -2478.061279, 1.436879, 0.000000, -0.000007, 359.999938, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16093, "a51_ext", "ws_whitewall2_top", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(18981, 1726.339721, -2503.030517, 1.436879, 0.000000, -0.000007, 359.999938, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16093, "a51_ext", "ws_whitewall2_top", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(18981, 1726.339721, -2528.012207, 1.436879, 0.000000, 0.000000, -0.000060, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16093, "a51_ext", "ws_whitewall2_top", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(18981, 1726.339721, -2552.981445, 1.436879, 0.000000, 0.000000, -0.000060, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16093, "a51_ext", "ws_whitewall2_top", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(18981, 1726.349487, -2556.691162, 8.956882, 0.000000, 0.000000, -0.000060, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16093, "a51_ext", "ws_whitewall2_top", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(18981, 1726.349487, -2478.179443, 8.956882, 0.000000, 0.000000, -0.000060, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16093, "a51_ext", "ws_whitewall2_top", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(18981, 1737.200073, -2472.710937, 6.756863, 0.000014, 0.000000, 179.999984, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16093, "a51_ext", "ws_whitewall2_top", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(19913, 1726.325439, -2492.586914, 15.886907, 0.000007, 0.000000, 89.999977, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x0FFFFFFF);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 14581, "ab_mafiasuitea", "barbersmir1", 0x90BBBBBB);
    // LSAINTXT = CreateDynamicObject(19913, 1727.296264, -2542.578613, 26.706945, 10.000043, 0.000007, 89.999893, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x0FFFFFFF);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 14581, "ab_mafiasuitea", "barbersmir1", 0x90BBBBBB);
    // LSAINTXT = CreateDynamicObject(19913, 1727.296264, -2492.586914, 26.706945, 10.000043, 0.000007, 89.999893, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x0FFFFFFF);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 14581, "ab_mafiasuitea", "barbersmir1", 0x90BBBBBB);
    // LSAINTXT = CreateDynamicObject(19552, 1790.470947, -2506.161621, 29.906967, 0.000000, 0.000007, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16640, "a51", "ws_stationfloor", 0xFF777777);
    // LSAINTXT = CreateDynamicObject(18981, 1749.221313, -2526.872314, 6.716884, 0.000000, -0.000007, 269.999938, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    // LSAINTXT = CreateDynamicObject(18766, 1737.174072, -2528.680664, 13.556915, 180.000000, 90.000000, 90.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16093, "a51_ext", "ws_whitewall2_top", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(11714, 1751.145874, -2514.690673, 13.816894, 0.000014, 0.000000, 179.999954, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 6284, "bev_law2", "lasjmposh2", 0x00000000);
    // LSAINTXT = CreateDynamicObject(11714, 1751.145874, -2517.650390, 13.816894, 0.000014, 0.000000, 179.999954, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 6284, "bev_law2", "lasjmposh2", 0x00000000);
    // LSAINTXT = CreateDynamicObject(18763, 1735.811767, -2560.581054, 22.602848, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    // LSAINTXT = CreateDynamicObject(18763, 1735.811767, -2560.581054, 27.572849, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    // LSAINTXT = CreateDynamicObject(18763, 1728.940917, -2560.581054, 27.502853, 0.000000, 0.000022, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    // LSAINTXT = CreateDynamicObject(18763, 1728.940917, -2560.581054, 22.592849, 0.000000, 0.000022, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    // LSAINTXT = CreateDynamicObject(19373, 1735.894287, -2559.556152, 18.866916, 0.000007, 0.000000, 89.999977, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19373, 1735.894287, -2560.326171, 18.866916, 0.000007, 0.000000, 89.999977, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19373, 1735.894287, -2561.036132, 18.866916, 0.000007, 0.000000, 89.999977, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19373, 1735.894287, -2561.836181, 18.866916, 0.000007, 0.000000, 89.999977, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19373, 1728.903442, -2559.556152, 18.866916, 0.000022, 0.000000, 89.999931, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19373, 1728.903442, -2560.326171, 18.866916, 0.000022, 0.000000, 89.999931, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19373, 1728.903442, -2561.036132, 18.866916, 0.000022, 0.000000, 89.999931, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19373, 1728.903442, -2561.836181, 18.866916, 0.000022, 0.000000, 89.999931, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // LSAINTXT = CreateDynamicObject(18763, 1735.791748, -2474.061035, 22.602848, 0.000000, 0.000007, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    // LSAINTXT = CreateDynamicObject(18763, 1735.791748, -2474.061035, 27.572849, 0.000000, 0.000007, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    // LSAINTXT = CreateDynamicObject(18763, 1728.920898, -2474.061035, 27.502853, 0.000000, 0.000029, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    // LSAINTXT = CreateDynamicObject(18763, 1728.920898, -2474.061035, 22.592849, 0.000000, 0.000029, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    // LSAINTXT = CreateDynamicObject(19373, 1735.874267, -2473.036132, 18.866916, 0.000014, 0.000000, 89.999954, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19373, 1735.874267, -2473.806152, 18.866916, 0.000014, 0.000000, 89.999954, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19373, 1735.874267, -2474.516113, 18.866916, 0.000014, 0.000000, 89.999954, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19373, 1735.874267, -2475.316162, 18.866916, 0.000014, 0.000000, 89.999954, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19373, 1728.883422, -2473.036132, 18.866916, 0.000029, 0.000000, 89.999908, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19373, 1728.883422, -2473.806152, 18.866916, 0.000029, 0.000000, 89.999908, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19373, 1728.883422, -2474.516113, 18.866916, 0.000029, 0.000000, 89.999908, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19373, 1728.883422, -2475.316162, 18.866916, 0.000029, 0.000000, 89.999908, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // LSAINTXT = CreateDynamicObject(8661, 1736.300537, -2468.725585, 12.596905, 0.000014, 0.000000, 89.999931, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 14803, "bdupsnew", "Bdup2_Artex", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(19373, 1728.831298, -2484.971435, 10.976907, 0.000000, 0.000000, 90.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19373, 1725.691284, -2484.971435, 10.976907, 0.000000, 0.000000, 90.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19373, 1739.015502, -2484.971435, 10.976907, 0.000007, 0.000000, 89.999977, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19373, 1735.875488, -2484.971435, 10.976907, 0.000007, 0.000000, 89.999977, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // LSAINTXT = CreateDynamicObject(8661, 1736.300537, -2570.861572, 12.606905, 0.000014, 0.000000, 89.999931, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 6882, "vgnland", "hiwaygravel1_256", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(19373, 1735.810180, -2550.883544, 10.986906, 0.000000, 0.000000, 90.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19373, 1729.079589, -2550.883544, 10.986906, 0.000000, 0.000000, 90.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19373, 1726.039550, -2550.883544, 10.986906, 0.000000, 0.000000, 90.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // LSAINTXT = CreateDynamicObject(18981, 1751.659790, -2503.493408, 6.716882, 0.000000, -0.000007, 359.999938, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16093, "a51_ext", "ws_whitewall2_top", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(2991, 1731.047607, -2539.958496, 12.224157, 89.999992, 45.098960, -135.098999, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 16640, "a51", "scratchedmetal", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2991, 1732.487670, -2539.958496, 12.224157, 89.999992, 180.000000, -89.999961, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 16640, "a51", "scratchedmetal", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19373, 1731.766967, -2539.375488, 12.166906, 0.000000, 0.000014, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19373, 1731.767456, -2540.475830, 12.166906, 0.000000, 0.000014, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2991, 1731.047607, -2532.297363, 12.224157, 89.999992, 25.596660, -115.596694, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 16640, "a51", "scratchedmetal", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2991, 1732.487670, -2532.297363, 12.224157, 89.999992, 180.000015, -89.999961, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 16640, "a51", "scratchedmetal", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19373, 1731.766967, -2531.714355, 12.166906, 0.000000, 0.000022, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19373, 1731.767456, -2532.814697, 12.166906, 0.000000, 0.000022, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2991, 1731.047607, -2518.704345, 12.224157, 89.999992, 25.596660, -115.596694, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 16640, "a51", "scratchedmetal", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2991, 1732.487670, -2518.704345, 12.224157, 89.999992, 180.000015, -89.999961, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 16640, "a51", "scratchedmetal", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19373, 1731.766967, -2518.121337, 12.166906, 0.000000, 0.000022, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19373, 1731.767456, -2519.221679, 12.166906, 0.000000, 0.000022, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2991, 1731.047607, -2511.043212, 12.224157, 89.999992, 13.407106, -103.407119, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 16640, "a51", "scratchedmetal", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2991, 1732.487670, -2511.043212, 12.224157, 89.999992, 180.000030, -89.999961, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 16640, "a51", "scratchedmetal", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19373, 1731.766967, -2510.460205, 12.166906, 0.000000, 0.000029, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19373, 1731.767456, -2511.560546, 12.166906, 0.000000, 0.000029, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2991, 1731.047607, -2502.213134, 12.224157, 89.999992, 13.407106, -103.407119, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 16640, "a51", "scratchedmetal", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2991, 1732.487670, -2502.213134, 12.224157, 89.999992, 180.000030, -89.999961, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 16640, "a51", "scratchedmetal", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19373, 1731.766967, -2501.630126, 12.166906, 0.000000, 0.000029, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19373, 1731.767456, -2502.730468, 12.166906, 0.000000, 0.000029, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2991, 1731.047607, -2494.552001, 12.224157, 89.999992, 6.794076, -96.794067, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 16640, "a51", "scratchedmetal", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2991, 1732.487670, -2494.552001, 12.224157, 89.999992, 180.000045, -89.999961, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 16640, "a51", "scratchedmetal", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19373, 1731.766967, -2493.968994, 12.166906, 0.000000, 0.000037, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19373, 1731.767456, -2495.069335, 12.166906, 0.000000, 0.000037, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19373, 1737.008056, -2523.825439, 21.346916, -0.000007, 0.000045, 0.000022, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16640, "a51", "scratchedmetal", 0xFF555555);
    // LSAINTXT = CreateDynamicObject(19373, 1737.017822, -2523.265625, 22.816923, -0.000007, 0.000045, 0.000022, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterialText(LSAINTXT, 0, "INFORMATION", 140, "Arial", 50, 1, 0xFFFFFFFF, 0x00000000, 1);
    // LSAINTXT = CreateDynamicObject(19373, 1737.007324, -2522.635253, 21.346916, -0.000007, 0.000045, 0.000022, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16640, "a51", "scratchedmetal", 0xFF555555);
    // LSAINTXT = CreateDynamicObject(19128, 1737.099365, -2523.217285, 20.546913, 89.999992, 185.863647, -95.863578, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2813, "gb_books01", "GB_novels07", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 2813, "gb_books01", "GB_novels04", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19373, 1737.008056, -2510.434814, 21.346916, -0.000007, 0.000052, 0.000022, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16640, "a51", "scratchedmetal", 0xFF555555);
    // LSAINTXT = CreateDynamicObject(19373, 1737.017822, -2509.875000, 22.816923, -0.000007, 0.000052, 0.000022, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterialText(LSAINTXT, 0, "INFORMATION", 140, "Arial", 50, 1, 0xFFFFFFFF, 0x00000000, 1);
    // LSAINTXT = CreateDynamicObject(8293, 1735.577270, -2569.025634, 24.466934, -0.000018, -0.000024, -142.499908, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2813, "gb_books01", "GB_novels10", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 10101, "2notherbuildsfe", "Bow_church_grass_alt", 0x00000000);
    // LSAINTXT = CreateDynamicObject(8293, 1735.577270, -2465.815673, 24.466934, -0.000012, -0.000018, 37.500072, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2813, "gb_books01", "GB_novels11", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 10101, "2notherbuildsfe", "Bow_church_grass_alt", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19377, 1731.810791, -2468.989746, 19.166919, 0.000000, 90.000015, 179.999893, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 14803, "bdupsnew", "Bdup2_Artex", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(19624, 1745.922241, -2528.499267, 19.736913, 0.000000, 0.000000, 32.599998, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 3, 3881, "apsecurity_sfxrf", "leather_seat_256", 0x00000000);
    // LSAINTXT = CreateDynamicObject(7212, 1737.000244, -2498.656982, 20.936922, 0.000000, 0.000000, 180.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x90FFFFFF);
    // SetDynamicObjectMaterial(LSAINTXT, 9, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    // LSAINTXT = CreateDynamicObject(7212, 1737.040283, -2535.866699, 20.936922, 0.000000, 0.000000, 180.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x90FFFFFF);
    // SetDynamicObjectMaterial(LSAINTXT, 9, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    // LSAINTXT = CreateDynamicObject(640, 1743.041870, -2563.580810, 19.866912, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 674, "gta_deserttrees", "sm_josh_leaf", 0x00000000);
    // LSAINTXT = CreateDynamicObject(640, 1727.241943, -2564.510742, 19.866912, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 674, "gta_deserttrees", "sm_josh_leaf", 0x00000000);
    // LSAINTXT = CreateDynamicObject(640, 1743.041870, -2471.082763, 19.866912, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 674, "gta_deserttrees", "sm_josh_leaf", 0x00000000);
    // LSAINTXT = CreateDynamicObject(640, 1727.281005, -2470.393066, 19.866912, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 674, "gta_deserttrees", "sm_josh_leaf", 0x00000000);
    // LSAINTXT = CreateDynamicObject(18981, 1752.781738, -2516.481201, 28.576868, 0.000000, 90.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    // LSAINTXT = CreateDynamicObject(18765, 1754.886596, -2516.939208, 30.436908, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19861, 1751.108886, -2516.324218, 17.206909, 0.000000, 0.000000, 270.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterialText(LSAINTXT, 0, "ARRIVALS GATE", 130, "Arial", 65, 1, 0xFFFFFFFF, 0x00000000, 1);
    // LSAINTXT = CreateDynamicObject(19443, 1751.201904, -2514.947753, 16.066909, 90.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    // LSAINTXT = CreateDynamicObject(19443, 1751.201049, -2517.388183, 16.066909, 90.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    // LSAINTXT = CreateDynamicObject(2256, 1751.129150, -2507.145996, 13.956903, 0.000000, 0.000000, -90.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16640, "a51", "ventb128", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2256, 1751.129150, -2511.915771, 13.956903, 0.000000, 0.000000, -90.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16640, "a51", "ventb128", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2256, 1751.129150, -2520.284179, 13.956903, -0.000007, 0.000000, -89.999977, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16640, "a51", "ventb128", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2256, 1751.129150, -2525.053955, 13.956903, -0.000007, 0.000000, -89.999977, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16640, "a51", "ventb128", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19786, 1751.212524, -2524.407470, 15.936911, 0.000000, 0.000000, -90.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 1, 2772, "airp_prop", "CJ_AIRP_S_2", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19786, 1751.212524, -2510.716308, 15.936911, 0.000000, 0.000000, -90.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 1, 2772, "airp_prop", "CJ_AIRP_S_2", 0x00000000);
    // LSAINTXT = CreateDynamicObject(18980, 1750.859985, -2515.597412, 18.756929, 90.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    // LSAINTXT = CreateDynamicObject(19377, 1755.880859, -2513.126953, 13.206905, 0.000000, 0.000000, 90.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    // LSAINTXT = CreateDynamicObject(19377, 1755.880859, -2519.207275, 13.206905, 0.000000, 0.000000, 90.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    // LSAINTXT = CreateDynamicObject(2256, 1751.139160, -2514.138183, 17.526908, 0.000000, 0.000000, -90.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 14706, "labig2int2", "HS3_wall2", 0xFF706353);
    // LSAINTXT = CreateDynamicObject(2256, 1751.139160, -2515.979003, 17.526908, 0.000000, 0.000000, -90.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 14706, "labig2int2", "HS3_wall2", 0xFF706353);
    // LSAINTXT = CreateDynamicObject(2256, 1751.139160, -2517.820800, 17.526908, 0.000000, 0.000000, -90.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 14706, "labig2int2", "HS3_wall2", 0xFF706353);
    // LSAINTXT = CreateDynamicObject(2256, 1751.149169, -2518.211181, 17.526908, 0.000000, 0.000000, -90.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 14706, "labig2int2", "HS3_wall2", 0xFF706353);
    // LSAINTXT = CreateDynamicObject(19786, 1751.212524, -2507.495605, 15.936911, 0.000000, 0.000000, -90.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 1, 2772, "airp_prop", "CJ_AIRP_S_2", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19786, 1751.212524, -2521.316894, 15.936911, 0.000000, 0.000000, -90.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 1, 2772, "airp_prop", "CJ_AIRP_S_2", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19447, 1736.946655, -2516.094726, 17.676904, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16640, "a51", "scratchedmetal", 0xFF555555);
    // LSAINTXT = CreateDynamicObject(19940, 1737.021118, -2518.646728, 16.996910, -0.000014, -89.999977, -0.000075, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19861, 1737.078857, -2514.944335, 18.796915, 0.000022, 0.000000, 89.999931, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterialText(LSAINTXT, 0, "International Airport", 130, "Segoe UI", 55, 1, 0xFFBBBBBB, 0x00000000, 1);
    // LSAINTXT = CreateDynamicObject(19861, 1737.078857, -2514.944335, 18.126909, 0.000022, 0.000000, 89.999931, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterialText(LSAINTXT, 0, "ATHERLIFE", 130, "Segoe UI", 125, 1, 0xFFBBBBBB, 0x00000000, 1);
    // LSAINTXT = CreateDynamicObject(19373, 1737.007324, -2509.244628, 21.346916, -0.000007, 0.000052, 0.000022, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16640, "a51", "scratchedmetal", 0xFF555555);
    // LSAINTXT = CreateDynamicObject(19128, 1737.099365, -2509.826660, 20.546913, 89.999992, 182.939514, -92.939422, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2813, "gb_books01", "GB_novels04", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 2813, "gb_books01", "GB_novels04", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19443, 1737.612060, -2487.325683, 18.396905, 89.999992, 90.000030, -89.999961, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    // LSAINTXT = CreateDynamicObject(19443, 1737.612060, -2490.816162, 18.396905, 89.999992, 90.000030, -89.999961, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    // LSAINTXT = CreateDynamicObject(19443, 1737.612060, -2494.315917, 18.396905, 89.999992, 90.000045, -89.999961, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    // LSAINTXT = CreateDynamicObject(19443, 1737.612060, -2497.806396, 18.396905, 89.999992, 90.000045, -89.999961, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    // LSAINTXT = CreateDynamicObject(19443, 1737.612060, -2501.315429, 18.396905, 89.999992, 90.000061, -89.999961, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    // LSAINTXT = CreateDynamicObject(19443, 1737.612060, -2503.254394, 18.396905, 89.999992, 90.000061, -89.999961, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    // LSAINTXT = CreateDynamicObject(18763, 1745.092041, -2494.648681, 17.816879, 0.000000, 90.000000, 90.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16640, "a51", "scratchedmetal", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19861, 1743.832519, -2494.597900, 15.956882, 0.000000, 0.000000, -90.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterialText(LSAINTXT, 0, "CLOTHING STORE", 130, "Palatino Linotype", 30, 1, 0xFFFFFFFF, 0x00000000, 1);
    // LSAINTXT = CreateDynamicObject(19552, 1665.100585, -2515.351074, 12.506969, 0.000000, 0.000007, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 9514, "711_sfw", "ws_carpark2", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19373, 1762.588867, -2503.062744, 20.686906, -0.000022, 0.000022, -89.999900, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16640, "a51", "scratchedmetal", 0xFF555555);
    // LSAINTXT = CreateDynamicObject(19373, 1763.148681, -2503.072509, 22.156913, -0.000022, 0.000022, -89.999900, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterialText(LSAINTXT, 0, "INFORMATION", 140, "Arial", 50, 1, 0xFFFFFFFF, 0x00000000, 1);
    // LSAINTXT = CreateDynamicObject(19373, 1762.898437, -2530.875976, 20.686906, -0.000022, 0.000022, 90.000038, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16640, "a51", "scratchedmetal", 0xFF555555);
    // LSAINTXT = CreateDynamicObject(19443, 1758.599609, -2501.541503, 20.176921, 0.000000, 0.000007, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, -1, "none", "none", 0xFF4E4945);
    // LSAINTXT = CreateDynamicObject(19443, 1756.799560, -2501.541503, 21.846927, 0.000000, 90.000007, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, -1, "none", "none", 0xFF4E4945);
    // LSAINTXT = CreateDynamicObject(19443, 1754.469848, -2501.541503, 21.846927, 0.000000, 90.000007, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, -1, "none", "none", 0xFF4E4945);
    // LSAINTXT = CreateDynamicObject(2658, 1751.780273, -2502.112060, 21.066907, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 3967, "cj_airprt", "cj_juank_1", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2658, 1759.420410, -2502.112060, 21.066907, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 3967, "cj_airprt", "cj_juank_1", 0x00000000);
    // LSAINTXT = CreateDynamicObject(1444, 1751.336059, -2503.011718, 20.076917, 0.000000, 0.000000, 29.699996, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 3967, "cj_airprt", "cj_juank_1", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19373, 1763.778930, -2503.062011, 20.686906, -0.000022, 0.000022, -89.999900, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16640, "a51", "scratchedmetal", 0xFF555555);
    // LSAINTXT = CreateDynamicObject(19128, 1763.197143, -2503.154052, 19.886903, 90.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2813, "gb_books01", "GB_novels05", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 2813, "gb_books01", "GB_novels05", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19373, 1762.338623, -2530.866210, 22.156913, -0.000022, 0.000022, 90.000038, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterialText(LSAINTXT, 0, "INFORMATION", 140, "Arial", 50, 1, 0xFFFFFFFF, 0x00000000, 1);
    // LSAINTXT = CreateDynamicObject(19373, 1761.708374, -2530.876708, 20.686906, -0.000022, 0.000022, 90.000038, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16640, "a51", "scratchedmetal", 0xFF555555);
    // LSAINTXT = CreateDynamicObject(19128, 1762.290161, -2530.784667, 19.886903, 89.999992, 179.971725, 720.028198, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2813, "gb_books01", "GB_novels04", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 2813, "gb_books01", "GB_novels04", 0x00000000);
    // LSAINTXT = CreateDynamicObject(18980, 1759.911132, -2501.625732, 23.716917, 0.000000, 90.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    // LSAINTXT = CreateDynamicObject(18980, 1759.911132, -2532.927734, 23.716917, 0.000000, 90.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    // LSAINTXT = CreateDynamicObject(19377, 1752.260620, -2502.005371, 29.456901, 0.000000, 0.000000, 90.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 14706, "labig2int2", "HS3_wall2", 0xFF706353);
    // LSAINTXT = CreateDynamicObject(19377, 1761.900756, -2502.005371, 29.456901, 0.000000, 0.000000, 90.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 14706, "labig2int2", "HS3_wall2", 0xFF706353);
    // LSAINTXT = CreateDynamicObject(19377, 1768.050903, -2502.004882, 29.456901, 0.000000, 0.000000, 90.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 14706, "labig2int2", "HS3_wall2", 0xFF706353);
    // LSAINTXT = CreateDynamicObject(19377, 1752.260620, -2532.524414, 29.456901, 0.000007, 0.000000, 89.999977, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 14706, "labig2int2", "HS3_wall2", 0xFF706353);
    // LSAINTXT = CreateDynamicObject(19377, 1761.900756, -2532.524414, 29.456901, 0.000007, 0.000000, 89.999977, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 14706, "labig2int2", "HS3_wall2", 0xFF706353);
    // LSAINTXT = CreateDynamicObject(19377, 1768.050903, -2532.523925, 29.456901, 0.000007, 0.000000, 89.999977, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 14706, "labig2int2", "HS3_wall2", 0xFF706353);
    // LSAINTXT = CreateDynamicObject(11714, 1753.217651, -2532.474853, 20.556900, 0.000006, 0.000000, -89.999992, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 8391, "ballys01", "vgncorpdoor1_512", 0x00000000);
    // LSAINTXT = CreateDynamicObject(11714, 1756.188964, -2532.474853, 20.556900, 0.000006, 0.000000, -89.999992, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 8391, "ballys01", "vgncorpdoor1_512", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19443, 1757.716186, -2532.964599, 20.176921, 0.000000, 0.000000, 179.999847, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, -1, "none", "none", 0xFF4E4945);
    // LSAINTXT = CreateDynamicObject(19443, 1751.765991, -2532.964599, 20.176921, 0.000000, 0.000000, 179.999847, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, -1, "none", "none", 0xFF4E4945);
    // LSAINTXT = CreateDynamicObject(19443, 1753.566040, -2532.964599, 21.846927, 0.000000, 90.000000, 179.999847, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, -1, "none", "none", 0xFF4E4945);
    // LSAINTXT = CreateDynamicObject(19443, 1755.895751, -2532.964599, 21.846927, 0.000000, 90.000000, 179.999847, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, -1, "none", "none", 0xFF4E4945);
    // LSAINTXT = CreateDynamicObject(2658, 1758.585327, -2532.394042, 21.066907, 0.000000, -0.000007, 179.999847, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 3967, "cj_airprt", "cj_juank_1", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2658, 1750.945190, -2532.394042, 21.066907, 0.000000, -0.000007, 179.999847, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 3967, "cj_airprt", "cj_juank_1", 0x00000000);
    // LSAINTXT = CreateDynamicObject(1444, 1750.390869, -2531.337402, 20.076917, 0.000000, 0.000000, -14.300003, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 3967, "cj_airprt", "cj_juank_1", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19373, 1727.126831, -2506.042724, 14.026893, -0.000007, 0.000052, 0.000022, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16640, "a51", "scratchedmetal", 0xFF555555);
    // LSAINTXT = CreateDynamicObject(19373, 1727.136596, -2505.482910, 15.496900, -0.000007, 0.000052, 0.000022, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterialText(LSAINTXT, 0, "INFORMATION", 140, "Arial", 50, 1, 0xFFFFFFFF, 0x00000000, 1);
    // LSAINTXT = CreateDynamicObject(19373, 1727.126098, -2504.852539, 14.026893, -0.000007, 0.000052, 0.000022, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16640, "a51", "scratchedmetal", 0xFF555555);
    // LSAINTXT = CreateDynamicObject(19128, 1727.218139, -2505.434570, 13.226890, 89.999992, 182.939514, -92.939422, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2813, "gb_books01", "GB_novels05", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 2813, "gb_books01", "GB_novels04", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19373, 1727.126831, -2526.354248, 14.026893, -0.000007, 0.000060, 0.000022, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16640, "a51", "scratchedmetal", 0xFF555555);
    // LSAINTXT = CreateDynamicObject(19373, 1727.136596, -2525.794433, 15.496900, -0.000007, 0.000060, 0.000022, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterialText(LSAINTXT, 0, "INFORMATION", 140, "Arial", 50, 1, 0xFFFFFFFF, 0x00000000, 1);
    // LSAINTXT = CreateDynamicObject(19373, 1727.126098, -2525.164062, 14.026893, -0.000007, 0.000060, 0.000022, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16640, "a51", "scratchedmetal", 0xFF555555);
    // LSAINTXT = CreateDynamicObject(19128, 1727.218139, -2525.746093, 13.226890, 89.999992, 181.470764, -91.470657, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2813, "gb_books01", "GB_novels07", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 2813, "gb_books01", "GB_novels04", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19940, 1737.021118, -2518.646728, 17.446905, -0.000014, -89.999977, -0.000075, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 18646, "matcolours", "red-4", 0xFFFF0000);
    // LSAINTXT = CreateDynamicObject(19940, 1772.274658, -2517.085937, 27.276922, -0.000014, -89.999977, 179.999816, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19940, 1772.274658, -2517.085937, 27.726917, -0.000014, -89.999977, 179.999816, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 18646, "matcolours", "red-4", 0xFFFF0000);
    // LSAINTXT = CreateDynamicObject(1297, 1767.983642, -2510.284179, 31.286954, 0.000000, 171.999984, -179.999893, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 19480, "signsurf", "sign", 0x00000000);
    // LSAINTXT = CreateDynamicObject(1297, 1743.882446, -2525.215087, 31.286954, 0.000000, 171.999984, -179.999938, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 19480, "signsurf", "sign", 0x00000000);
    // LSAINTXT = CreateDynamicObject(1297, 1743.882446, -2510.284179, 31.286954, 0.000000, 171.999984, -179.999893, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 19480, "signsurf", "sign", 0x00000000);
    // LSAINTXT = CreateDynamicObject(1297, 1755.763183, -2525.215087, 31.286954, 0.000000, 171.999984, -179.999893, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 19480, "signsurf", "sign", 0x00000000);
    // LSAINTXT = CreateDynamicObject(1297, 1755.763183, -2510.284179, 31.286954, 0.000000, 171.999984, -179.999847, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 19480, "signsurf", "sign", 0x00000000);
    // LSAINTXT = CreateDynamicObject(1431, 1738.189941, -2541.283447, 12.597847, 0.000051, 0.000000, 89.999839, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2212, "burger_tray", "burgerkids", 0x00000000);
    // LSAINTXT = CreateDynamicObject(1431, 1738.149780, -2547.253906, 12.597847, 0.000051, 0.000000, 89.999839, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2212, "burger_tray", "burgerkids", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19373, 1747.966674, -2515.486816, 19.286911, 0.000000, 90.000022, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19373, 1747.966674, -2517.968261, 19.285999, 0.000000, 90.000022, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19936, 1746.298461, -2514.171142, 19.286911, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19936, 1746.298461, -2519.514892, 19.286911, 0.000000, 0.000000, 90.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2163, 1746.289184, -2515.106689, 19.366912, -0.000007, 0.000000, -89.999977, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2163, 1746.289184, -2517.767822, 19.366912, -0.000007, 0.000000, -89.999977, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19373, 1751.077026, -2515.486816, 20.876466, 0.000000, 180.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 4835, "airoads_las", "aarprt8LAS", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19373, 1751.076049, -2517.968261, 20.876466, 0.000000, 180.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 4835, "airoads_las", "aarprt8LAS", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19373, 1748.766235, -2515.486816, 22.535987, 0.000000, 270.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 4835, "airoads_las", "aarprt8LAS", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19373, 1748.766235, -2517.968261, 22.536964, 0.000000, 270.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 4835, "airoads_las", "aarprt8LAS", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19937, 1751.180541, -2514.847167, 22.090589, 0.000000, -45.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(19937, 1751.180541, -2516.746582, 22.090589, 0.000000, -45.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(19937, 1751.180541, -2518.606445, 22.090589, 0.000000, -44.999992, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(19373, 1751.277221, -2515.486816, 21.056470, 0.000000, 180.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(19373, 1751.276245, -2517.968261, 21.056470, 0.000000, 180.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(19373, 1749.607055, -2515.486816, 22.865993, 0.000000, 270.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(19373, 1749.607055, -2517.968261, 22.866970, 0.000000, 270.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(19937, 1751.180541, -2518.625732, 22.720586, 0.000000, -89.999992, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(19937, 1751.180541, -2514.825195, 22.720586, 0.000000, -89.999992, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(19937, 1746.066284, -2518.092041, 19.311935, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(19937, 1746.066284, -2516.191406, 19.311935, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(19937, 1746.066040, -2515.600830, 19.311935, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(2163, 1745.858764, -2518.578613, 19.366912, -0.000007, 0.000000, 89.999961, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2163, 1745.858764, -2515.917480, 19.366912, -0.000007, 0.000000, 89.999961, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2387, 1748.374633, -2515.195312, 19.366903, 0.000000, 0.000014, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 3, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 4, 10101, "2notherbuildsfe", "ferry_build14", 0xFF333333);
    // LSAINTXT = CreateDynamicObject(19939, 1748.696655, -2515.099365, 20.196903, 0.000000, 90.000030, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19939, 1748.401977, -2515.077880, 20.196903, 0.000000, 89.999961, 179.999664, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2387, 1748.374633, -2518.207275, 19.366903, 0.000000, 0.000022, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 3, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 4, 10101, "2notherbuildsfe", "ferry_build14", 0xFF333333);
    // LSAINTXT = CreateDynamicObject(19939, 1748.696655, -2518.111328, 20.196903, 0.000000, 90.000038, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19939, 1748.401977, -2518.089843, 20.196903, 0.000000, 89.999954, 179.999618, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2375, 1751.076538, -2514.773193, 19.501909, -0.000007, -0.000007, -89.999961, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 2, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 3, 2423, "cj_ff_counters", "CJ_Laminate1", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 4, 16640, "a51", "ws_stationfloor", 0xFF555555);
    // SetDynamicObjectMaterial(LSAINTXT, 5, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2375, 1751.076538, -2516.372802, 19.501909, -0.000007, -0.000007, -89.999961, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 1, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 2, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 3, 2423, "cj_ff_counters", "CJ_Laminate1", 0x00000000);
    // SetDynamicObjectMaterial(LSAINTXT, 4, 16640, "a51", "ws_stationfloor", 0xFF555555);
    // SetDynamicObjectMaterial(LSAINTXT, 5, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2585, 1750.740356, -2515.287353, 20.931909, -0.000007, -0.000007, -89.999961, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 4, 18031, "cj_exp", "CJ_DS_WINDOW", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2585, 1750.740356, -2516.647460, 20.931909, -0.000007, -0.000007, -89.999961, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 4, 2028, "cj_games", "CJ_CONSOLETOP", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2585, 1750.740356, -2518.247802, 20.931909, -0.000007, -0.000007, -89.999961, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 4, 2655, "cj_banner", "CJ_SUBURBAN_1", 0x00000000);
    // LSAINTXT = CreateDynamicObject(19443, 1747.838134, -2514.018554, 20.796918, 0.000000, 0.000000, 90.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10973, "mall_sfse", "ws_grilleshade", 0xFFBBBBBB);
    // LSAINTXT = CreateDynamicObject(19443, 1747.838134, -2519.350097, 20.796918, 0.000000, 0.000000, 90.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 10973, "mall_sfse", "ws_grilleshade", 0x00000000);
    // LSAINTXT = CreateDynamicObject(2599, 1746.068725, -2520.712158, 19.716911, 0.000000, 0.000000, -45.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterialText(LSAINTXT, 0, "Electronic Store", 140, "Segoe Script", 90, 1, 0xFFFFFFFF, 0xFF555555, 1);
    // LSAINTXT = CreateDynamicObject(2599, 1746.104125, -2520.676757, 19.926916, 0.000000, 0.000000, -45.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 1, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterialText(LSAINTXT, 0, "~", 140, "Webdings", 150, 1, 0xFFFFFFFF, 0x00000000, 1);
    // LSAINTXT = CreateDynamicObject(2599, 1746.068603, -2513.086669, 19.716911, -0.000009, 0.000000, -134.999984, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterialText(LSAINTXT, 0, "Electronic Store", 140, "Segoe Script", 90, 1, 0xFFFFFFFF, 0xFF555555, 1);
    // LSAINTXT = CreateDynamicObject(2599, 1746.104003, -2513.121826, 19.926916, -0.000009, 0.000000, -134.999984, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 1, 19480, "signsurf", "sign", 0x00000000);
    // SetDynamicObjectMaterialText(LSAINTXT, 0, "~", 140, "Webdings", 150, 1, 0xFFFFFFFF, 0x00000000, 1);
    // LSAINTXT = CreateDynamicObject(18766, 1749.044433, -2509.454101, 12.770131, 90.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16640, "a51", "scratchedmetal", 0x00000000);
    // LSAINTXT = CreateDynamicObject(18766, 1749.044433, -2522.583740, 12.740133, 90.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(LSAINTXT, 0, 16640, "a51", "scratchedmetal", 0x00000000);
    // /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    // /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    // /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    // CreateDynamicObject(2936, 1755.069824, -2515.831787, 20.406906, 0.000000, 0.000037, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(2936, 1754.659667, -2515.561523, 20.516908, 0.000035, 0.000012, 69.999984, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(3439, 1753.965454, -2516.931396, 20.526920, 0.000000, 0.000037, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1892, 1768.363159, -2514.307617, 19.286909, 0.000007, 0.000000, 89.999977, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1892, 1768.363159, -2517.808593, 19.286909, 0.000007, 0.000000, 89.999977, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1892, 1768.363159, -2521.099853, 19.286909, 0.000007, 0.000000, 89.999977, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(3439, 1771.145751, -2511.821777, 20.526920, 0.000000, 0.000037, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(3439, 1771.145751, -2522.642578, 20.526920, 0.000000, 0.000037, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(3657, 1757.517089, -2516.786132, 19.701902, 0.000007, 0.000000, 89.999977, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(3657, 1754.041992, -2513.220947, 19.701902, 0.000007, -0.000014, 179.999862, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(3657, 1754.041992, -2520.171142, 19.701902, -0.000007, 0.000014, -0.000006, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(2773, 1744.433227, -2540.174316, 19.786911, 0.000022, 0.000000, 89.999931, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(2773, 1744.433227, -2538.002197, 19.786911, 0.000022, 0.000000, 89.999931, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(2773, 1744.433227, -2546.484619, 19.786911, 0.000029, 0.000000, 89.999908, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(2773, 1744.433227, -2544.312500, 19.786911, 0.000029, 0.000000, 89.999908, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(2773, 1744.433227, -2552.856445, 19.786911, 0.000029, 0.000000, 89.999908, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(2773, 1744.433227, -2550.684326, 19.786911, 0.000029, 0.000000, 89.999908, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(2773, 1744.433227, -2559.166748, 19.786911, 0.000037, 0.000000, 89.999885, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(2773, 1744.433227, -2556.994628, 19.786911, 0.000037, 0.000000, 89.999885, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(3586, 1732.411132, -2555.335205, 15.846920, 0.000000, 0.000014, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1897, 1737.646240, -2534.041992, 14.471908, 0.000014, -0.000067, 179.999511, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1897, 1737.646240, -2532.781494, 14.511911, -0.000012, 0.000067, 0.000012, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1897, 1737.616210, -2533.091796, 15.391917, 89.999992, 89.787414, -89.787284, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1897, 1737.616210, -2533.091796, 15.011908, 89.999992, 89.787414, -89.787284, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1897, 1737.616210, -2533.091796, 13.271899, 89.999992, 89.787414, -89.787284, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1897, 1737.646240, -2539.983398, 14.501912, 0.000022, -0.000067, 179.999496, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1897, 1737.646240, -2538.722900, 14.431914, -0.000020, 0.000067, 0.000037, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1897, 1737.616210, -2539.033203, 15.391917, 89.999992, 89.787429, -89.787284, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1897, 1737.616210, -2539.033203, 15.011908, 89.999992, 89.787429, -89.787284, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1897, 1737.616210, -2539.033203, 13.261902, 89.999992, 89.787429, -89.787284, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1897, 1737.616210, -2535.982421, 15.181913, -89.999992, 90.107124, 90.106933, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1897, 1737.626220, -2536.863037, 15.181913, -89.999992, -89.894157, 90.105659, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1897, 1737.616210, -2535.982421, 15.371916, -89.999992, 90.092643, 90.092430, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1897, 1737.626220, -2536.863037, 15.371916, -89.999992, -89.908271, 90.091499, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1897, 1737.632568, -2534.871826, 12.581979, 0.000020, 180.197845, -179.801681, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1897, 1737.624755, -2534.871826, 14.771966, 0.000020, 180.197845, -179.801681, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1897, 1737.634521, -2537.953613, 13.521952, 0.000000, 180.197845, 0.197918, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1897, 1737.636962, -2537.953613, 14.131953, 0.000000, 180.197845, 0.197918, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(3657, 1737.082763, -2540.643310, 12.957556, -0.000067, -0.000006, -89.999778, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(3657, 1737.082763, -2532.241455, 12.957556, -0.000067, -0.000006, -89.999778, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1987, 1740.939941, -2548.325439, 12.507843, 0.000000, -0.000059, 179.999633, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1897, 1737.646240, -2543.113281, 14.501912, 0.000022, -0.000081, 179.999404, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1994, 1740.121337, -2533.188964, 12.597846, 0.000007, -0.000059, 179.999588, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1993, 1739.151123, -2533.188964, 12.597846, 0.000007, -0.000059, 179.999588, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1995, 1738.160644, -2533.188964, 12.597846, 0.000007, -0.000059, 179.999588, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1885, 1738.048339, -2534.439453, 12.597848, 0.000052, 0.000000, 89.999839, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1897, 1737.646240, -2541.852783, 14.431914, -0.000020, 0.000081, 0.000037, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1897, 1737.616210, -2542.163085, 15.391917, 89.999992, 89.946968, -89.946792, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1987, 1741.820556, -2548.325439, 12.507843, 0.000000, -0.000068, 179.999588, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1987, 1742.690917, -2548.325439, 12.507843, 0.000000, -0.000068, 179.999588, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1897, 1737.616210, -2542.163085, 15.011908, 89.999992, 89.946968, -89.946792, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1897, 1737.616210, -2542.163085, 13.261902, 89.999992, 89.946968, -89.946792, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1897, 1737.646240, -2544.503662, 14.501912, 0.000022, -0.000090, 179.999359, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1897, 1737.616210, -2543.553466, 15.391917, 89.999992, 89.973571, -89.973373, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1897, 1737.616210, -2543.553466, 15.011908, 89.999992, 89.973571, -89.973373, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1897, 1737.616210, -2543.553466, 13.261902, 89.999992, 89.973571, -89.973373, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1897, 1737.646240, -2545.935058, 14.501912, 0.000022, -0.000096, 179.999313, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1897, 1737.616210, -2544.984863, 15.391917, 89.999992, 89.986885, -89.986663, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1897, 1737.616210, -2544.984863, 15.011908, 89.999992, 89.986885, -89.986663, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1897, 1737.616210, -2544.984863, 13.261902, 89.999992, 89.986885, -89.986663, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(2773, 1744.433227, -2477.613525, 19.786911, 0.000037, 0.000000, 89.999885, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(2773, 1744.433227, -2475.441406, 19.786911, 0.000037, 0.000000, 89.999885, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(2362, 1741.925292, -2533.128173, 13.547850, 0.000000, 0.000060, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(2773, 1744.433227, -2483.923828, 19.786911, 0.000044, 0.000000, 89.999862, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(2773, 1744.433227, -2481.751708, 19.786911, 0.000044, 0.000000, 89.999862, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(2773, 1744.433227, -2490.295654, 19.786911, 0.000044, 0.000000, 89.999862, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(2773, 1744.433227, -2488.123535, 19.786911, 0.000044, 0.000000, 89.999862, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(2773, 1744.433227, -2496.605957, 19.786911, 0.000051, 0.000000, 89.999839, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(2773, 1744.433227, -2494.433837, 19.786911, 0.000050, 0.000000, 89.999839, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(3586, 1732.381103, -2479.299072, 15.846920, 0.000000, 0.000012, 179.999893, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(2371, 1740.047119, -2485.945068, 12.606884, 0.000000, 0.000000, 90.000000, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(2371, 1742.648559, -2485.945068, 12.606884, 0.000007, 0.000000, 89.999977, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(2371, 1740.047119, -2503.176025, 12.606884, 0.000022, 0.000000, 89.999931, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(2371, 1742.648559, -2503.176025, 12.606884, 0.000029, 0.000000, 89.999908, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(2374, 1743.737792, -2500.382324, 13.836879, -0.000007, 0.000000, -89.999977, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(2374, 1743.737792, -2499.642333, 13.836879, -0.000007, 0.000000, -89.999977, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(2374, 1743.737792, -2501.123291, 13.836879, -0.000007, 0.000000, -89.999977, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(2374, 1743.737792, -2498.852539, 13.836879, -0.000007, 0.000000, -89.999977, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(2374, 1743.737792, -2489.510498, 13.836879, -0.000014, 0.000000, -89.999954, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(2374, 1743.737792, -2488.770507, 13.836879, -0.000014, 0.000000, -89.999954, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(2374, 1743.737792, -2490.251464, 13.836879, -0.000014, 0.000000, -89.999954, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(2374, 1743.737792, -2487.980712, 13.836879, -0.000014, 0.000000, -89.999954, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(2072, 1740.769042, -2495.103027, 17.356901, 0.000000, 90.000000, -90.000000, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(2072, 1740.769042, -2491.133056, 17.356901, 0.000000, 90.000000, -90.000000, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(2072, 1740.769042, -2498.903808, 17.356901, 0.000000, 90.000000, -90.000000, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(2412, 1738.394165, -2496.333007, 12.606884, 0.000000, 0.000000, 90.000000, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(2412, 1738.394165, -2491.971435, 12.606884, 0.000000, 0.000000, 90.000000, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(3969, 1749.286132, -2504.217041, 13.566905, 0.000000, 0.000000, -90.000000, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1572, 1740.618530, -2524.821289, 13.186902, 0.000000, 0.000000, -102.699996, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1572, 1742.567382, -2520.718750, 13.186902, 0.000000, 0.000000, -102.699996, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1572, 1738.828613, -2506.559326, 13.186902, 0.000000, 0.000000, 6.699996, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(2372, 1738.105468, -2507.084716, 12.596904, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(2372, 1740.005859, -2507.084716, 12.596904, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1572, 1738.981689, -2507.860107, 13.186902, 0.000000, 0.000000, 30.299995, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(19787, 1741.512817, -2505.139160, 14.806911, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(19787, 1739.272338, -2505.139160, 14.806911, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(19787, 1739.272338, -2526.333251, 14.806911, 0.000000, 0.000000, 179.999893, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(19787, 1741.512817, -2526.333251, 14.806911, 0.000000, 0.000000, 179.999893, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(822, 1729.166137, -2478.250000, 12.596907, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(822, 1735.136474, -2478.250000, 12.596907, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(905, 1728.627197, -2483.204589, 12.876907, 0.000000, 0.000000, 110.500000, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(905, 1729.433959, -2482.479248, 12.627618, -159.900024, 0.000000, 96.300010, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(822, 1735.397949, -2557.435302, 12.596907, 0.000000, 0.000007, 179.999893, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(822, 1729.427612, -2557.435302, 12.596907, 0.000000, 0.000007, 179.999893, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(905, 1735.936889, -2552.480712, 12.876907, 0.000007, -0.000001, -69.500038, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(905, 1735.130126, -2553.206054, 12.627618, -20.099971, -179.999984, 96.299942, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1572, 1727.811401, -2526.888916, 13.186902, 0.000000, 0.000000, 77.300003, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1572, 1728.020019, -2547.451904, 13.186902, 0.000000, 0.000000, 101.399986, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(2790, 1726.939453, -2547.808349, 16.336910, 0.000000, 0.000000, 90.000000, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(2790, 1726.939453, -2487.498291, 16.336910, 0.000000, 0.000000, 90.000000, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(2790, 1726.939453, -2553.948730, 16.336910, 0.000000, 0.000000, 90.000000, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(2790, 1726.939453, -2481.457519, 16.336910, 0.000000, 0.000000, 90.000000, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1572, 1727.811401, -2508.179931, 13.186902, 0.000007, 0.000000, 77.299980, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(19624, 1745.391479, -2528.838378, 19.736913, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(2790, 1743.446533, -2471.041015, 22.416908, 0.000000, 0.000000, 270.000000, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(2790, 1743.446533, -2563.682617, 22.416908, 0.000000, 0.000000, 270.000000, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1444, 1738.555419, -2525.025146, 13.396906, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(2773, 1749.033325, -2513.353515, 13.116905, 0.000050, 0.000000, 89.999839, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(2773, 1749.033325, -2519.193847, 13.116905, 0.000050, 0.000000, 89.999839, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(2773, 1746.223388, -2513.353515, 13.116905, 0.000059, 0.000000, 89.999816, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(2773, 1746.223388, -2519.193847, 13.116905, 0.000059, 0.000000, 89.999816, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(2790, 1747.001098, -2505.031494, 16.416912, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(2790, 1747.001098, -2526.363769, 16.416912, 0.000000, 0.000000, 180.000000, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(8038, 1686.557739, -2453.091552, 32.424674, 0.000000, 0.000000, -90.000000, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(3663, 1715.989135, -2494.688232, 14.474685, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(3663, 1715.989135, -2535.949462, 14.474685, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(3663, 1715.989135, -2569.790039, 14.474685, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(8038, 1687.887573, -2581.911621, 32.424674, 0.000000, 0.000000, -90.000000, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(3666, 1674.386596, -2492.091308, 12.934687, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(3666, 1674.386596, -2510.551513, 12.934687, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(3666, 1674.386596, -2527.553710, 12.934687, 0.000000, 0.000007, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(3666, 1674.386596, -2546.013916, 12.934687, 0.000000, 0.000007, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1572, 1760.254882, -2503.381591, 19.836900, 0.000017, 0.000027, -12.700035, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1572, 1769.288330, -2505.417480, 19.836898, 0.000017, 0.000027, -61.200031, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1572, 1768.204956, -2507.457275, 19.836898, 0.000017, 0.000027, -88.200027, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1572, 1768.834228, -2527.487548, 19.836889, 0.000017, 0.000027, -40.100032, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(19786, 1749.804931, -2502.040771, 21.526941, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(19786, 1749.040283, -2532.465332, 21.526941, 0.000000, -0.000007, 179.999847, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1987, 1740.090209, -2548.325439, 12.507843, 0.000000, -0.000059, 179.999633, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1987, 1739.229980, -2548.325439, 12.507843, 0.000000, -0.000059, 179.999633, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(19860, 1743.714721, -2534.471191, 13.826884, 0.000000, 0.000000, -90.000000, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(18874, 1748.702026, -2514.688720, 20.426900, 89.999992, 180.000030, -89.999961, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(18874, 1748.702026, -2515.138671, 20.426900, 89.999992, 180.000030, -89.999961, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(18874, 1748.702026, -2515.488525, 20.426900, 89.999992, 180.000030, -89.999961, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(18874, 1748.396606, -2515.488525, 20.426900, 89.999992, 356.598236, -86.598289, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(18874, 1748.396606, -2515.038574, 20.426900, 89.999992, 356.598236, -86.598289, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(18874, 1748.396606, -2514.688720, 20.426900, 89.999992, 356.598236, -86.598289, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(18874, 1748.702026, -2517.700683, 20.426900, 89.999992, 180.000045, -89.999961, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(18874, 1748.702026, -2518.150634, 20.426900, 89.999992, 180.000045, -89.999961, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(18874, 1748.702026, -2518.500488, 20.426900, 89.999992, 180.000045, -89.999961, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(18874, 1748.396606, -2518.500488, 20.426900, 89.999992, 358.297607, -88.297630, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(18874, 1748.396606, -2518.050537, 20.426900, 89.999992, 358.297607, -88.297630, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(18874, 1748.396606, -2517.700683, 20.426900, 89.999992, 358.297607, -88.297630, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(19787, 1747.054931, -2515.784912, 22.186916, -0.000007, 0.000000, -89.999977, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(19787, 1747.054931, -2517.815185, 22.186916, -0.000007, 0.000000, -89.999977, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1514, 1746.160766, -2516.794189, 20.512851, 0.000000, 0.000000, -90.000000, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(11745, 1744.303222, -2521.751464, 13.435035, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(11745, 1747.703125, -2507.060546, 13.435035, 0.000000, 0.000000, 83.000000, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(19624, 1746.836547, -2512.066894, 13.363054, 90.000000, 4.299999, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(19624, 1750.320068, -2525.184570, 13.353054, 90.000000, 4.299999, 0.000000, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(19559, 1747.844726, -2520.183837, 13.239685, 270.000000, 0.000000, -82.100006, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(4890, 1814.321777, -2519.905517, 18.624485, 0.000000, 0.000000, 90.000000, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(4890, 1814.321777, -2571.420166, 18.624485, 0.000000, 0.000000, 360.000000, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(4890, 1814.321777, -2425.025390, 18.534488, 0.000000, 0.000000, 720.000000, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(3996, 1786.661987, -2554.935302, 19.328252, 0.000000, 0.000000, 90.000000, 60, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(3996, 1786.661987, -2451.755371, 19.338253, 0.000000, 0.000000, 90.000000, 60, 0, -1, 200.00, 200.00);

    //san fierro
    new STREAMER_TAG_OBJECT:LSAINTXT;
    LSAINTXT = CreateDynamicObject(8661, 1411.981567, 1532.638183, 15.530336, 0.000014, 0.000000, 89.999954, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 4830, "airport2", "tardor9", 0xFFC6BCB0);
    LSAINTXT = CreateDynamicObject(8661, 1431.939331, 1532.638183, 15.530336, 0.000014, 0.000000, 89.999954, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 4830, "airport2", "tardor9", 0xFFC6BCB0);
    LSAINTXT = CreateDynamicObject(19377, 1420.593261, 1552.594726, 20.470336, 0.000014, 0.000000, 89.999954, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(8661, 1412.001586, 1492.649047, 15.530336, 0.000014, 0.000000, 89.999954, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 4830, "airport2", "tardor9", 0xFFC6BCB0);
    LSAINTXT = CreateDynamicObject(19447, 1456.327880, 1484.343994, 15.430334, 0.000000, 270.000000, 89.999992, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 4830, "airport2", "tardor9", 0xFFC6BCB0);
    LSAINTXT = CreateDynamicObject(19447, 1446.727050, 1484.343994, 15.430334, -0.000004, 270.000000, 90.000015, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 4830, "airport2", "tardor9", 0xFFC6BCB0);
    LSAINTXT = CreateDynamicObject(19447, 1456.327880, 1487.824096, 15.430334, -0.000058, 270.000000, -89.999824, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 4830, "airport2", "tardor9", 0xFFC6BCB0);
    LSAINTXT = CreateDynamicObject(19447, 1456.327880, 1482.483398, 14.620330, -0.000058, 360.000000, -89.999824, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1456.327880, 1489.644409, 14.620331, -0.000058, 360.000000, -89.999824, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1456.327880, 1489.644409, 20.950340, -0.000058, 360.000000, -89.999824, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1456.327880, 1482.493652, 20.910333, -0.000058, 360.000000, -89.999824, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1456.377929, 1489.714477, 17.550336, -0.000058, 360.000000, -89.999824, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14584, "ab_abbatoir01", "ab_vent1", 0xFF6E493A);
    LSAINTXT = CreateDynamicObject(19447, 1456.377929, 1482.472656, 17.550336, 0.000000, 360.000000, 89.999992, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14584, "ab_abbatoir01", "ab_vent1", 0xFF6E493A);
    LSAINTXT = CreateDynamicObject(19377, 1461.232666, 1486.018066, 17.516267, 0.000000, 0.000014, 0.000000, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0x00000000);
    LSAINTXT = CreateDynamicObject(11714, 1461.144409, 1487.532714, 16.716283, 0.000000, 0.000014, 0.000000, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 4828, "airport3_las", "gallery01_law", 0x00000000);
    LSAINTXT = CreateDynamicObject(19447, 1446.727050, 1487.824096, 15.430334, -0.000050, 270.000000, -89.999847, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 4830, "airport2", "tardor9", 0xFFC6BCB0);
    LSAINTXT = CreateDynamicObject(19447, 1446.727050, 1482.483398, 14.620330, -0.000050, 360.000000, -89.999847, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1446.727050, 1489.644409, 14.620331, -0.000050, 360.000000, -89.999847, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1446.727050, 1489.644409, 20.950340, -0.000050, 360.000000, -89.999847, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1446.727050, 1482.493652, 20.910333, -0.000050, 360.000000, -89.999847, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1441.886840, 1494.353271, 14.620331, 0.000000, 360.000000, -0.000029, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1441.886840, 1494.353271, 22.390340, 0.000000, 360.000000, -0.000029, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1441.876831, 1477.752197, 14.620330, 0.000000, 360.000000, -0.000029, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1441.876831, 1477.781982, 20.640335, 0.000000, 360.000000, -0.000029, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(8661, 1431.981201, 1492.649047, 15.530336, 0.000014, 0.000000, 89.999954, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 4830, "airport2", "tardor9", 0xFFC6BCB0);
    LSAINTXT = CreateDynamicObject(11714, 1461.144409, 1484.531860, 16.716283, 0.000000, 0.000014, 0.000000, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 4828, "airport3_las", "gallery01_law", 0x00000000);
    LSAINTXT = CreateDynamicObject(8167, 1475.657104, 1477.830078, 15.540309, -0.000018, 360.000000, -177.499816, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 7978, "vgssairport", "newall16white", 0x00000000);
    LSAINTXT = CreateDynamicObject(18763, 1462.834472, 1482.547973, 16.490329, -0.000022, 0.000028, -94.499931, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 11301, "carshow_sfse", "concpanel_la", 0x00000000);
    LSAINTXT = CreateDynamicObject(8167, 1473.714233, 1481.450195, 15.520311, 0.000020, 360.000000, 72.099853, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 7978, "vgssairport", "newall16white", 0x00000000);
    LSAINTXT = CreateDynamicObject(8167, 1466.722045, 1483.114990, 15.803030, 6.700009, 357.400024, 82.900009, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 7978, "vgssairport", "newall16white", 0x00000000);
    LSAINTXT = CreateDynamicObject(8167, 1475.209594, 1480.902709, 15.520311, 0.000020, 360.000000, 83.499786, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 7978, "vgssairport", "newall16white", 0x00000000);
    LSAINTXT = CreateDynamicObject(8167, 1475.587036, 1531.239379, 15.540309, -0.000018, 360.000000, -177.499771, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 7978, "vgssairport", "newall16white", 0x00000000);
    LSAINTXT = CreateDynamicObject(18763, 1462.764404, 1535.957275, 16.490329, -0.000029, 0.000027, -94.499908, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 11301, "carshow_sfse", "concpanel_la", 0x00000000);
    LSAINTXT = CreateDynamicObject(8167, 1473.053222, 1479.403686, 15.520311, 0.000020, 360.000000, 72.099830, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 7978, "vgssairport", "newall16white", 0x00000000);
    LSAINTXT = CreateDynamicObject(8167, 1466.104125, 1481.201904, 15.803030, 6.700006, 357.400024, 82.900047, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 7978, "vgssairport", "newall16white", 0x00000000);
    LSAINTXT = CreateDynamicObject(19447, 1446.777099, 1489.714477, 17.550336, -0.000058, 360.000000, -89.999824, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14584, "ab_abbatoir01", "ab_vent1", 0xFF6E493A);
    LSAINTXT = CreateDynamicObject(19447, 1446.777099, 1482.472656, 17.550336, 0.000000, 360.000000, 89.999992, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14584, "ab_abbatoir01", "ab_vent1", 0xFF6E493A);
    LSAINTXT = CreateDynamicObject(19447, 1441.886840, 1480.862182, 17.750339, 89.999992, 450.000000, -89.999969, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1441.896850, 1491.252563, 17.750339, 89.999992, 450.000000, -89.999969, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(8167, 1473.382324, 1480.422363, 16.920318, 0.000020, 450.000000, 72.099830, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 11301, "carshow_sfse", "concpanel_la", 0x00000000);
    LSAINTXT = CreateDynamicObject(8167, 1466.485717, 1482.048950, 17.266473, 8.100008, 89.300025, 85.700149, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 11301, "carshow_sfse", "concpanel_la", 0x00000000);
    LSAINTXT = CreateDynamicObject(8167, 1478.095581, 1477.936523, 15.540309, -0.000018, 360.000000, -177.499816, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 7978, "vgssairport", "newall16white", 0x00000000);
    LSAINTXT = CreateDynamicObject(8167, 1476.906372, 1478.305297, 16.940319, -0.000025, 450.000000, -177.499786, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 11301, "carshow_sfse", "concpanel_la", 0x00000000);
    LSAINTXT = CreateDynamicObject(18762, 1477.408691, 1480.041870, 12.146268, -0.000000, 0.000020, 2.200009, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 5154, "dkcargoshp_las2", "boatrailing_128", 0x00000000);
    LSAINTXT = CreateDynamicObject(18762, 1477.579956, 1475.574462, 12.146268, -0.000000, 0.000020, 2.200009, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 5154, "dkcargoshp_las2", "boatrailing_128", 0x00000000);
    LSAINTXT = CreateDynamicObject(19373, 1477.125488, 1473.244995, 15.370332, 0.000020, 0.000000, 92.799942, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 7978, "vgssairport", "Industdoor9white", 0x00000000);
    LSAINTXT = CreateDynamicObject(8167, 1478.095581, 1492.160156, 15.540309, -0.000015, 360.000000, 2.500122, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 7978, "vgssairport", "newall16white", 0x00000000);
    LSAINTXT = CreateDynamicObject(18763, 1462.834472, 1489.128295, 16.490329, -0.000037, 0.000027, -94.499885, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 11301, "carshow_sfse", "concpanel_la", 0x00000000);
    LSAINTXT = CreateDynamicObject(8167, 1472.889282, 1490.545532, 15.520311, 0.000043, 360.000000, 101.899772, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 7978, "vgssairport", "newall16white", 0x00000000);
    LSAINTXT = CreateDynamicObject(8167, 1466.490478, 1489.944213, 15.875083, 9.100027, 357.400024, 82.899963, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 7978, "vgssairport", "newall16white", 0x00000000);
    LSAINTXT = CreateDynamicObject(8167, 1474.458984, 1490.813598, 15.520311, 0.000043, 360.000000, 113.299720, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 7978, "vgssairport", "newall16white", 0x00000000);
    LSAINTXT = CreateDynamicObject(8167, 1473.332763, 1488.441162, 15.520311, 0.000043, 360.000000, 101.899749, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 7978, "vgssairport", "newall16white", 0x00000000);
    LSAINTXT = CreateDynamicObject(8167, 1466.097534, 1487.732910, 15.800778, 6.700023, 357.400024, 82.900001, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 7978, "vgssairport", "newall16white", 0x00000000);
    LSAINTXT = CreateDynamicObject(8167, 1473.112060, 1489.488525, 16.920318, 0.000043, 450.000000, 101.899749, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 11301, "carshow_sfse", "concpanel_la", 0x00000000);
    LSAINTXT = CreateDynamicObject(8167, 1466.324462, 1488.731567, 17.291513, 8.100023, 89.300025, 85.700103, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 11301, "carshow_sfse", "concpanel_la", 0x00000000);
    LSAINTXT = CreateDynamicObject(8167, 1475.657104, 1492.053710, 15.540309, -0.000015, 360.000000, 2.500122, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 7978, "vgssairport", "newall16white", 0x00000000);
    LSAINTXT = CreateDynamicObject(8167, 1476.846313, 1491.684936, 16.940319, -0.000023, 450.000000, 2.500149, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 11301, "carshow_sfse", "concpanel_la", 0x00000000);
    LSAINTXT = CreateDynamicObject(18762, 1476.343994, 1489.948364, 12.146268, -0.000003, -0.000022, -177.799697, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 5154, "dkcargoshp_las2", "boatrailing_128", 0x00000000);
    LSAINTXT = CreateDynamicObject(18762, 1476.172729, 1494.415771, 12.146268, -0.000003, -0.000022, -177.799697, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 5154, "dkcargoshp_las2", "boatrailing_128", 0x00000000);
    LSAINTXT = CreateDynamicObject(19373, 1476.627197, 1496.745239, 15.370332, -0.000022, 0.000003, -87.199890, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 7978, "vgssairport", "Industdoor9white", 0x00000000);
    LSAINTXT = CreateDynamicObject(8167, 1475.645507, 1488.909667, 15.520311, 0.000044, 360.000000, 91.599754, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 7978, "vgssairport", "newall16white", 0x00000000);
    LSAINTXT = CreateDynamicObject(8167, 1478.103515, 1491.759277, 15.540309, -0.000015, 360.000000, 2.500122, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 7978, "vgssairport", "newall16white", 0x00000000);
    LSAINTXT = CreateDynamicObject(19447, 1456.327880, 1537.776000, 15.430334, 0.000007, 270.000000, 89.999969, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 4830, "airport2", "tardor9", 0xFFC6BCB0);
    LSAINTXT = CreateDynamicObject(19447, 1446.727050, 1537.776000, 15.430334, 0.000000, 270.000000, 89.999992, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 4830, "airport2", "tardor9", 0xFFC6BCB0);
    LSAINTXT = CreateDynamicObject(19447, 1456.327880, 1541.256103, 15.430334, -0.000066, 270.000000, -89.999801, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 4830, "airport2", "tardor9", 0xFFC6BCB0);
    LSAINTXT = CreateDynamicObject(19447, 1456.327880, 1535.915405, 14.620330, -0.000066, 360.000000, -89.999801, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1456.327880, 1543.076416, 14.620331, -0.000066, 360.000000, -89.999801, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1456.327880, 1543.076416, 20.950340, -0.000066, 360.000000, -89.999801, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1456.327880, 1535.925659, 20.910333, -0.000066, 360.000000, -89.999801, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1456.377929, 1543.146484, 17.550336, -0.000066, 360.000000, -89.999801, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14584, "ab_abbatoir01", "ab_vent1", 0xFF6E493A);
    LSAINTXT = CreateDynamicObject(19447, 1456.377929, 1535.904663, 17.550336, 0.000007, 360.000000, 89.999969, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14584, "ab_abbatoir01", "ab_vent1", 0xFF6E493A);
    LSAINTXT = CreateDynamicObject(19377, 1461.232666, 1539.450073, 17.516267, 0.000000, 0.000022, 0.000000, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0x00000000);
    LSAINTXT = CreateDynamicObject(11714, 1461.144409, 1540.964721, 16.716283, 0.000000, 0.000022, 0.000000, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 4828, "airport3_las", "gallery01_law", 0x00000000);
    LSAINTXT = CreateDynamicObject(19447, 1446.727050, 1541.256103, 15.430334, -0.000058, 270.000000, -89.999824, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 4830, "airport2", "tardor9", 0xFFC6BCB0);
    LSAINTXT = CreateDynamicObject(19447, 1446.727050, 1535.915405, 14.620330, -0.000058, 360.000000, -89.999824, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1446.727050, 1543.076416, 14.620331, -0.000058, 360.000000, -89.999824, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1446.727050, 1543.076416, 20.950340, -0.000058, 360.000000, -89.999824, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1446.727050, 1535.925659, 20.910333, -0.000058, 360.000000, -89.999824, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1441.886840, 1547.785278, 14.620331, 0.000000, 360.000000, -0.000029, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1441.886840, 1547.785278, 20.640335, 0.000000, 360.000000, -0.000029, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1441.876831, 1531.184204, 14.620330, 0.000000, 360.000000, -0.000029, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1441.876831, 1531.213989, 22.390340, 0.000000, 360.000000, -0.000029, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(11714, 1461.144409, 1537.963867, 16.716283, 0.000000, 0.000022, 0.000000, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 4828, "airport3_las", "gallery01_law", 0x00000000);
    LSAINTXT = CreateDynamicObject(19447, 1446.777099, 1543.146484, 17.550336, -0.000066, 360.000000, -89.999801, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14584, "ab_abbatoir01", "ab_vent1", 0xFF6E493A);
    LSAINTXT = CreateDynamicObject(19447, 1446.777099, 1535.904663, 17.550336, 0.000007, 360.000000, 89.999969, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14584, "ab_abbatoir01", "ab_vent1", 0xFF6E493A);
    LSAINTXT = CreateDynamicObject(19447, 1441.886840, 1534.294189, 17.750339, 89.999992, 450.000000, -89.999961, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1441.896850, 1544.684570, 17.750339, 89.999992, 450.000000, -89.999961, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(8167, 1473.644165, 1534.859497, 15.520311, 0.000028, 360.000000, 72.099853, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 7978, "vgssairport", "newall16white", 0x00000000);
    LSAINTXT = CreateDynamicObject(8167, 1466.651977, 1536.524291, 15.803030, 6.700016, 357.400024, 82.899986, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 7978, "vgssairport", "newall16white", 0x00000000);
    LSAINTXT = CreateDynamicObject(8167, 1475.139526, 1534.312011, 15.520311, 0.000029, 360.000000, 83.499763, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 7978, "vgssairport", "newall16white", 0x00000000);
    LSAINTXT = CreateDynamicObject(8167, 1472.983154, 1532.812988, 15.520311, 0.000028, 360.000000, 72.099830, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 7978, "vgssairport", "newall16white", 0x00000000);
    LSAINTXT = CreateDynamicObject(8167, 1466.034057, 1534.611206, 15.803030, 6.700015, 357.400024, 82.900024, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 7978, "vgssairport", "newall16white", 0x00000000);
    LSAINTXT = CreateDynamicObject(8167, 1473.312255, 1533.831665, 16.920318, 0.000028, 450.000000, 72.099830, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 11301, "carshow_sfse", "concpanel_la", 0x00000000);
    LSAINTXT = CreateDynamicObject(8167, 1466.415649, 1535.458251, 17.266473, 8.100014, 89.300025, 85.700126, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 11301, "carshow_sfse", "concpanel_la", 0x00000000);
    LSAINTXT = CreateDynamicObject(8167, 1478.025512, 1531.345825, 15.540309, -0.000018, 360.000000, -177.499771, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 7978, "vgssairport", "newall16white", 0x00000000);
    LSAINTXT = CreateDynamicObject(8167, 1476.836303, 1531.714599, 16.940319, -0.000025, 450.000000, -177.499740, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 11301, "carshow_sfse", "concpanel_la", 0x00000000);
    LSAINTXT = CreateDynamicObject(18762, 1477.338623, 1533.451171, 12.146268, 0.000000, 0.000029, 2.200009, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 5154, "dkcargoshp_las2", "boatrailing_128", 0x00000000);
    LSAINTXT = CreateDynamicObject(18762, 1477.509887, 1528.983764, 12.146268, 0.000000, 0.000029, 2.200009, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 5154, "dkcargoshp_las2", "boatrailing_128", 0x00000000);
    LSAINTXT = CreateDynamicObject(19373, 1477.055419, 1526.654296, 15.370332, 0.000029, 0.000000, 92.799919, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 7978, "vgssairport", "Industdoor9white", 0x00000000);
    LSAINTXT = CreateDynamicObject(8167, 1478.025512, 1545.569458, 15.540309, -0.000014, 360.000000, 2.500122, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 7978, "vgssairport", "newall16white", 0x00000000);
    LSAINTXT = CreateDynamicObject(18763, 1462.764404, 1542.537597, 16.490329, -0.000045, 0.000025, -94.499862, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 11301, "carshow_sfse", "concpanel_la", 0x00000000);
    LSAINTXT = CreateDynamicObject(8167, 1472.819213, 1543.954833, 15.520311, 0.000050, 360.000000, 101.899749, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 7978, "vgssairport", "newall16white", 0x00000000);
    LSAINTXT = CreateDynamicObject(8167, 1466.420410, 1543.353515, 15.875083, 9.100032, 357.400024, 82.899940, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 7978, "vgssairport", "newall16white", 0x00000000);
    LSAINTXT = CreateDynamicObject(8167, 1474.388916, 1544.222900, 15.520311, 0.000049, 360.000000, 113.299697, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 7978, "vgssairport", "newall16white", 0x00000000);
    LSAINTXT = CreateDynamicObject(8167, 1473.262695, 1541.850463, 15.520311, 0.000050, 360.000000, 101.899726, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 7978, "vgssairport", "newall16white", 0x00000000);
    LSAINTXT = CreateDynamicObject(8167, 1466.027465, 1541.142211, 15.800778, 6.700029, 357.400024, 82.899978, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 7978, "vgssairport", "newall16white", 0x00000000);
    LSAINTXT = CreateDynamicObject(8167, 1473.041992, 1542.897827, 16.920318, 0.000050, 450.000000, 101.899726, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 11301, "carshow_sfse", "concpanel_la", 0x00000000);
    LSAINTXT = CreateDynamicObject(8167, 1466.254394, 1542.140869, 17.291513, 8.100030, 89.300025, 85.700080, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 11301, "carshow_sfse", "concpanel_la", 0x00000000);
    LSAINTXT = CreateDynamicObject(8167, 1475.587036, 1545.463012, 15.540309, -0.000014, 360.000000, 2.500122, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 7978, "vgssairport", "newall16white", 0x00000000);
    LSAINTXT = CreateDynamicObject(8167, 1476.776245, 1545.094238, 16.940319, -0.000023, 450.000000, 2.500149, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 11301, "carshow_sfse", "concpanel_la", 0x00000000);
    LSAINTXT = CreateDynamicObject(18762, 1476.273925, 1543.357666, 12.146268, -0.000003, -0.000029, -177.799652, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 5154, "dkcargoshp_las2", "boatrailing_128", 0x00000000);
    LSAINTXT = CreateDynamicObject(18762, 1476.102661, 1547.825073, 12.146268, -0.000003, -0.000029, -177.799652, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 5154, "dkcargoshp_las2", "boatrailing_128", 0x00000000);
    LSAINTXT = CreateDynamicObject(19373, 1476.557128, 1550.154541, 15.370332, -0.000029, 0.000003, -87.199867, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 7978, "vgssairport", "Industdoor9white", 0x00000000);
    LSAINTXT = CreateDynamicObject(8167, 1475.575439, 1542.318969, 15.520311, 0.000050, 360.000000, 91.599731, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 7978, "vgssairport", "newall16white", 0x00000000);
    LSAINTXT = CreateDynamicObject(8167, 1478.033447, 1545.168579, 15.540309, -0.000014, 360.000000, 2.500122, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 7978, "vgssairport", "newall16white", 0x00000000);
    LSAINTXT = CreateDynamicObject(19447, 1430.612915, 1567.026733, 15.430334, 0.000009, 270.000000, 179.999847, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 4830, "airport2", "tardor9", 0xFFC6BCB0);
    LSAINTXT = CreateDynamicObject(19447, 1430.612915, 1557.425903, 15.430334, 0.000000, 270.000000, -179.999862, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 4830, "airport2", "tardor9", 0xFFC6BCB0);
    LSAINTXT = CreateDynamicObject(19447, 1427.132812, 1567.026733, 15.430334, -0.000066, 270.000000, 0.000165, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 4830, "airport2", "tardor9", 0xFFC6BCB0);
    LSAINTXT = CreateDynamicObject(19447, 1432.473510, 1567.026733, 14.620329, -0.000066, 360.000000, 0.000165, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1425.312500, 1567.026733, 14.620330, -0.000066, 360.000000, 0.000165, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1425.312500, 1567.026733, 20.950340, -0.000066, 360.000000, 0.000165, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1432.463256, 1567.026733, 20.910333, -0.000066, 360.000000, 0.000165, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1425.242431, 1567.076782, 17.550336, -0.000066, 360.000000, 0.000165, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14584, "ab_abbatoir01", "ab_vent1", 0xFF6E493A);
    LSAINTXT = CreateDynamicObject(19447, 1432.484252, 1567.076782, 17.550336, 0.000009, 360.000000, 179.999847, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14584, "ab_abbatoir01", "ab_vent1", 0xFF6E493A);
    LSAINTXT = CreateDynamicObject(19377, 1428.938842, 1571.931518, 17.516267, 0.000014, 0.000022, 89.999923, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0x00000000);
    LSAINTXT = CreateDynamicObject(11714, 1427.424194, 1571.843261, 16.716283, 0.000014, 0.000022, 89.999923, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 4828, "airport3_las", "gallery01_law", 0x00000000);
    LSAINTXT = CreateDynamicObject(19447, 1427.132812, 1557.425903, 15.430334, -0.000058, 270.000000, 0.000144, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 4830, "airport2", "tardor9", 0xFFC6BCB0);
    LSAINTXT = CreateDynamicObject(19447, 1432.473510, 1557.425903, 14.620330, -0.000058, 360.000000, 0.000144, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1425.312500, 1557.425903, 14.620331, -0.000058, 360.000000, 0.000144, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1425.312500, 1557.425903, 20.950340, -0.000058, 360.000000, 0.000144, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1432.463256, 1557.425903, 20.910333, -0.000058, 360.000000, 0.000144, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1441.876831, 1521.554443, 14.620330, 0.000000, 360.000000, -0.000029, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1441.876831, 1521.584228, 22.390340, 0.000000, 360.000000, -0.000029, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1437.204711, 1552.575683, 14.620330, 0.000014, 360.000000, 89.999900, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1437.174926, 1552.575683, 20.640335, 0.000014, 360.000000, 89.999900, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(11714, 1430.425048, 1571.843261, 16.716283, 0.000014, 0.000022, 89.999923, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 4828, "airport3_las", "gallery01_law", 0x00000000);
    LSAINTXT = CreateDynamicObject(19447, 1425.242431, 1557.475952, 17.550336, -0.000066, 360.000000, 0.000165, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14584, "ab_abbatoir01", "ab_vent1", 0xFF6E493A);
    LSAINTXT = CreateDynamicObject(19447, 1432.484252, 1557.475952, 17.550336, 0.000009, 360.000000, 179.999847, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14584, "ab_abbatoir01", "ab_vent1", 0xFF6E493A);
    LSAINTXT = CreateDynamicObject(19447, 1434.094726, 1552.585693, 17.750339, 89.999992, 514.471191, -64.471199, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1441.876831, 1511.935180, 14.620330, 0.000000, 360.000000, -0.000029, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1427.104980, 1458.481079, 15.430334, 0.000007, 270.000000, -0.000120, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 4830, "airport2", "tardor9", 0xFFC6BCB0);
    LSAINTXT = CreateDynamicObject(19447, 1427.104980, 1468.081909, 15.430334, 0.000000, 270.000000, 0.000075, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 4830, "airport2", "tardor9", 0xFFC6BCB0);
    LSAINTXT = CreateDynamicObject(19447, 1430.585083, 1458.481079, 15.430334, -0.000067, 270.000000, -179.999710, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 4830, "airport2", "tardor9", 0xFFC6BCB0);
    LSAINTXT = CreateDynamicObject(19447, 1425.244384, 1458.481079, 14.620330, -0.000067, 360.000000, -179.999710, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1432.405395, 1458.481079, 14.620331, -0.000067, 360.000000, -179.999710, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1432.405395, 1458.481079, 20.950340, -0.000067, 360.000000, -179.999710, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1425.254638, 1458.481079, 20.910333, -0.000067, 360.000000, -179.999710, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1432.475463, 1458.431030, 17.550336, -0.000067, 360.000000, -179.999710, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14584, "ab_abbatoir01", "ab_vent1", 0xFF6E493A);
    LSAINTXT = CreateDynamicObject(19447, 1425.233642, 1458.431030, 17.550336, 0.000007, 360.000000, -0.000120, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14584, "ab_abbatoir01", "ab_vent1", 0xFF6E493A);
    LSAINTXT = CreateDynamicObject(19377, 1428.779052, 1454.156860, 17.516267, -0.000007, 0.000019, -89.999977, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0x00000000);
    LSAINTXT = CreateDynamicObject(11714, 1430.293701, 1454.245117, 16.716283, -0.000007, 0.000019, -89.999977, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 4828, "airport3_las", "gallery01_law", 0x00000000);
    LSAINTXT = CreateDynamicObject(19447, 1430.585083, 1468.081909, 15.430334, -0.000059, 270.000000, -179.999740, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 4830, "airport2", "tardor9", 0xFFC6BCB0);
    LSAINTXT = CreateDynamicObject(19447, 1425.244384, 1468.081909, 14.620330, -0.000059, 360.000000, -179.999740, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1432.405395, 1468.081909, 14.620331, -0.000059, 360.000000, -179.999740, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1432.405395, 1468.081909, 20.950340, -0.000059, 360.000000, -179.999740, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1425.254638, 1468.081909, 20.910333, -0.000059, 360.000000, -179.999740, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1437.114257, 1472.922119, 14.620331, 0.000000, 360.000000, -90.000022, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1437.114257, 1472.922119, 20.640335, 0.000000, 360.000000, -90.000022, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1441.876831, 1511.964965, 22.390340, 0.000000, 360.000000, -0.000029, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1441.876831, 1502.345581, 14.620330, 0.000000, 360.000000, -0.000029, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(11714, 1427.292846, 1454.245117, 16.716283, -0.000007, 0.000019, -89.999977, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 4828, "airport3_las", "gallery01_law", 0x00000000);
    LSAINTXT = CreateDynamicObject(19447, 1432.475463, 1468.031860, 17.550336, -0.000067, 360.000000, -179.999710, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14584, "ab_abbatoir01", "ab_vent1", 0xFF6E493A);
    LSAINTXT = CreateDynamicObject(19447, 1425.233642, 1468.031860, 17.550336, 0.000007, 360.000000, -0.000120, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14584, "ab_abbatoir01", "ab_vent1", 0xFF6E493A);
    LSAINTXT = CreateDynamicObject(19447, 1441.876831, 1502.375366, 22.390340, 0.000000, 360.000000, -0.000029, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1434.013549, 1472.912109, 17.750339, 89.999992, 382.409057, -112.409095, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1441.886840, 1521.134643, 17.570335, 89.999992, 450.000000, -89.999961, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1441.886840, 1504.374511, 17.570335, 89.999992, 450.000000, -89.999961, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19912, 1441.854980, 1532.691894, 18.460332, 0.000000, 0.000000, 90.000000, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 14581, "ab_mafiasuitea", "barbersmir1", 0x80FFFFFF);
    LSAINTXT = CreateDynamicObject(19912, 1441.854980, 1516.710571, 18.850328, 0.000000, 0.000000, 90.000000, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 14581, "ab_mafiasuitea", "barbersmir1", 0x80FFFFFF);
    LSAINTXT = CreateDynamicObject(19912, 1441.854980, 1508.820312, 18.850328, 0.000000, 0.000000, 270.000000, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 14581, "ab_mafiasuitea", "barbersmir1", 0x80FFFFFF);
    LSAINTXT = CreateDynamicObject(19912, 1441.854980, 1492.900390, 18.850328, 0.000000, 0.000000, 270.000000, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 14581, "ab_mafiasuitea", "barbersmir1", 0x80FFFFFF);
    LSAINTXT = CreateDynamicObject(19377, 1410.964111, 1552.614746, 20.470336, 0.000014, 0.000000, 89.999954, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19377, 1420.593261, 1536.865356, 20.470336, 0.000007, -0.000014, 179.999877, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0x00000000);
    LSAINTXT = CreateDynamicObject(19447, 1420.626098, 1550.844360, 20.240337, 89.999992, 450.000000, -89.999961, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19377, 1420.593261, 1527.225341, 20.470336, 0.000007, -0.000014, 179.999877, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0x00000000);
    LSAINTXT = CreateDynamicObject(19377, 1420.593261, 1517.586181, 20.470336, 0.000007, -0.000022, 179.999832, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0x00000000);
    LSAINTXT = CreateDynamicObject(19377, 1420.593261, 1507.946166, 20.470336, 0.000007, -0.000022, 179.999832, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0x00000000);
    LSAINTXT = CreateDynamicObject(19377, 1420.593261, 1498.326416, 20.470336, 0.000007, -0.000022, 179.999832, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0x00000000);
    LSAINTXT = CreateDynamicObject(19377, 1420.593261, 1488.716674, 20.470336, 0.000007, -0.000022, 179.999832, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0x00000000);
    LSAINTXT = CreateDynamicObject(19447, 1420.626098, 1474.593261, 20.240337, 89.999992, 450.000000, -89.999961, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19377, 1420.553710, 1472.854736, 20.470336, 0.000014, 0.000000, 89.999954, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19377, 1410.923583, 1472.854736, 20.470336, 0.000014, 0.000000, 89.999954, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19377, 1425.382690, 1529.068359, 15.457839, 0.000065, 90.000015, -90.000205, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 16639, "a51_labs", "dam_terazzo", 0xFFBBBBBB);
    LSAINTXT = CreateDynamicObject(19377, 1425.382690, 1536.379760, 15.449998, 0.000065, 90.000015, -90.000205, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 16639, "a51_labs", "dam_terazzo", 0xFFBBBBBB);
    LSAINTXT = CreateDynamicObject(19443, 1430.146118, 1524.546142, 17.377845, 0.000019, -0.000066, -0.000608, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    LSAINTXT = CreateDynamicObject(19443, 1430.146118, 1533.607177, 17.377843, 0.000019, -0.000066, -0.000608, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    LSAINTXT = CreateDynamicObject(19443, 1430.146118, 1526.107177, 14.517843, 0.000019, -0.000080, -0.000699, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    LSAINTXT = CreateDynamicObject(19443, 1430.156250, 1532.147705, 14.517843, 0.000019, -0.000080, -0.000699, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    LSAINTXT = CreateDynamicObject(1499, 1430.105346, 1530.558105, 15.477846, 0.000058, 0.000028, -90.000183, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 19787, "samplcdtvs1", "samplcdtv1screen", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 7088, "casinoshops1", "247sign2", 0x00000000);
    LSAINTXT = CreateDynamicObject(1499, 1430.124633, 1527.687744, 15.477846, -0.000058, -0.000028, 90.000000, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 19787, "samplcdtvs1", "samplcdtv1screen", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 7088, "casinoshops1", "247sign2", 0x00000000);
    LSAINTXT = CreateDynamicObject(2679, 1430.189086, 1526.881103, 15.363787, 0.000065, 360.000000, -90.000205, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(LSAINTXT, 1, 10101, "2notherbuildsfe", "ferry_build14", 0xFFFFFFFF);
    SetDynamicObjectMaterial(LSAINTXT, 2, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(LSAINTXT, 3, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    LSAINTXT = CreateDynamicObject(2679, 1430.189086, 1531.342285, 15.613787, 0.000065, 180.000000, -90.000205, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(LSAINTXT, 1, 10101, "2notherbuildsfe", "ferry_build14", 0xFFFFFFFF);
    SetDynamicObjectMaterial(LSAINTXT, 2, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(LSAINTXT, 3, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    LSAINTXT = CreateDynamicObject(19477, 1430.172485, 1526.281982, 17.037836, 0.000004, -0.000066, -0.000593, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x80777777);
    LSAINTXT = CreateDynamicObject(19477, 1430.172485, 1526.281982, 18.427837, 0.000004, -0.000066, -0.000593, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x80777777);
    LSAINTXT = CreateDynamicObject(2679, 1430.189086, 1526.881103, 17.763782, 0.000065, 360.000000, -90.000205, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(LSAINTXT, 1, 10101, "2notherbuildsfe", "ferry_build14", 0xFFFFFFFF);
    SetDynamicObjectMaterial(LSAINTXT, 2, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(LSAINTXT, 3, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    LSAINTXT = CreateDynamicObject(2679, 1430.189086, 1531.342285, 18.023788, 0.000065, 180.000000, -90.000205, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(LSAINTXT, 1, 10101, "2notherbuildsfe", "ferry_build14", 0xFFFFFFFF);
    SetDynamicObjectMaterial(LSAINTXT, 2, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(LSAINTXT, 3, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    LSAINTXT = CreateDynamicObject(19477, 1430.172485, 1529.132568, 18.637844, -0.000004, 179.999984, 179.999801, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x80777777);
    LSAINTXT = CreateDynamicObject(19477, 1430.172485, 1532.714111, 17.037836, 0.000012, -0.000059, -0.000562, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x80777777);
    LSAINTXT = CreateDynamicObject(19377, 1426.512817, 1529.049316, 19.056934, 0.000065, 90.000015, -90.000205, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 8391, "ballys01", "ballywall02_128", 0x00000000);
    LSAINTXT = CreateDynamicObject(19443, 1430.146118, 1524.546142, 13.887857, 0.000019, -0.000066, -0.000608, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    LSAINTXT = CreateDynamicObject(19443, 1430.146118, 1533.607177, 13.897846, 0.000019, -0.000066, -0.000608, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    LSAINTXT = CreateDynamicObject(2679, 1430.189086, 1526.251220, 19.213787, 0.000065, 270.000000, -90.000205, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(LSAINTXT, 1, 10101, "2notherbuildsfe", "ferry_build14", 0xFFFFFFFF);
    SetDynamicObjectMaterial(LSAINTXT, 2, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(LSAINTXT, 3, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    LSAINTXT = CreateDynamicObject(2679, 1430.189086, 1531.710205, 19.213787, 0.000065, 270.000000, -90.000205, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(LSAINTXT, 1, 10101, "2notherbuildsfe", "ferry_build14", 0xFFFFFFFF);
    SetDynamicObjectMaterial(LSAINTXT, 2, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(LSAINTXT, 3, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    LSAINTXT = CreateDynamicObject(19786, 1431.219970, 1529.377197, 19.497842, -0.000065, -0.000027, 90.000022, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 7088, "casinoshops1", "247sign1", 0x00000000);
    LSAINTXT = CreateDynamicObject(19377, 1426.513793, 1530.249023, 19.056972, 0.000065, 90.000015, -90.000205, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 8391, "ballys01", "ballywall02_128", 0x00000000);
    LSAINTXT = CreateDynamicObject(2679, 1430.189086, 1529.532226, 18.233787, 0.000065, 450.000000, -90.000205, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(LSAINTXT, 1, 10101, "2notherbuildsfe", "ferry_build14", 0xFFFFFFFF);
    SetDynamicObjectMaterial(LSAINTXT, 2, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(LSAINTXT, 3, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    LSAINTXT = CreateDynamicObject(2679, 1430.189086, 1528.981689, 18.233787, 0.000065, 450.000000, -90.000205, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(LSAINTXT, 1, 10101, "2notherbuildsfe", "ferry_build14", 0xFFFFFFFF);
    SetDynamicObjectMaterial(LSAINTXT, 2, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(LSAINTXT, 3, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    LSAINTXT = CreateDynamicObject(19477, 1430.172485, 1532.714111, 18.427837, 0.000012, -0.000059, -0.000562, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x80777777);
    LSAINTXT = CreateDynamicObject(2531, 1424.737182, 1534.219970, 15.543783, -0.000043, -0.000043, 89.999862, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 4, 14582, "casmafbar", "beerfridge128", 0x00000000);
    LSAINTXT = CreateDynamicObject(2531, 1424.737182, 1532.229003, 15.543783, -0.000043, -0.000049, 89.999816, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 4, 14582, "casmafbar", "beerfridge128", 0x00000000);
    LSAINTXT = CreateDynamicObject(2679, 1430.079223, 1526.881103, 15.363787, 0.000065, 360.000000, -90.000205, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(LSAINTXT, 1, 10101, "2notherbuildsfe", "ferry_build14", 0xFFFFFFFF);
    SetDynamicObjectMaterial(LSAINTXT, 2, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(LSAINTXT, 3, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    LSAINTXT = CreateDynamicObject(2679, 1430.079223, 1526.881103, 17.803789, 0.000065, 360.000000, -90.000205, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(LSAINTXT, 1, 10101, "2notherbuildsfe", "ferry_build14", 0xFFFFFFFF);
    SetDynamicObjectMaterial(LSAINTXT, 2, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(LSAINTXT, 3, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    LSAINTXT = CreateDynamicObject(2717, 1430.227172, 1524.606445, 17.237848, 0.000065, 0.000012, -90.000205, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    LSAINTXT = CreateDynamicObject(2717, 1430.227172, 1533.656494, 17.237848, 0.000065, 0.000012, -90.000205, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    LSAINTXT = CreateDynamicObject(2531, 1424.737182, 1533.239990, 15.543783, -0.000043, -0.000049, 89.999816, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 4, 14582, "casmafbar", "beerfridge128", 0x00000000);
    LSAINTXT = CreateDynamicObject(2679, 1430.079223, 1531.342285, 15.613787, 0.000065, 180.000000, -90.000205, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(LSAINTXT, 1, 10101, "2notherbuildsfe", "ferry_build14", 0xFFFFFFFF);
    SetDynamicObjectMaterial(LSAINTXT, 2, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(LSAINTXT, 3, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    LSAINTXT = CreateDynamicObject(2679, 1430.079223, 1531.342285, 18.033788, 0.000065, 180.000000, -90.000205, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(LSAINTXT, 1, 10101, "2notherbuildsfe", "ferry_build14", 0xFFFFFFFF);
    SetDynamicObjectMaterial(LSAINTXT, 2, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(LSAINTXT, 3, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    LSAINTXT = CreateDynamicObject(2679, 1430.079223, 1526.251220, 19.213787, -0.000065, 270.000000, 90.000114, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(LSAINTXT, 1, 10101, "2notherbuildsfe", "ferry_build14", 0xFFFFFFFF);
    SetDynamicObjectMaterial(LSAINTXT, 2, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(LSAINTXT, 3, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    LSAINTXT = CreateDynamicObject(2679, 1430.079223, 1532.641357, 19.213787, -0.000065, 270.000000, 90.000114, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(LSAINTXT, 1, 10101, "2notherbuildsfe", "ferry_build14", 0xFFFFFFFF);
    SetDynamicObjectMaterial(LSAINTXT, 2, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(LSAINTXT, 3, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    LSAINTXT = CreateDynamicObject(2679, 1430.079223, 1528.649902, 19.013782, -0.000065, 270.000000, 90.000114, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(LSAINTXT, 1, 10101, "2notherbuildsfe", "ferry_build14", 0xFFFFFFFF);
    SetDynamicObjectMaterial(LSAINTXT, 2, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(LSAINTXT, 3, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    LSAINTXT = CreateDynamicObject(2679, 1430.079711, 1529.550537, 19.013782, -0.000065, 270.000000, 90.000114, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(LSAINTXT, 1, 10101, "2notherbuildsfe", "ferry_build14", 0xFFFFFFFF);
    SetDynamicObjectMaterial(LSAINTXT, 2, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(LSAINTXT, 3, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    LSAINTXT = CreateDynamicObject(1347, 1430.634887, 1527.155517, 16.018487, 0.000065, 0.000006, -90.000205, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 12984, "sw_block11", "shoptopb128", 0x00000000);
    LSAINTXT = CreateDynamicObject(1347, 1430.634887, 1531.075927, 16.018487, 0.000065, 0.000006, -90.000205, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 12984, "sw_block11", "shoptopb128", 0x00000000);
    LSAINTXT = CreateDynamicObject(2658, 1430.265747, 1527.272705, 17.338491, -0.000065, -0.000003, 90.000160, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 1733, "cj_commercial", "CJ_SPRUNK_FRONT", 0x00000000);
    LSAINTXT = CreateDynamicObject(2658, 1430.265747, 1530.933349, 17.338491, -0.000065, -0.000003, 90.000160, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 1733, "cj_commercial", "CJ_SPRUNK_FRONT", 0x00000000);
    LSAINTXT = CreateDynamicObject(2531, 1424.737182, 1535.230957, 15.543783, -0.000043, -0.000043, 89.999862, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 4, 14582, "casmafbar", "beerfridge128", 0x00000000);
    LSAINTXT = CreateDynamicObject(19377, 1425.382690, 1541.596923, 13.887845, 0.000037, 360.000000, -90.000114, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF08996E);
    LSAINTXT = CreateDynamicObject(19377, 1425.382690, 1523.865478, 13.887845, 0.000037, 360.000000, -90.000114, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF08996E);
    LSAINTXT = CreateDynamicObject(2543, 1427.665649, 1530.110107, 15.543782, -0.000043, 0.000081, 90.000106, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 2, 7088, "casinoshops1", "247sign1", 0x00000000);
    LSAINTXT = CreateDynamicObject(2543, 1427.665649, 1531.100585, 15.543782, -0.000043, 0.000081, 90.000106, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 2, 7088, "casinoshops1", "247sign1", 0x00000000);
    LSAINTXT = CreateDynamicObject(2541, 1426.625122, 1531.100830, 15.543782, 0.000043, -0.000066, -90.000640, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 2212, "burger_tray", "sprinkler", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 2212, "burger_tray", "burgerside", 0x00000000);
    LSAINTXT = CreateDynamicObject(2541, 1426.625122, 1530.110351, 15.543782, 0.000043, -0.000066, -90.000640, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 2212, "burger_tray", "sprinkler", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 2212, "burger_tray", "burgerside", 0x00000000);
    LSAINTXT = CreateDynamicObject(2542, 1426.763549, 1537.854003, 15.543782, 0.000043, 0.000089, -90.000137, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    LSAINTXT = CreateDynamicObject(2541, 1427.693725, 1537.854003, 15.543782, -0.000043, -0.000072, 89.999526, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    LSAINTXT = CreateDynamicObject(2585, 1427.147827, 1529.477539, 16.443777, -0.000073, -0.000043, 0.000608, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFF22B1C);
    SetDynamicObjectMaterial(LSAINTXT, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 3, 7088, "casinoshops1", "247sign1", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 4, 18632, "fishingrod", "handle2", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 5, 19625, "ciggy1", "ciggy1", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 6, 19897, "cigarettepacks", "cigarettepack1", 0x00000000);
    LSAINTXT = CreateDynamicObject(19477, 1430.172485, 1535.843994, 17.037836, 0.000012, -0.000072, -0.000654, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x80777777);
    LSAINTXT = CreateDynamicObject(2582, 1427.928833, 1526.548339, 16.023784, 0.000000, 0.000050, 179.999801, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 7088, "casinoshops1", "GB_nastybar19", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 3, 12848, "cunte_town1", "sprunk_temp", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 5, 18264, "cw2_cinemablockcs_t", "GB_Last of Mullets", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 6, 15040, "cuntcuts", "piratesign01_128", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 7, 7088, "casinoshops1", "247sign1", 0x00000000);
    LSAINTXT = CreateDynamicObject(19477, 1430.172485, 1535.843994, 18.427837, 0.000012, -0.000072, -0.000654, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x80777777);
    LSAINTXT = CreateDynamicObject(2543, 1427.665649, 1532.061523, 15.543782, -0.000043, 0.000089, 90.000106, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 2, 7088, "casinoshops1", "247sign1", 0x00000000);
    LSAINTXT = CreateDynamicObject(2543, 1427.665649, 1533.052001, 15.543782, -0.000043, 0.000089, 90.000106, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 2, 7088, "casinoshops1", "247sign1", 0x00000000);
    LSAINTXT = CreateDynamicObject(2541, 1426.625122, 1533.052246, 15.543782, 0.000043, -0.000073, -90.000686, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    LSAINTXT = CreateDynamicObject(2541, 1426.625122, 1532.061767, 15.543782, 0.000043, -0.000073, -90.000686, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    LSAINTXT = CreateDynamicObject(2542, 1426.763549, 1536.863037, 15.543782, 0.000043, 0.000095, -90.000137, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    LSAINTXT = CreateDynamicObject(2542, 1426.763549, 1535.902587, 15.543782, 0.000043, 0.000095, -90.000137, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    LSAINTXT = CreateDynamicObject(2541, 1427.693725, 1535.902587, 15.543782, -0.000043, -0.000079, 89.999481, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 2767, "cb_details", "pattern1_cb", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 2767, "cb_details", "fillets_cb", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 3, 8618, "ceasersign", "ceaserssign01_128", 0x00000000);
    LSAINTXT = CreateDynamicObject(2541, 1427.693725, 1536.863037, 15.543782, -0.000043, -0.000079, 89.999481, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 2767, "cb_details", "pattern1_cb", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 2767, "cb_details", "fillets_cb", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 3, 8618, "ceasersign", "ceaserssign01_128", 0x00000000);
    LSAINTXT = CreateDynamicObject(2582, 1429.379272, 1526.548339, 16.023784, 0.000000, 0.000050, 179.999801, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 7088, "casinoshops1", "GB_nastybar19", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 3, 12848, "cunte_town1", "sprunk_temp", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 5, 18264, "cw2_cinemablockcs_t", "GB_Last of Mullets", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 6, 15040, "cuntcuts", "piratesign01_128", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 7, 7088, "casinoshops1", "247sign1", 0x00000000);
    LSAINTXT = CreateDynamicObject(2626, 1426.218505, 1525.958740, 15.953781, 0.000000, 0.000050, 179.999801, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 3, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    LSAINTXT = CreateDynamicObject(2579, 1429.361450, 1524.078613, 17.303783, 0.000000, 0.000050, 179.999801, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFF22B1C);
    SetDynamicObjectMaterial(LSAINTXT, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 18632, "fishingrod", "line", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 3, 7088, "casinoshops1", "247sign1", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 4, 18632, "fishingrod", "handle2", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 5, 19625, "ciggy1", "ciggy1", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 6, 19897, "cigarettepacks", "cigarettepack1", 0x00000000);
    LSAINTXT = CreateDynamicObject(2579, 1428.211303, 1524.078613, 17.303783, 0.000000, 0.000050, 179.999801, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFF22B1C);
    SetDynamicObjectMaterial(LSAINTXT, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 18632, "fishingrod", "line", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 3, 7088, "casinoshops1", "247sign1", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 4, 18632, "fishingrod", "handle2", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 5, 19625, "ciggy1", "ciggy1", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 6, 19897, "cigarettepacks", "cigarettepack1", 0x00000000);
    LSAINTXT = CreateDynamicObject(2578, 1427.091186, 1524.078613, 17.303783, 0.000000, 0.000050, 179.999801, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFF22B1C);
    SetDynamicObjectMaterial(LSAINTXT, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 3, 7088, "casinoshops1", "247sign1", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 4, 18632, "fishingrod", "handle2", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 5, 19625, "ciggy1", "ciggy1", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 6, 19897, "cigarettepacks", "cigarettepack1", 0x00000000);
    LSAINTXT = CreateDynamicObject(2578, 1425.971313, 1524.078613, 17.303783, 0.000000, 0.000050, 179.999801, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFF22B1C);
    SetDynamicObjectMaterial(LSAINTXT, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 3, 7088, "casinoshops1", "247sign1", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 4, 18632, "fishingrod", "handle2", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 5, 19625, "ciggy1", "ciggy1", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 6, 19897, "cigarettepacks", "cigarettepack1", 0x00000000);
    LSAINTXT = CreateDynamicObject(19477, 1430.172485, 1538.604736, 17.037836, 0.000012, -0.000081, -0.000699, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x80777777);
    LSAINTXT = CreateDynamicObject(19477, 1430.172485, 1538.604736, 18.427837, 0.000012, -0.000081, -0.000699, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x80777777);
    LSAINTXT = CreateDynamicObject(19443, 1430.175415, 1536.147705, 15.502840, 89.999992, 244.437255, -64.437301, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    LSAINTXT = CreateDynamicObject(19443, 1430.195434, 1539.598388, 15.502840, 89.999992, 244.437255, -64.437301, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    LSAINTXT = CreateDynamicObject(19447, 1430.201416, 1527.663085, 20.212850, 0.000000, 0.000007, -0.000059, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    LSAINTXT = CreateDynamicObject(2531, 1424.737182, 1536.201660, 15.543783, -0.000050, -0.000043, 89.999885, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 4, 14582, "casmafbar", "beerfridge128", 0x00000000);
    LSAINTXT = CreateDynamicObject(19443, 1430.175415, 1539.569091, 16.732847, 0.000000, -0.000007, 179.999801, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    LSAINTXT = CreateDynamicObject(19377, 1426.512817, 1536.259033, 19.076936, 0.000065, 90.000015, -90.000205, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 8391, "ballys01", "ballywall02_128", 0x00000000);
    LSAINTXT = CreateDynamicObject(19447, 1430.221435, 1536.942138, 20.202850, 0.000000, 0.000007, -0.000059, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    LSAINTXT = CreateDynamicObject(2531, 1424.737182, 1537.212646, 15.543783, -0.000050, -0.000043, 89.999885, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 4, 14582, "casmafbar", "beerfridge128", 0x00000000);
    LSAINTXT = CreateDynamicObject(19377, 1423.972534, 1528.714355, 13.887845, 0.000044, 360.000000, -0.000136, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF08996E);
    LSAINTXT = CreateDynamicObject(19377, 1423.932495, 1536.823730, 13.887845, 0.000044, 360.000000, -0.000136, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF08996E);
    LSAINTXT = CreateDynamicObject(19918, 1425.570190, 1526.131103, 16.463787, 0.000000, 0.000059, 179.999801, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 2221, "donut_tray", "rustycoffeerap_rb", 0x00000000);
    LSAINTXT = CreateDynamicObject(19918, 1425.570190, 1525.810791, 16.463787, -0.000003, 0.000059, 174.499801, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 2221, "donut_tray", "rustycoffeerap_rb", 0x00000000);
    LSAINTXT = CreateDynamicObject(18066, 1424.184570, 1531.898681, 18.363786, 0.000050, 0.000000, -90.000129, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 7088, "casinoshops1", "crapdoor1_256", 0x00000000);
    LSAINTXT = CreateDynamicObject(11727, 1426.234008, 1533.195556, 18.983783, 89.999992, 244.403442, -64.403327, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    LSAINTXT = CreateDynamicObject(11727, 1426.234008, 1530.045166, 18.983783, 89.999992, 244.403442, -64.403327, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    LSAINTXT = CreateDynamicObject(11727, 1426.234008, 1526.235107, 18.983783, 89.999992, 244.403411, -64.403327, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    LSAINTXT = CreateDynamicObject(19377, 1424.126342, 1491.515502, 16.990385, 0.000007, 0.000007, 0.000006, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    LSAINTXT = CreateDynamicObject(1431, 1429.622558, 1539.958496, 15.543782, 0.000049, 0.000000, -90.000129, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 2212, "burger_tray", "burgerkids", 0x00000000);
    LSAINTXT = CreateDynamicObject(3660, 1416.067016, 1521.017211, 17.440368, 0.000014, 0.000000, 89.999954, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(18766, 1425.309448, 1509.044555, 17.523752, 0.000000, 0.000020, 0.000000, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    LSAINTXT = CreateDynamicObject(18766, 1425.309448, 1520.224487, 17.523752, 0.000000, 0.000020, 0.000000, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    LSAINTXT = CreateDynamicObject(3660, 1416.067016, 1501.377319, 17.440368, 0.000014, 0.000000, 89.999954, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19313, 1430.182495, 1509.910278, 11.893753, -0.000028, 90.000000, -89.999908, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 14581, "ab_mafiasuitea", "barbersmir1", 0x90333333);
    LSAINTXT = CreateDynamicObject(19313, 1430.182495, 1519.520263, 11.893753, 0.000028, 90.000000, 89.999908, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 14581, "ab_mafiasuitea", "barbersmir1", 0x90333333);
    LSAINTXT = CreateDynamicObject(1499, 1430.194335, 1513.197387, 15.473752, 0.000028, 0.000000, 89.999908, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 14581, "ab_mafiasuitea", "barbersmir1", 0xFF555555);
    LSAINTXT = CreateDynamicObject(1499, 1430.194335, 1516.217651, 15.473752, -0.000028, 0.000000, -89.999908, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 14581, "ab_mafiasuitea", "barbersmir1", 0xFF555555);
    LSAINTXT = CreateDynamicObject(19377, 1425.920776, 1515.802368, 19.003749, 0.000000, 90.000022, 0.000000, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 16640, "a51", "ws_stationfloor", 0xFF333333);
    LSAINTXT = CreateDynamicObject(19377, 1425.920776, 1513.562011, 19.023750, 0.000000, 90.000022, 0.000000, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 16640, "a51", "ws_stationfloor", 0xFF333333);
    LSAINTXT = CreateDynamicObject(19433, 1430.181152, 1514.704101, 18.763750, 89.999992, 270.000000, -89.999969, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    LSAINTXT = CreateDynamicObject(18762, 1429.830078, 1512.044555, 19.513763, 89.999992, 90.000000, -89.999969, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    LSAINTXT = CreateDynamicObject(18762, 1429.830078, 1517.264526, 19.513763, 89.999992, 90.000000, -89.999969, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    LSAINTXT = CreateDynamicObject(18762, 1429.810058, 1514.514526, 19.303762, 89.999992, 90.000000, -89.999969, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    LSAINTXT = CreateDynamicObject(19433, 1430.181152, 1513.044067, 17.073755, 0.000014, 0.000014, 89.999961, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 16640, "a51", "ws_stationfloor", 0xFF333333);
    LSAINTXT = CreateDynamicObject(19433, 1430.181152, 1516.373901, 17.073755, 0.000014, 0.000014, 89.999961, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 16640, "a51", "ws_stationfloor", 0xFF333333);
    LSAINTXT = CreateDynamicObject(19377, 1420.600097, 1514.666625, 13.685437, 0.000000, 0.000019, 0.000000, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 4835, "airoads_las", "aarprt8LAS", 0x00000000);
    LSAINTXT = CreateDynamicObject(2368, 1423.065673, 1511.904785, 15.533746, -0.000014, 0.000000, -89.999923, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 2, 16640, "a51", "ws_stationfloor", 0xFF555555);
    LSAINTXT = CreateDynamicObject(19477, 1431.072021, 1514.875976, 20.186897, -1.700011, 0.000049, -0.000003, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(LSAINTXT, 0, "(", 130, "Wingdings", 199, 1, 0xFFFFFFFF, 0x00000000, 1);
    LSAINTXT = CreateDynamicObject(19433, 1430.951416, 1514.704101, 19.953752, 89.999992, 270.000000, -89.999969, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF555555);
    LSAINTXT = CreateDynamicObject(19477, 1431.081909, 1514.804931, 19.509687, -0.000007, 0.000049, -0.000003, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(LSAINTXT, 0, "ELECTRONIC STORE", 130, "Calibri", 75, 1, 0xFFFFFFFF, 0x00000000, 1);
    LSAINTXT = CreateDynamicObject(2368, 1423.085693, 1514.724487, 15.533746, -0.000014, 0.000000, -89.999923, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 2, 16640, "a51", "ws_stationfloor", 0xFF555555);
    LSAINTXT = CreateDynamicObject(3431, 1425.592529, 1519.259277, 15.913743, 0.000020, 0.000000, 89.999931, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    LSAINTXT = CreateDynamicObject(18766, 1424.621337, 1509.224243, 15.839267, 0.000000, 30.000022, 0.000000, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 4835, "airoads_las", "aarprt8LAS", 0x00000000);
    LSAINTXT = CreateDynamicObject(18766, 1424.621337, 1520.104125, 15.839267, 0.000000, 30.000022, 0.000000, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 4835, "airoads_las", "aarprt8LAS", 0x00000000);
    LSAINTXT = CreateDynamicObject(2375, 1429.642822, 1516.993530, 15.463747, 0.000020, 0.000000, 89.999931, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 3, 2423, "cj_ff_counters", "CJ_Laminate1", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 4, 16640, "a51", "ws_stationfloor", 0xFF555555);
    SetDynamicObjectMaterial(LSAINTXT, 5, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    LSAINTXT = CreateDynamicObject(2375, 1429.642822, 1510.163818, 15.463747, 0.000020, 0.000000, 89.999931, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 3, 2423, "cj_ff_counters", "CJ_Laminate1", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 4, 16640, "a51", "ws_stationfloor", 0xFF555555);
    SetDynamicObjectMaterial(LSAINTXT, 5, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    LSAINTXT = CreateDynamicObject(3431, 1427.462524, 1519.259277, 15.823745, 0.000020, 0.000000, 89.999931, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    LSAINTXT = CreateDynamicObject(2387, 1426.008056, 1511.728881, 15.533744, 0.000000, 0.000020, 0.000000, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 3, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 4, 10101, "2notherbuildsfe", "ferry_build14", 0xFF333333);
    LSAINTXT = CreateDynamicObject(2387, 1426.008056, 1517.088989, 15.533744, 0.000000, 0.000020, 0.000000, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 3, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 4, 10101, "2notherbuildsfe", "ferry_build14", 0xFF333333);
    LSAINTXT = CreateDynamicObject(19939, 1426.330078, 1517.214965, 16.363748, 0.000000, 90.000030, 0.000000, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    LSAINTXT = CreateDynamicObject(19939, 1426.035400, 1517.236694, 16.363748, 0.000000, 89.999961, 179.999664, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    LSAINTXT = CreateDynamicObject(19939, 1426.330078, 1511.824829, 16.363748, 0.000000, 90.000038, 0.000000, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    LSAINTXT = CreateDynamicObject(19939, 1426.035400, 1511.846557, 16.363748, 0.000000, 89.999954, 179.999618, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    LSAINTXT = CreateDynamicObject(2375, 1429.522827, 1509.543212, 15.463747, 0.000000, -0.000020, 179.999862, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 3, 2423, "cj_ff_counters", "CJ_Laminate1", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 4, 16640, "a51", "ws_stationfloor", 0xFF555555);
    SetDynamicObjectMaterial(LSAINTXT, 5, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    LSAINTXT = CreateDynamicObject(2375, 1427.923217, 1509.543212, 15.463747, 0.000000, -0.000020, 179.999862, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 3, 2423, "cj_ff_counters", "CJ_Laminate1", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 4, 16640, "a51", "ws_stationfloor", 0xFF555555);
    SetDynamicObjectMaterial(LSAINTXT, 5, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    LSAINTXT = CreateDynamicObject(2585, 1429.008911, 1509.879516, 16.893751, 0.000000, -0.000020, 179.999862, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 4, 18031, "cj_exp", "CJ_DS_WINDOW", 0x00000000);
    LSAINTXT = CreateDynamicObject(2585, 1427.648681, 1509.879516, 16.893751, 0.000000, -0.000020, 179.999862, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 4, 2028, "cj_games", "CJ_CONSOLETOP", 0x00000000);
    LSAINTXT = CreateDynamicObject(2585, 1426.048461, 1509.879516, 16.893751, 0.000000, -0.000020, 179.999862, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 4, 2655, "cj_banner", "CJ_SUBURBAN_1", 0x00000000);
    LSAINTXT = CreateDynamicObject(3468, 1430.855590, 1517.026489, 16.123750, 0.000020, 0.000000, 89.999931, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 2767, "cb_details", "CJ_cbell_top", 0x00000000);
    LSAINTXT = CreateDynamicObject(3468, 1430.855590, 1512.406494, 16.123750, 0.000020, 0.000000, 89.999931, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 2767, "cb_details", "CJ_cbell_top", 0x00000000);
    LSAINTXT = CreateDynamicObject(19373, 1417.176391, 1538.550415, 16.915334, 0.000015, 0.000006, 179.999801, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 16640, "a51", "scratchedmetal", 0xFF555555);
    LSAINTXT = CreateDynamicObject(18765, 1419.102050, 1504.757812, 13.003746, 0.000000, 0.000020, 0.000000, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    LSAINTXT = CreateDynamicObject(19373, 1417.166625, 1537.990600, 18.385341, 0.000015, 0.000006, 179.999801, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(LSAINTXT, 0, "INFORMATION", 140, "Arial", 50, 1, 0xFFFFFFFF, 0x00000000, 1);
    LSAINTXT = CreateDynamicObject(2412, 1429.482055, 1513.585571, 15.533748, 0.000020, 0.000000, 89.999931, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 19787, "samplcdtvs1", "samplcdtv1screen", 0x00000000);
    LSAINTXT = CreateDynamicObject(2412, 1429.482055, 1516.456542, 15.533748, 0.000020, 0.000000, 89.999931, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 19787, "samplcdtvs1", "samplcdtv1screen", 0x00000000);
    LSAINTXT = CreateDynamicObject(19373, 1417.177124, 1537.360351, 16.915334, 0.000015, 0.000006, 179.999801, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 16640, "a51", "scratchedmetal", 0xFF555555);
    LSAINTXT = CreateDynamicObject(1297, 1424.953125, 1509.937500, 18.692062, 0.000000, 173.400009, 179.999862, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 19480, "signsurf", "sign", 0x00000000);
    LSAINTXT = CreateDynamicObject(1297, 1429.816284, 1519.347656, 18.703142, 0.000000, 173.400009, 179.999862, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 19480, "signsurf", "sign", 0x00000000);
    LSAINTXT = CreateDynamicObject(1499, 1430.416015, 1497.497802, 15.495372, 0.000095, 0.000027, -90.000297, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 2732, "cj_zip_sign", "CJ_ZIP_2", 0x00000000);
    LSAINTXT = CreateDynamicObject(1499, 1430.435302, 1494.627441, 15.495372, -0.000095, -0.000025, 90.000114, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 2732, "cj_zip_sign", "CJ_ZIP_2", 0x00000000);
    LSAINTXT = CreateDynamicObject(19477, 1430.483154, 1496.072265, 18.655372, -0.000004, 179.999984, 179.999801, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x80777777);
    LSAINTXT = CreateDynamicObject(1897, 1430.466796, 1495.626708, 18.145376, -89.999992, 244.466278, 64.465911, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(1897, 1430.456787, 1496.507324, 18.145376, -89.999992, 64.466262, 64.465911, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(1897, 1430.466796, 1495.626708, 18.335378, -89.999992, 244.465545, 64.465171, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(1897, 1430.456787, 1496.507324, 18.335378, -89.999992, 64.465263, 64.464866, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(1897, 1430.450439, 1494.516113, 15.545440, 0.000018, 180.197845, 0.198669, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(1897, 1430.458251, 1494.516113, 17.735427, 0.000018, 180.197845, 0.198669, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(1897, 1430.428466, 1497.597900, 16.485414, 0.000000, 180.197845, -179.801925, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(1897, 1430.426025, 1497.597900, 17.095417, 0.000000, 180.197845, -179.801925, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(19443, 1430.440063, 1498.333496, 17.210371, 0.000000, 0.000036, 179.999801, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    LSAINTXT = CreateDynamicObject(19443, 1430.450195, 1493.823242, 17.210371, 0.000000, 0.000036, 179.999801, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    LSAINTXT = CreateDynamicObject(19443, 1430.450927, 1494.762207, 19.080369, 89.999992, 244.403381, -64.403327, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    LSAINTXT = CreateDynamicObject(19443, 1430.451904, 1497.382812, 19.080369, 89.999992, 244.403381, -64.403327, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    LSAINTXT = CreateDynamicObject(1897, 1430.447265, 1493.076660, 16.975439, 0.000018, 180.197845, -179.801467, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(1897, 1430.441894, 1493.076660, 18.425436, 0.000018, 180.197845, -179.801467, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(1897, 1430.491333, 1490.514892, 16.224807, 0.000004, 180.197845, 90.198532, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(1897, 1430.491699, 1490.494873, 18.455177, -0.000037, 0.197873, 90.198432, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(1897, 1430.452514, 1491.555175, 19.445440, 89.999992, 426.135864, -65.739562, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(1897, 1430.457641, 1493.785156, 19.445440, 89.999992, -65.360252, -114.243453, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(1897, 1430.419311, 1493.354736, 15.995437, -89.999992, 424.939971, 64.543601, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(1897, 1430.437622, 1491.555175, 15.995437, -89.999992, 244.924026, 64.527626, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(1897, 1430.491333, 1489.764404, 16.224807, 0.000000, 180.197845, 90.198554, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(19325, 1430.484130, 1489.646972, 17.980371, 0.000000, 179.999984, 0.000091, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x90FFFFFF);
    LSAINTXT = CreateDynamicObject(1897, 1430.491699, 1489.744384, 18.455177, -0.000044, 0.197873, 90.198455, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(1897, 1430.441894, 1487.124267, 16.975439, 0.000018, 180.197845, 0.198652, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(1897, 1430.447265, 1487.124267, 18.425436, 0.000018, 180.197845, 0.198652, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(1897, 1430.452514, 1488.294677, 19.445440, 89.999992, 425.507415, -65.111091, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(1897, 1430.477661, 1490.524658, 19.445440, 89.999992, 466.120819, -105.724617, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(1897, 1430.393676, 1489.373535, 16.005435, -89.999992, 425.012207, 64.615890, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(1897, 1430.407592, 1488.294677, 15.995437, -89.999992, 244.980361, 64.583953, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(19443, 1430.450927, 1488.911621, 20.370367, 89.999992, 244.403381, -64.403327, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    LSAINTXT = CreateDynamicObject(19443, 1430.440917, 1488.867919, 15.080369, 89.999992, 244.403350, -64.403327, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    LSAINTXT = CreateDynamicObject(19443, 1430.440917, 1492.347656, 15.080369, 89.999992, 244.403350, -64.403327, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    LSAINTXT = CreateDynamicObject(19443, 1430.451538, 1492.380615, 20.370367, 89.999992, 244.403381, -64.403327, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    LSAINTXT = CreateDynamicObject(19443, 1430.451538, 1495.870361, 20.370367, 89.999992, 244.403381, -64.403327, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    LSAINTXT = CreateDynamicObject(19443, 1430.451538, 1499.349853, 20.370367, 89.999992, 244.403381, -64.403327, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    LSAINTXT = CreateDynamicObject(19443, 1430.451538, 1502.850341, 20.370367, 89.999992, 244.403381, -64.403327, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    LSAINTXT = CreateDynamicObject(19443, 1430.447998, 1504.489501, 20.370367, 89.999992, 244.403381, -64.403327, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    LSAINTXT = CreateDynamicObject(1897, 1430.447265, 1505.039306, 16.975439, 0.000018, 180.197845, -179.801467, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(1897, 1430.441894, 1505.039306, 18.425436, 0.000018, 180.197845, -179.801467, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(1897, 1430.441894, 1502.347412, 16.975439, 0.000018, 180.197845, 0.198652, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(1897, 1430.447265, 1502.537597, 18.425436, -0.000014, 0.197881, -179.801406, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(1897, 1430.452514, 1503.517822, 19.445440, 89.999992, 425.507415, -65.111091, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(1897, 1430.463012, 1505.067382, 19.445440, 89.999992, -64.723426, -114.880218, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(1897, 1430.417114, 1505.127197, 15.995437, -89.999992, 424.903839, 64.507408, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(1897, 1430.437622, 1503.517822, 15.995437, -89.999992, 244.895858, 64.499404, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(1897, 1430.447265, 1501.778808, 16.975439, 0.000000, 180.197845, -179.801467, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(19325, 1430.484130, 1501.609619, 17.980371, 0.000000, 179.999984, 0.000136, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x90FFFFFF);
    LSAINTXT = CreateDynamicObject(1897, 1430.442260, 1501.588623, 18.425436, -0.000012, 360.197814, 0.198592, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(1897, 1430.441894, 1499.086914, 16.975439, 0.000018, 180.197845, 0.198697, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(1897, 1430.447265, 1499.086914, 18.425436, 0.000018, 180.197845, 0.198697, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(1897, 1430.452514, 1500.257324, 19.445440, 89.999992, 425.188934, -64.792572, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(1897, 1430.457641, 1502.487304, 19.445440, 89.999992, -64.400604, -115.203071, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(1897, 1430.393676, 1501.336181, 16.005435, -89.999992, 424.939971, 64.543601, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(1897, 1430.407592, 1500.257324, 15.995437, -89.999992, 244.924026, 64.527626, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(19443, 1430.470214, 1505.774902, 17.210371, 0.000000, 0.000036, 179.999801, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    LSAINTXT = CreateDynamicObject(19443, 1430.450195, 1505.774902, 20.490364, 0.000000, 0.000036, 179.999801, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    LSAINTXT = CreateDynamicObject(19443, 1430.450195, 1486.393554, 17.210371, 0.000000, 0.000044, 179.999801, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    LSAINTXT = CreateDynamicObject(19443, 1430.450195, 1486.393554, 20.490364, 0.000000, 0.000044, 179.999801, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    LSAINTXT = CreateDynamicObject(19443, 1430.440917, 1500.744384, 15.080369, 89.999992, 244.403381, -64.403327, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    LSAINTXT = CreateDynamicObject(19443, 1430.440917, 1504.224121, 15.080369, 89.999992, 244.403381, -64.403327, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    LSAINTXT = CreateDynamicObject(19377, 1425.567138, 1486.616455, 16.990385, 0.000000, 0.000000, -89.999969, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    LSAINTXT = CreateDynamicObject(19377, 1425.567138, 1505.656250, 16.990385, 0.000000, 0.000000, -89.999969, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    LSAINTXT = CreateDynamicObject(19377, 1425.661987, 1491.781127, 15.446000, 0.000065, 90.000015, -90.000205, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14865, "gf2", "mp_bobbie_wall", 0x00000000);
    LSAINTXT = CreateDynamicObject(19805, 1427.373291, 1486.810302, 17.870349, 0.000000, 0.000000, 179.999801, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 2732, "cj_zip_sign", "CJ_ZIP_2", 0x00000000);
    LSAINTXT = CreateDynamicObject(2394, 1428.698608, 1487.167968, 16.290353, 0.000000, 0.000000, 179.999801, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 2374, "cloth_track_t", "tshirterisyell", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 2374, "cloth_track_t", "tshirterisyell", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 2374, "cloth_track_t", "tshirterisyell", 0x00000000);
    LSAINTXT = CreateDynamicObject(2394, 1428.698608, 1487.838623, 16.290353, 0.000000, 0.000000, 179.999801, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 2374, "cloth_track_t", "trackytop1pro", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 2374, "cloth_track_t", "trackytop1pro", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 2374, "cloth_track_t", "trackytop1pro", 0x00000000);
    LSAINTXT = CreateDynamicObject(2394, 1426.097167, 1487.167968, 16.290353, 0.000000, 0.000006, 179.999801, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 2374, "cloth_track_t", "shirtshortblu", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 2374, "cloth_track_t", "shirtshortblu", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 2374, "cloth_track_t", "shirtshortblu", 0x00000000);
    LSAINTXT = CreateDynamicObject(2394, 1426.097167, 1487.838623, 16.290353, 0.000000, 0.000006, 179.999801, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 2374, "cloth_track_t", "sweatrstar", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 2374, "cloth_track_t", "sweatrstar", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 2374, "cloth_track_t", "sweatrstar", 0x00000000);
    LSAINTXT = CreateDynamicObject(2411, 1429.324707, 1491.824218, 16.930347, -0.000006, 0.000000, 89.999961, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14668, "711c", "gun_ceiling1128", 0x00000000);
    LSAINTXT = CreateDynamicObject(18762, 1429.340454, 1491.853271, 14.130344, 0.000000, 0.000006, 179.999801, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    LSAINTXT = CreateDynamicObject(2411, 1429.324707, 1489.463134, 16.930347, -0.000014, 0.000000, 89.999984, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14668, "711c", "gun_ceiling1128", 0x00000000);
    LSAINTXT = CreateDynamicObject(18762, 1429.340454, 1489.492187, 14.130344, 0.000000, 0.000014, 179.999801, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    LSAINTXT = CreateDynamicObject(19805, 1427.373291, 1505.521484, 17.870349, 0.000000, 0.000019, -0.000104, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 2732, "cj_zip_sign", "CJ_ZIP_2", 0x00000000);
    LSAINTXT = CreateDynamicObject(2394, 1428.698608, 1504.398925, 16.290353, 0.000000, 0.000020, 179.999801, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 2374, "cloth_track_t", "tshirterisyell", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 2374, "cloth_track_t", "tshirterisyell", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 2374, "cloth_track_t", "tshirterisyell", 0x00000000);
    LSAINTXT = CreateDynamicObject(2394, 1428.698608, 1505.069580, 16.290353, 0.000000, 0.000020, 179.999801, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 2374, "cloth_track_t", "trackytop1pro", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 2374, "cloth_track_t", "trackytop1pro", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 2374, "cloth_track_t", "trackytop1pro", 0x00000000);
    LSAINTXT = CreateDynamicObject(2394, 1426.097167, 1504.398925, 16.290353, 0.000000, 0.000028, 179.999801, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 2374, "cloth_track_t", "shirtshortblu", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 2374, "cloth_track_t", "shirtshortblu", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 2374, "cloth_track_t", "shirtshortblu", 0x00000000);
    LSAINTXT = CreateDynamicObject(2394, 1426.097167, 1505.069580, 16.290353, 0.000000, 0.000028, 179.999801, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 2374, "cloth_track_t", "sweatrstar", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 2374, "cloth_track_t", "sweatrstar", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 2374, "cloth_track_t", "sweatrstar", 0x00000000);
    LSAINTXT = CreateDynamicObject(2411, 1429.324707, 1502.624023, 16.930347, -0.000014, 0.000000, 89.999984, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14668, "711c", "gun_ceiling1128", 0x00000000);
    LSAINTXT = CreateDynamicObject(18762, 1429.340454, 1502.653076, 14.130344, 0.000000, 0.000014, 179.999801, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    LSAINTXT = CreateDynamicObject(2411, 1429.324707, 1500.262939, 16.930347, -0.000020, 0.000000, 90.000007, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14668, "711c", "gun_ceiling1128", 0x00000000);
    LSAINTXT = CreateDynamicObject(18762, 1429.340454, 1500.291992, 14.130344, 0.000000, 0.000020, 179.999801, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    LSAINTXT = CreateDynamicObject(19937, 1425.810302, 1496.979003, 15.570346, 0.000000, 0.000014, 179.999801, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    LSAINTXT = CreateDynamicObject(19937, 1425.810302, 1495.110839, 15.570346, 0.000000, 0.000014, 179.999801, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    LSAINTXT = CreateDynamicObject(19937, 1426.080566, 1497.039062, 15.820347, 0.000000, 0.000006, 179.999801, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    LSAINTXT = CreateDynamicObject(19937, 1426.080566, 1495.118896, 15.820347, 0.000000, 0.000006, 179.999801, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    LSAINTXT = CreateDynamicObject(19861, 1424.230468, 1496.183837, 19.440345, 0.000000, 0.000000, 89.999938, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(LSAINTXT, 0, "Z I P", 130, "Palatino Linotype", 120, 1, 0xFFFFFFFF, 0x00000000, 1);
    LSAINTXT = CreateDynamicObject(2400, 1424.307861, 1500.079345, 15.570346, 0.000000, 0.000000, 89.999938, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 2423, "cj_ff_counters", "CJ_Laminate1", 0x00000000);
    LSAINTXT = CreateDynamicObject(2400, 1424.317871, 1500.079345, 13.910346, 0.000000, 0.000000, 89.999938, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 16640, "a51", "scratchedmetal", 0x00000000);
    LSAINTXT = CreateDynamicObject(18066, 1424.292602, 1501.543701, 17.910352, 0.000000, 0.000000, -89.999969, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 2732, "cj_zip_sign", "CJ_ZIP_2", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 16640, "a51", "scratchedmetal", 0x00000000);
    LSAINTXT = CreateDynamicObject(1297, 1423.738891, 1499.775878, 19.120346, 0.000000, 179.999984, 0.000044, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 19480, "signsurf", "sign", 0x00000000);
    LSAINTXT = CreateDynamicObject(1297, 1423.738891, 1503.017578, 19.120346, 0.000000, 179.999984, 0.000044, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 19480, "signsurf", "sign", 0x00000000);
    LSAINTXT = CreateDynamicObject(2400, 1424.307861, 1489.207519, 15.570346, -0.000006, 0.000000, 89.999961, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 2423, "cj_ff_counters", "CJ_Laminate1", 0x00000000);
    LSAINTXT = CreateDynamicObject(2400, 1424.317871, 1489.207519, 13.910346, -0.000006, 0.000000, 89.999961, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 16640, "a51", "scratchedmetal", 0x00000000);
    LSAINTXT = CreateDynamicObject(18066, 1424.292602, 1490.671875, 17.910352, 0.000006, 0.000000, -89.999992, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 2732, "cj_zip_sign", "CJ_ZIP_2", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 16640, "a51", "scratchedmetal", 0x00000000);
    LSAINTXT = CreateDynamicObject(1297, 1423.738891, 1488.904052, 19.120346, 0.000000, 179.999984, 0.000091, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 19480, "signsurf", "sign", 0x00000000);
    LSAINTXT = CreateDynamicObject(1297, 1423.738891, 1492.145751, 19.120346, 0.000000, 179.999984, 0.000091, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 19480, "signsurf", "sign", 0x00000000);
    LSAINTXT = CreateDynamicObject(19373, 1427.667968, 1498.091796, 20.550350, 0.000000, 90.000000, 179.999801, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0x00000000);
    LSAINTXT = CreateDynamicObject(19373, 1427.667968, 1501.251708, 20.550350, 0.000000, 90.000000, 179.999801, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0x00000000);
    LSAINTXT = CreateDynamicObject(19373, 1427.667968, 1494.911132, 20.550350, 0.000000, 90.000000, 179.999801, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0x00000000);
    LSAINTXT = CreateDynamicObject(19373, 1427.667968, 1491.750732, 20.550350, 0.000000, 90.000000, 179.999801, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0x00000000);
    LSAINTXT = CreateDynamicObject(19860, 1424.218261, 1492.876464, 16.790348, 0.000000, 0.000000, 89.999938, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 12978, "ce_payspray", "sf_spray2", 0x00000000);
    LSAINTXT = CreateDynamicObject(2654, 1424.509765, 1498.737792, 15.740347, 0.000000, 0.000000, 179.999801, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 2732, "cj_zip_sign", "CJ_ZIP_2", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 2732, "cj_zip_sign", "CJ_ZIP_2", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 2732, "cj_zip_sign", "CJ_ZIP_2", 0x00000000);
    LSAINTXT = CreateDynamicObject(2654, 1424.509765, 1504.339355, 15.740347, 0.000000, 0.000000, 179.999801, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 2732, "cj_zip_sign", "CJ_ZIP_2", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 2732, "cj_zip_sign", "CJ_ZIP_2", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 2732, "cj_zip_sign", "CJ_ZIP_2", 0x00000000);
    LSAINTXT = CreateDynamicObject(19805, 1430.572021, 1495.991210, 19.730360, 0.000007, 0.000022, 89.999916, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 2732, "cj_zip_sign", "CJ_ZIP_2", 0x00000000);
    LSAINTXT = CreateDynamicObject(19861, 1424.230468, 1496.183837, 18.920345, 0.000000, 0.000000, 89.999938, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(LSAINTXT, 0, "CLOTHING STORE", 130, "Palatino Linotype", 30, 1, 0xFFFFFFFF, 0x00000000, 1);
    LSAINTXT = CreateDynamicObject(19377, 1424.126342, 1501.115356, 16.990385, 0.000007, 0.000007, 0.000006, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    LSAINTXT = CreateDynamicObject(18764, 1422.336181, 1496.263427, 21.850341, 0.000007, 90.000000, 89.999977, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 3922, "bistro", "mp_snow", 0x00000000);
    LSAINTXT = CreateDynamicObject(19447, 1430.256591, 1507.344726, 17.450332, 89.999992, 450.000000, -89.999961, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1430.216552, 1522.024414, 17.450332, 89.999992, 450.000000, -89.999961, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19377, 1425.661987, 1500.330810, 15.447836, 0.000065, 90.000015, -90.000205, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14865, "gf2", "mp_bobbie_wall", 0x00000000);
    LSAINTXT = CreateDynamicObject(19377, 1425.661987, 1500.330810, 21.127819, 0.000065, 90.000015, -90.000205, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 3922, "bistro", "marblekb_256128", 0x00000000);
    LSAINTXT = CreateDynamicObject(19377, 1425.661987, 1491.830200, 21.127000, 0.000065, 90.000015, -90.000205, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 3922, "bistro", "marblekb_256128", 0x00000000);
    LSAINTXT = CreateDynamicObject(19447, 1418.935913, 1483.920776, 17.750339, 89.999992, 450.000000, 0.000037, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14738, "whorebar", "AH_wdblinds", 0xFF6E493A);
    LSAINTXT = CreateDynamicObject(19538, 1358.587646, 1503.996337, 25.600378, 0.000000, 0.000000, 179.999954, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 5114, "beach_las2", "boardwalk_la", 0xFF926D45);
    LSAINTXT = CreateDynamicObject(19447, 1418.935913, 1541.601074, 17.750339, 89.999992, 450.000000, 0.000037, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14738, "whorebar", "AH_wdblinds", 0xFF6E493A);
    LSAINTXT = CreateDynamicObject(19377, 1425.502685, 1541.717041, 20.427846, 0.000037, 360.000000, -90.000114, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1430.232543, 1536.827392, 20.717849, 0.000037, 360.000000, -0.000114, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1430.232543, 1527.226562, 20.717849, 0.000037, 360.000000, -0.000114, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1430.272583, 1517.606689, 20.717849, 0.000037, 360.000000, -0.000114, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1430.282592, 1511.347167, 20.977842, 0.000037, 360.000000, -0.000114, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1430.482788, 1501.787109, 21.367851, 0.000037, 360.000000, -0.000114, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1430.482788, 1490.417114, 21.367851, 0.000037, 360.000000, -0.000114, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1430.512817, 1496.727539, 21.367851, 0.000037, 360.000000, -0.000114, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19443, 1430.175415, 1540.989624, 16.732847, 0.000000, -0.000007, 179.999801, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    LSAINTXT = CreateDynamicObject(19377, 1425.747314, 1485.666137, 17.860391, 0.000000, 0.000000, -89.999969, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19538, 1358.719116, 1521.436889, 25.590040, 0.000000, 0.000000, 179.999954, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 5114, "beach_las2", "boardwalk_la", 0xFF926D45);
    LSAINTXT = CreateDynamicObject(19377, 1417.310791, 1488.716674, 20.470336, 0.000007, -0.000022, 179.999832, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19377, 1417.310791, 1498.346801, 20.470336, 0.000007, -0.000022, 179.999832, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19377, 1417.310791, 1507.975952, 20.470336, 0.000007, -0.000037, 179.999740, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19377, 1417.310791, 1517.606079, 20.470336, 0.000007, -0.000037, 179.999740, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19377, 1417.310791, 1527.216186, 20.470336, 0.000007, -0.000029, 179.999786, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19377, 1417.310791, 1536.845947, 20.470336, 0.000007, -0.000029, 179.999786, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19552, 1464.458007, 1391.760620, 9.829986, 0.000000, 0.000000, 179.999954, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "Bow_Abpave_Gen", 0xFF555555);
    LSAINTXT = CreateDynamicObject(19552, 1464.458007, 1516.777709, 9.829986, 0.000000, 0.000000, 179.999954, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "Bow_Abpave_Gen", 0xFF555555);
    LSAINTXT = CreateDynamicObject(19552, 1464.458007, 1641.536865, 9.829986, 0.000000, 0.000000, 179.999954, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "Bow_Abpave_Gen", 0xFF555555);
    LSAINTXT = CreateDynamicObject(19552, 1339.441040, 1391.760620, 9.829986, 0.000000, -0.000007, 179.999908, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "Bow_Abpave_Gen", 0xFF555555);
    LSAINTXT = CreateDynamicObject(19552, 1339.441040, 1516.777709, 9.829986, 0.000000, -0.000007, 179.999908, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "Bow_Abpave_Gen", 0xFF555555);
    LSAINTXT = CreateDynamicObject(19552, 1339.441040, 1641.536865, 9.829986, 0.000000, -0.000007, 179.999908, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "Bow_Abpave_Gen", 0xFF555555);
    LSAINTXT = CreateDynamicObject(19447, 1437.126708, 1472.892089, 17.550336, 0.000000, 360.000000, 89.999992, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14584, "ab_abbatoir01", "ab_vent1", 0xFF6E493A);
    LSAINTXT = CreateDynamicObject(19447, 1441.895874, 1477.812744, 17.550336, 0.000000, 360.000000, 180.000000, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14584, "ab_abbatoir01", "ab_vent1", 0xFF6E493A);
    LSAINTXT = CreateDynamicObject(19447, 1441.925903, 1547.832275, 17.550336, 0.000000, 360.000000, 180.000000, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14584, "ab_abbatoir01", "ab_vent1", 0xFF6E493A);
    LSAINTXT = CreateDynamicObject(19447, 1437.243408, 1552.592773, 17.550336, 0.000000, 360.000000, 270.000000, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14584, "ab_abbatoir01", "ab_vent1", 0xFF6E493A);
    LSAINTXT = CreateDynamicObject(971, 1402.079589, 1501.785034, 17.015323, -0.000009, 0.000000, -89.999946, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 18878, "ferriswheel", "railing3", 0xFF333333);
    SetDynamicObjectMaterial(LSAINTXT, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 4, 19480, "signsurf", "sign", 0x00000000);
    LSAINTXT = CreateDynamicObject(971, 1402.069580, 1501.785034, 17.015323, -0.000009, 0.000000, -89.999946, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 14581, "ab_mafiasuitea", "barbersmir1", 0x90FFFFFF);
    SetDynamicObjectMaterial(LSAINTXT, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 4, 19480, "signsurf", "sign", 0x00000000);
    LSAINTXT = CreateDynamicObject(971, 1402.079589, 1501.785034, 24.125360, -0.000000, 0.000000, -89.999969, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 18878, "ferriswheel", "railing3", 0xFF333333);
    SetDynamicObjectMaterial(LSAINTXT, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 4, 19480, "signsurf", "sign", 0x00000000);
    LSAINTXT = CreateDynamicObject(971, 1402.069580, 1501.785034, 24.125360, -0.000000, 0.000000, -89.999969, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 14581, "ab_mafiasuitea", "barbersmir1", 0x90FFFFFF);
    SetDynamicObjectMaterial(LSAINTXT, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 4, 19480, "signsurf", "sign", 0x00000000);
    LSAINTXT = CreateDynamicObject(971, 1402.079589, 1523.382446, 17.015323, -0.000000, 0.000000, -89.999969, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 18878, "ferriswheel", "railing3", 0xFF333333);
    SetDynamicObjectMaterial(LSAINTXT, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 4, 19480, "signsurf", "sign", 0x00000000);
    LSAINTXT = CreateDynamicObject(971, 1402.069580, 1523.382446, 17.015323, -0.000000, 0.000000, -89.999969, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 14581, "ab_mafiasuitea", "barbersmir1", 0x90FFFFFF);
    SetDynamicObjectMaterial(LSAINTXT, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 4, 19480, "signsurf", "sign", 0x00000000);
    LSAINTXT = CreateDynamicObject(971, 1402.079589, 1523.382446, 24.125360, 0.000006, 0.000000, -89.999992, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 18878, "ferriswheel", "railing3", 0xFF333333);
    SetDynamicObjectMaterial(LSAINTXT, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 4, 19480, "signsurf", "sign", 0x00000000);
    LSAINTXT = CreateDynamicObject(971, 1402.069580, 1523.382446, 24.125360, 0.000006, 0.000000, -89.999992, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 14581, "ab_mafiasuitea", "barbersmir1", 0x90FFFFFF);
    SetDynamicObjectMaterial(LSAINTXT, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 4, 19480, "signsurf", "sign", 0x00000000);
    LSAINTXT = CreateDynamicObject(975, 1402.079589, 1515.837036, 15.335335, 0.000024, 270.000000, 89.999862, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 16640, "a51", "a51_glass", 0xFF333333);
    SetDynamicObjectMaterial(LSAINTXT, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 4, 19480, "signsurf", "sign", 0x00000000);
    LSAINTXT = CreateDynamicObject(975, 1402.029541, 1515.837036, 15.335335, 0.000024, 270.000000, 89.999862, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 14581, "ab_mafiasuitea", "barbersmir1", 0x90FFFFFF);
    SetDynamicObjectMaterial(LSAINTXT, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 4, 19480, "signsurf", "sign", 0x00000000);
    LSAINTXT = CreateDynamicObject(975, 1402.079589, 1512.645141, 15.335335, 0.000015, 270.000000, 89.999885, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 16640, "a51", "a51_glass", 0xFF333333);
    SetDynamicObjectMaterial(LSAINTXT, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 4, 19480, "signsurf", "sign", 0x00000000);
    LSAINTXT = CreateDynamicObject(975, 1402.029541, 1512.645141, 15.335335, 0.000015, 270.000000, 89.999885, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 14581, "ab_mafiasuitea", "barbersmir1", 0x90FFFFFF);
    SetDynamicObjectMaterial(LSAINTXT, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 4, 19480, "signsurf", "sign", 0x00000000);
    LSAINTXT = CreateDynamicObject(975, 1402.079589, 1509.462768, 15.335335, 0.000009, 270.000000, 89.999908, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 16640, "a51", "a51_glass", 0xFF333333);
    SetDynamicObjectMaterial(LSAINTXT, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 4, 19480, "signsurf", "sign", 0x00000000);
    LSAINTXT = CreateDynamicObject(975, 1402.029541, 1509.462768, 15.335334, 0.000009, 270.000000, 89.999908, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 14581, "ab_mafiasuitea", "barbersmir1", 0x90FFFFFF);
    SetDynamicObjectMaterial(LSAINTXT, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 4, 19480, "signsurf", "sign", 0x00000000);
    LSAINTXT = CreateDynamicObject(19443, 1402.046020, 1506.990356, 17.225332, 0.000000, -0.000030, 179.999664, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 16640, "a51", "scratchedmetal", 0x00000000);
    LSAINTXT = CreateDynamicObject(19443, 1402.046020, 1506.990356, 20.625322, 0.000000, -0.000030, 179.999664, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 16640, "a51", "scratchedmetal", 0x00000000);
    LSAINTXT = CreateDynamicObject(19443, 1402.046020, 1509.501098, 20.595321, 89.999992, 266.596099, -86.596099, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 16640, "a51", "scratchedmetal", 0x00000000);
    LSAINTXT = CreateDynamicObject(19443, 1402.046020, 1513.000366, 20.595321, 89.999992, 266.596099, -86.596099, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 16640, "a51", "scratchedmetal", 0x00000000);
    LSAINTXT = CreateDynamicObject(19443, 1402.046752, 1515.601440, 20.595321, 89.999992, 266.596099, -86.596099, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 16640, "a51", "scratchedmetal", 0x00000000);
    LSAINTXT = CreateDynamicObject(19443, 1402.046020, 1518.142211, 17.225332, 0.000000, -0.000024, 179.999664, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 16640, "a51", "scratchedmetal", 0x00000000);
    LSAINTXT = CreateDynamicObject(19443, 1402.046020, 1518.142211, 20.625322, 0.000000, -0.000024, 179.999664, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 16640, "a51", "scratchedmetal", 0x00000000);
    LSAINTXT = CreateDynamicObject(19443, 1402.046020, 1507.949829, 23.155340, 89.999992, 266.594421, -86.594406, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 16640, "a51", "scratchedmetal", 0x00000000);
    LSAINTXT = CreateDynamicObject(19443, 1402.046020, 1511.449096, 23.155340, 89.999992, 266.594421, -86.594406, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 16640, "a51", "scratchedmetal", 0x00000000);
    LSAINTXT = CreateDynamicObject(19443, 1402.046752, 1517.202270, 23.155340, 89.999992, 266.594421, -86.594406, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 16640, "a51", "scratchedmetal", 0x00000000);
    LSAINTXT = CreateDynamicObject(19443, 1402.046752, 1513.741333, 23.155340, 89.999992, 266.594421, -86.594406, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 16640, "a51", "scratchedmetal", 0x00000000);
    LSAINTXT = CreateDynamicObject(971, 1402.079589, 1510.497192, 25.865331, -0.000000, 0.000000, -89.999969, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 18878, "ferriswheel", "railing3", 0xFF333333);
    SetDynamicObjectMaterial(LSAINTXT, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 4, 19480, "signsurf", "sign", 0x00000000);
    LSAINTXT = CreateDynamicObject(971, 1402.069580, 1510.497192, 25.865331, -0.000000, 0.000000, -89.999969, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 14581, "ab_mafiasuitea", "barbersmir1", 0x90FFFFFF);
    SetDynamicObjectMaterial(LSAINTXT, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 4, 19480, "signsurf", "sign", 0x00000000);
    LSAINTXT = CreateDynamicObject(971, 1402.059570, 1514.564331, 25.865331, 0.000012, 0.000000, -90.000015, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 18878, "ferriswheel", "railing3", 0xFF333333);
    SetDynamicObjectMaterial(LSAINTXT, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 4, 19480, "signsurf", "sign", 0x00000000);
    LSAINTXT = CreateDynamicObject(971, 1402.049560, 1514.564331, 25.865331, 0.000012, 0.000000, -90.000015, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 14581, "ab_mafiasuitea", "barbersmir1", 0x90FFFFFF);
    SetDynamicObjectMaterial(LSAINTXT, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 4, 19480, "signsurf", "sign", 0x00000000);
    LSAINTXT = CreateDynamicObject(8324, 1401.735839, 1512.812133, 22.485349, 0.000030, 0.000000, 90.599838, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterialText(LSAINTXT, 1, "KUALANAMU INTERNATIONAL AIRPORT", 120, "Calibri", 20, 1, 0xFF333333, 0x00000000, 1);
    LSAINTXT = CreateDynamicObject(19373, 1417.176391, 1488.209350, 16.915334, 0.000015, 0.000014, 179.999847, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 16640, "a51", "scratchedmetal", 0xFF555555);
    LSAINTXT = CreateDynamicObject(19373, 1417.166625, 1487.649536, 18.385341, 0.000015, 0.000014, 179.999847, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(LSAINTXT, 0, "INFORMATION", 140, "Arial", 50, 1, 0xFFFFFFFF, 0x00000000, 1);
    LSAINTXT = CreateDynamicObject(19373, 1417.177124, 1487.019287, 16.915334, 0.000015, 0.000014, 179.999847, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 16640, "a51", "scratchedmetal", 0xFF555555);
    LSAINTXT = CreateDynamicObject(19128, 1417.085083, 1487.601074, 16.115331, 89.999992, 311.547668, -41.547794, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 2813, "gb_books01", "GB_novels05", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 2813, "gb_books01", "GB_novels05", 0x00000000);
    LSAINTXT = CreateDynamicObject(2645, 1402.168823, 1512.761352, 23.555364, 180.000030, 450.000000, 269.999816, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 1373, "traincross", "rednwhite", 0x00000000);
    LSAINTXT = CreateDynamicObject(19377, 1402.143920, 1551.835449, 20.470336, 0.000014, -0.000022, 179.999816, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19377, 1402.143920, 1542.235717, 20.470336, 0.000014, -0.000022, 179.999816, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19377, 1402.143920, 1532.615478, 20.470336, 0.000014, -0.000022, 179.999816, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19377, 1402.103881, 1492.553710, 20.470336, 0.000014, -0.000029, 179.999771, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19377, 1402.103881, 1482.953979, 20.470336, 0.000014, -0.000029, 179.999771, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19377, 1402.103881, 1473.333740, 20.470336, 0.000014, -0.000029, 179.999771, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1404.863525, 1474.168090, 20.850343, 89.999992, 450.000000, -44.999961, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14738, "whorebar", "AH_wdblinds", 0xFF6E493A);
    LSAINTXT = CreateDynamicObject(19447, 1402.409790, 1476.621826, 20.850343, 89.999992, 450.000000, -44.999961, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14738, "whorebar", "AH_wdblinds", 0xFF6E493A);
    LSAINTXT = CreateDynamicObject(19447, 1405.082885, 1551.465332, 20.850343, 89.999992, 450.000000, -134.999969, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14738, "whorebar", "AH_wdblinds", 0xFF6E493A);
    LSAINTXT = CreateDynamicObject(19447, 1402.629394, 1549.011840, 20.850343, 89.999992, 450.000000, -134.999969, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14738, "whorebar", "AH_wdblinds", 0xFF6E493A);
    LSAINTXT = CreateDynamicObject(18766, 1403.117187, 1549.285156, 13.650338, 0.000000, 0.000000, 45.000000, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(18766, 1403.351440, 1476.142333, 13.650338, 0.000000, 0.000000, -45.000000, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(18766, 1402.764160, 1476.177612, 25.980342, 0.000000, 0.000000, -45.000000, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(18766, 1403.117187, 1549.285156, 25.250326, 0.000000, 0.000000, 45.000000, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19545, 1434.467041, 1503.872070, 22.290327, 0.000000, 0.000000, 180.000000, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 5114, "beach_las2", "boardwalk_la", 0xFF926D45);
    LSAINTXT = CreateDynamicObject(19545, 1424.706176, 1504.002197, 22.280000, 0.000000, 0.000000, 180.000000, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 5114, "beach_las2", "boardwalk_la", 0xFF926D45);
    LSAINTXT = CreateDynamicObject(19545, 1434.467041, 1521.281616, 22.300329, 0.000000, -0.000014, 179.999908, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 5114, "beach_las2", "boardwalk_la", 0xFF926D45);
    LSAINTXT = CreateDynamicObject(19545, 1424.747558, 1521.411743, 22.290000, 0.000000, -0.000014, 179.999908, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 5114, "beach_las2", "boardwalk_la", 0xFF926D45);
    LSAINTXT = CreateDynamicObject(19937, 1442.218017, 1541.627563, 15.746351, 89.999992, 251.625274, -71.625221, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFFFFFFFF);
    LSAINTXT = CreateDynamicObject(19939, 1442.033325, 1542.133178, 16.736349, 0.000014, 90.000038, -179.999862, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF3C4D55);
    LSAINTXT = CreateDynamicObject(19939, 1442.233398, 1542.623535, 16.476352, 89.999992, 431.625305, -71.625221, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF3C4D55);
    LSAINTXT = CreateDynamicObject(19939, 1442.233154, 1541.653076, 16.476352, 89.999992, 431.625305, -71.625221, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF3C4D55);
    LSAINTXT = CreateDynamicObject(19477, 1441.967407, 1542.122802, 16.276357, 0.000014, 0.000039, -179.999862, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(LSAINTXT, 0, "AVIATION", 130, "Palatino Linotype", 30, 1, 0xFF333333, 0x00000000, 1);
    LSAINTXT = CreateDynamicObject(19477, 1441.967407, 1542.122802, 16.146356, 0.000014, 0.000039, -179.999862, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(LSAINTXT, 0, "SECURITY", 130, "Palatino Linotype", 30, 0, 0xFF333333, 0x00000000, 1);
    LSAINTXT = CreateDynamicObject(19937, 1442.218017, 1488.237915, 15.746351, 89.999992, 260.582305, -80.582229, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFFFFFFFF);
    LSAINTXT = CreateDynamicObject(19939, 1442.033325, 1488.743530, 16.736349, 0.000014, 90.000030, -179.999816, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF3C4D55);
    LSAINTXT = CreateDynamicObject(19939, 1442.233398, 1489.233886, 16.476352, 89.999992, 440.582336, -80.582229, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF3C4D55);
    LSAINTXT = CreateDynamicObject(19939, 1442.233154, 1488.263427, 16.476352, 89.999992, 440.582336, -80.582229, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF3C4D55);
    LSAINTXT = CreateDynamicObject(19477, 1441.967407, 1488.733154, 16.276357, 0.000014, 0.000033, -179.999816, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(LSAINTXT, 0, "AVIATION", 130, "Palatino Linotype", 30, 1, 0xFF333333, 0x00000000, 1);
    LSAINTXT = CreateDynamicObject(19477, 1441.967407, 1488.733154, 16.146356, 0.000014, 0.000033, -179.999816, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(LSAINTXT, 0, "SECURITY", 130, "Palatino Linotype", 30, 0, 0xFF333333, 0x00000000, 1);
    LSAINTXT = CreateDynamicObject(19937, 1431.119873, 1472.594238, 15.746351, 89.999992, 220.357757, -130.357604, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFFFFFFFF);
    LSAINTXT = CreateDynamicObject(19939, 1431.625488, 1472.778930, 16.736349, 0.000022, 90.000022, 90.000144, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF3C4D55);
    LSAINTXT = CreateDynamicObject(19939, 1432.115844, 1472.578857, 16.476352, 89.999992, 400.357818, -130.357604, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF3C4D55);
    LSAINTXT = CreateDynamicObject(19939, 1431.145385, 1472.579101, 16.476352, 89.999992, 400.357818, -130.357604, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF3C4D55);
    LSAINTXT = CreateDynamicObject(19477, 1431.615112, 1472.844848, 16.276357, 0.000022, 0.000024, 90.000144, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(LSAINTXT, 0, "AVIATION", 130, "Palatino Linotype", 30, 1, 0xFF333333, 0x00000000, 1);
    LSAINTXT = CreateDynamicObject(19477, 1431.615112, 1472.844848, 16.146356, 0.000022, 0.000024, 90.000144, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(LSAINTXT, 0, "SECURITY", 130, "Palatino Linotype", 30, 0, 0xFF333333, 0x00000000, 1);
    LSAINTXT = CreateDynamicObject(19937, 1426.805175, 1552.727539, 15.746351, 89.999992, 281.621887, -11.621770, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFFFFFFFF);
    LSAINTXT = CreateDynamicObject(19939, 1426.299560, 1552.542846, 16.736349, 0.000029, 90.000022, -89.999847, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF3C4D55);
    LSAINTXT = CreateDynamicObject(19939, 1425.809204, 1552.742919, 16.476352, 89.999992, 461.621948, -11.621770, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF3C4D55);
    LSAINTXT = CreateDynamicObject(19939, 1426.779663, 1552.742675, 16.476352, 89.999992, 461.621948, -11.621770, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF3C4D55);
    LSAINTXT = CreateDynamicObject(19477, 1426.309936, 1552.476928, 16.276357, 0.000029, 0.000024, -89.999847, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(LSAINTXT, 0, "AVIATION", 130, "Palatino Linotype", 30, 1, 0xFF333333, 0x00000000, 1);
    LSAINTXT = CreateDynamicObject(19477, 1426.309936, 1552.476928, 16.146356, 0.000029, 0.000024, -89.999847, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(LSAINTXT, 0, "SECURITY", 130, "Palatino Linotype", 30, 0, 0xFF333333, 0x00000000, 1);
    LSAINTXT = CreateDynamicObject(2790, 1436.000122, 1486.760498, 20.700359, 0.000000, 0.000000, 0.000000, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 19787, "samplcdtvs1", "samplcdtv1screen", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 2772, "airp_prop", "cj_AIRP_DIRECT", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 19787, "samplcdtvs1", "samplcdtv1screen", 0x00000000);
    LSAINTXT = CreateDynamicObject(2790, 1436.000122, 1542.891601, 20.700359, 0.000000, 0.000000, 180.000000, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 19787, "samplcdtvs1", "samplcdtv1screen", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 2772, "airp_prop", "cj_AIRP_DIRECT", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 19787, "samplcdtvs1", "samplcdtv1screen", 0x00000000);
    LSAINTXT = CreateDynamicObject(19443, 1434.049438, 1473.144042, 18.000328, 0.000000, 0.000000, 90.000000, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, -1, "none", "none", 0xFF926D45);
    LSAINTXT = CreateDynamicObject(19373, 1433.995849, 1473.167724, 18.990350, 0.000000, 0.000000, 90.000000, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(LSAINTXT, 0, "4", 140, "Quartz MS", 199, 1, 0xFFFFFFFF, 0x00000000, 1);
    LSAINTXT = CreateDynamicObject(19373, 1433.995849, 1473.167724, 18.110330, 0.000000, 0.000000, 90.000000, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(LSAINTXT, 0, "GATE", 140, "Quartz MS", 80, 1, 0xFFFFFFFF, 0x00000000, 1);
    LSAINTXT = CreateDynamicObject(19443, 1441.639648, 1490.489501, 18.000328, 0.000007, -0.000007, 179.999908, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, -1, "none", "none", 0xFF926D45);
    LSAINTXT = CreateDynamicObject(19373, 1441.615966, 1490.475952, 18.990350, 0.000007, -0.000007, 179.999908, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(LSAINTXT, 0, "3", 140, "Quartz MS", 199, 1, 0xFFFFFFFF, 0x00000000, 1);
    LSAINTXT = CreateDynamicObject(19373, 1441.615966, 1490.475952, 18.110330, 0.000007, -0.000007, 179.999908, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(LSAINTXT, 0, "GATE", 140, "Quartz MS", 80, 1, 0xFFFFFFFF, 0x00000000, 1);
    LSAINTXT = CreateDynamicObject(19443, 1441.639648, 1534.949584, 18.000328, 0.000007, -0.000014, 179.999862, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, -1, "none", "none", 0xFF926D45);
    LSAINTXT = CreateDynamicObject(19373, 1441.615966, 1534.895996, 18.990350, 0.000007, -0.000014, 179.999862, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(LSAINTXT, 0, "2", 140, "Quartz MS", 199, 1, 0xFFFFFFFF, 0x00000000, 1);
    LSAINTXT = CreateDynamicObject(19373, 1441.615966, 1534.895996, 18.110330, 0.000007, -0.000014, 179.999862, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(LSAINTXT, 0, "GATE", 140, "Quartz MS", 80, 1, 0xFFFFFFFF, 0x00000000, 1);
    LSAINTXT = CreateDynamicObject(19443, 1433.745117, 1552.441284, 18.000328, 0.000000, -0.000022, -90.000099, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, -1, "none", "none", 0xFF926D45);
    LSAINTXT = CreateDynamicObject(19373, 1433.768676, 1552.417602, 18.990350, 0.000000, -0.000022, -90.000099, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(LSAINTXT, 0, "1", 140, "Quartz MS", 199, 1, 0xFFFFFFFF, 0x00000000, 1);
    LSAINTXT = CreateDynamicObject(19373, 1433.768676, 1552.417602, 18.110330, 0.000000, -0.000022, -90.000099, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(LSAINTXT, 0, "GATE", 140, "Quartz MS", 80, 1, 0xFFFFFFFF, 0x00000000, 1);
    LSAINTXT = CreateDynamicObject(640, 1404.344970, 1475.231323, 16.480339, 0.000000, 0.000000, 45.000000, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 801, "gta_proc_ferns", "veg_bush2", 0x00000000);
    LSAINTXT = CreateDynamicObject(640, 1404.359008, 1550.416015, 16.480339, 0.000000, 0.000000, -45.000000, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 801, "gta_proc_ferns", "veg_bush2", 0x00000000);
    LSAINTXT = CreateDynamicObject(19447, 1417.265747, 1547.903808, 23.950338, 180.000000, 540.000000, 0.000037, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1417.245727, 1546.283569, 23.950338, 180.000000, 540.000000, 0.000037, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1417.245727, 1479.212890, 23.950338, 180.000000, 540.000000, 0.000037, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19447, 1417.265747, 1477.721923, 23.950338, 180.000000, 540.000000, 0.000037, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19377, 1425.356933, 1485.646118, 17.860391, 0.000000, 0.000000, -89.999969, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14853, "gen_pol_vegas", "office_wallnu1", 0xFFB5A68A);
    LSAINTXT = CreateDynamicObject(19377, 1429.018188, 1468.192138, 22.340335, 0.000000, 90.000000, 0.000000, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 5114, "beach_las2", "boardwalk_la", 0xFF926D45);
    LSAINTXT = CreateDynamicObject(19377, 1429.018188, 1458.953491, 22.340335, 0.000000, 90.000000, 0.000000, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 5114, "beach_las2", "boardwalk_la", 0xFF926D45);
    LSAINTXT = CreateDynamicObject(19377, 1446.828369, 1486.193969, 22.340335, 0.000007, 90.000007, 89.999946, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 5114, "beach_las2", "boardwalk_la", 0xFF926D45);
    LSAINTXT = CreateDynamicObject(19377, 1456.067016, 1486.193969, 22.340335, 0.000007, 90.000007, 89.999946, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 5114, "beach_las2", "boardwalk_la", 0xFF926D45);
    LSAINTXT = CreateDynamicObject(19377, 1446.828369, 1539.611694, 22.340335, 0.000014, 90.000007, 89.999923, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 5114, "beach_las2", "boardwalk_la", 0xFF926D45);
    LSAINTXT = CreateDynamicObject(19377, 1456.067016, 1539.611694, 22.340335, 0.000014, 90.000007, 89.999923, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 5114, "beach_las2", "boardwalk_la", 0xFF926D45);
    LSAINTXT = CreateDynamicObject(19377, 1428.772094, 1557.529174, 22.340335, 0.000022, 90.000000, 179.999832, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 5114, "beach_las2", "boardwalk_la", 0xFF926D45);
    LSAINTXT = CreateDynamicObject(19377, 1428.772094, 1566.767822, 22.340335, 0.000022, 90.000000, 179.999832, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 5114, "beach_las2", "boardwalk_la", 0xFF926D45);
    LSAINTXT = CreateDynamicObject(19128, 1417.085083, 1537.942138, 16.115331, 89.999992, 332.063568, -62.063701, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 2813, "gb_books01", "GB_novels05", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 2813, "gb_books01", "GB_novels05", 0x00000000);
    LSAINTXT = CreateDynamicObject(8330, 1417.545410, 1525.188842, 20.130346, 0.000000, 0.000000, -90.899978, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterialText(LSAINTXT, 0, "KUALANAMU", 120, "Arial", 60, 1, 0xFFFFFFFF, 0x00000000, 1);
    LSAINTXT = CreateDynamicObject(8330, 1417.542114, 1511.587280, 20.130346, 0.000000, 0.000000, -90.899978, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterialText(LSAINTXT, 0, "INTERNATIONAL", 120, "Arial", 60, 1, 0xFFFFFFFF, 0x00000000, 1);
    LSAINTXT = CreateDynamicObject(8330, 1417.563476, 1499.625732, 20.130346, 0.000000, 0.000000, -90.899978, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterialText(LSAINTXT, 0, "AIRPORT", 120, "Arial", 60, 1, 0xFFFFFFFF, 0x00000000, 1);
    LSAINTXT = CreateDynamicObject(19786, 1402.179931, 1481.077270, 17.880352, 0.000000, 0.000000, 90.000000, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 2772, "airp_prop", "CJ_AIRP_S_1", 0x00000000);
    LSAINTXT = CreateDynamicObject(19786, 1402.179931, 1483.748535, 17.880352, 0.000000, 0.000000, 90.000000, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 2772, "airp_prop", "CJ_AIRP_S_2", 0x00000000);
    LSAINTXT = CreateDynamicObject(19786, 1402.179931, 1486.480468, 17.880352, 0.000000, 0.000000, 90.000000, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 2772, "airp_prop", "CJ_AIRP_S_2", 0x00000000);
    LSAINTXT = CreateDynamicObject(19786, 1402.179931, 1489.299316, 17.880352, 0.000000, 0.000000, 90.000000, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 2772, "airp_prop", "CJ_AIRP_S_1", 0x00000000);
    LSAINTXT = CreateDynamicObject(19786, 1402.179931, 1533.290039, 17.880352, 0.000007, 0.000000, 89.999977, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 2772, "airp_prop", "CJ_AIRP_S_1", 0x00000000);
    LSAINTXT = CreateDynamicObject(19786, 1402.179931, 1535.961303, 17.880352, 0.000007, 0.000000, 89.999977, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 2772, "airp_prop", "CJ_AIRP_S_2", 0x00000000);
    LSAINTXT = CreateDynamicObject(19786, 1402.179931, 1538.693237, 17.880352, 0.000007, 0.000000, 89.999977, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 2772, "airp_prop", "CJ_AIRP_S_2", 0x00000000);
    LSAINTXT = CreateDynamicObject(19786, 1402.179931, 1541.512084, 17.880352, 0.000007, 0.000000, 89.999977, 145, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 2772, "airp_prop", "CJ_AIRP_S_1", 0x00000000);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(1892, 1420.542602, 1478.014404, 15.515336, -0.000037, 0.000000, -89.999855, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1892, 1420.542602, 1481.515380, 15.515336, -0.000037, 0.000000, -89.999855, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1892, 1420.542602, 1484.806640, 15.515336, -0.000037, 0.000000, -89.999855, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1892, 1420.542602, 1541.835449, 15.515336, -0.000046, 0.000000, -89.999832, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1892, 1420.542602, 1545.336425, 15.515336, -0.000046, 0.000000, -89.999832, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1892, 1420.542602, 1548.627685, 15.515336, -0.000046, 0.000000, -89.999832, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1430.126098, 1526.746582, 17.417842, 0.000012, -0.000066, -0.000593, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1430.126098, 1525.486083, 17.457845, -0.000009, 0.000066, 179.999816, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1430.156127, 1525.796386, 18.337850, 89.999992, -64.489242, -115.510581, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1430.156127, 1525.796386, 17.957841, 89.999992, -64.489242, -115.510581, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1430.156127, 1525.796386, 16.217834, 89.999992, -64.489242, -115.510581, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1430.126098, 1532.687988, 17.447845, 0.000019, -0.000059, -0.000562, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1430.126098, 1531.427490, 17.377847, -0.000019, 0.000059, -179.999801, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1430.156127, 1531.737792, 18.337850, 89.999992, -64.507492, -115.492347, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1430.156127, 1531.737792, 17.957841, 89.999992, -64.507492, -115.492347, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1430.156127, 1531.737792, 16.207836, 89.999992, -64.507492, -115.492347, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1430.156127, 1528.687011, 18.127847, -89.999992, 244.489700, 64.489501, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1430.146118, 1529.567626, 18.127847, -89.999992, 64.489471, 64.489288, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1430.156127, 1528.687011, 18.317850, -89.999992, 244.487228, 64.487007, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1430.146118, 1529.567626, 18.317850, -89.999992, 64.487091, 64.486846, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1430.139770, 1527.576416, 15.527914, 0.000019, 180.197845, 0.198347, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1430.147583, 1527.576416, 17.717899, 0.000019, 180.197845, 0.198347, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1430.137817, 1530.658203, 16.467887, 0.000000, 180.197845, -179.801925, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1430.135375, 1530.658203, 17.077886, 0.000000, 180.197845, -179.801925, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1987, 1426.832397, 1541.030029, 15.453778, 0.000000, -0.000059, -0.000472, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1430.126098, 1535.817871, 17.447845, 0.000019, -0.000072, -0.000654, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1994, 1427.651000, 1525.893554, 15.543781, 0.000004, -0.000059, -0.000516, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1993, 1428.621215, 1525.893554, 15.543781, 0.000004, -0.000059, -0.000516, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1995, 1429.611694, 1525.893554, 15.543781, 0.000004, -0.000059, -0.000516, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1885, 1429.723999, 1527.144042, 15.543783, 0.000050, 0.000000, -90.000129, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1430.126098, 1534.557373, 17.377847, -0.000019, 0.000072, -179.999801, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1430.156127, 1534.867675, 18.337850, 89.999992, -64.480064, -115.519706, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1987, 1425.951782, 1541.030029, 15.453778, 0.000000, -0.000067, -0.000516, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1987, 1425.081420, 1541.030029, 15.453778, 0.000000, -0.000067, -0.000516, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1430.156127, 1534.867675, 17.957841, 89.999992, -64.480064, -115.519706, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1430.156127, 1534.867675, 16.207836, 89.999992, -64.480064, -115.519706, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1430.126098, 1537.208251, 17.447845, 0.000019, -0.000081, -0.000699, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1430.156127, 1536.258056, 18.337850, 89.999992, -64.475402, -115.524360, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1430.156127, 1536.258056, 17.957841, 89.999992, -64.475402, -115.524360, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1430.156127, 1536.258056, 16.207836, 89.999992, -64.475402, -115.524360, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1430.126098, 1538.639648, 17.447845, 0.000019, -0.000088, -0.000745, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1430.156127, 1537.689453, 18.337850, 89.999992, -64.472946, -115.526786, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1430.156127, 1537.689453, 17.957841, 89.999992, -64.472946, -115.526786, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1430.156127, 1537.689453, 16.207836, 89.999992, -64.472946, -115.526786, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2362, 1425.847045, 1525.832763, 16.493785, 0.000000, 0.000059, 179.999801, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1987, 1427.682128, 1541.030029, 15.453778, 0.000000, -0.000059, -0.000472, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1987, 1428.542358, 1541.030029, 15.453778, 0.000000, -0.000059, -0.000472, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19860, 1424.057617, 1527.175781, 16.772819, 0.000007, 0.000000, 89.999916, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18874, 1426.335449, 1517.625732, 16.593746, 89.999992, 180.000030, -89.999961, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18874, 1426.335449, 1517.175659, 16.593746, 89.999992, 180.000030, -89.999961, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18874, 1426.335449, 1516.825927, 16.593746, 89.999992, 180.000030, -89.999961, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18874, 1426.030029, 1516.825927, 16.593746, 89.999992, 356.598236, -86.598289, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18874, 1426.030029, 1517.276000, 16.593746, 89.999992, 356.598236, -86.598289, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18874, 1426.030029, 1517.625732, 16.593746, 89.999992, 356.598236, -86.598289, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18874, 1426.335449, 1512.235595, 16.593746, 89.999992, 180.000045, -89.999961, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18874, 1426.335449, 1511.785522, 16.593746, 89.999992, 180.000045, -89.999961, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18874, 1426.335449, 1511.435791, 16.593746, 89.999992, 180.000045, -89.999961, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18874, 1426.030029, 1511.435791, 16.593746, 89.999992, 358.297607, -88.297630, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18874, 1426.030029, 1511.885864, 16.593746, 89.999992, 358.297607, -88.297630, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18874, 1426.030029, 1512.235595, 16.593746, 89.999992, 358.297607, -88.297630, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19893, 1425.064208, 1519.216308, 16.523750, 0.000000, 0.000020, 0.000000, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19893, 1426.044189, 1519.216308, 16.523750, 0.000000, 0.000020, 0.000000, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19894, 1426.964355, 1519.216308, 16.453748, 0.000000, 0.000020, 0.000000, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19894, 1427.894409, 1519.216308, 16.453748, 0.000000, 0.000020, 0.000000, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19787, 1429.828369, 1511.373046, 17.143747, -0.000020, 0.000000, -89.999931, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19787, 1429.828369, 1518.093017, 17.143747, -0.000020, 0.000000, -89.999931, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(638, 1430.650268, 1510.708618, 15.929691, 0.000000, 0.000020, 0.000000, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(638, 1430.650268, 1518.748657, 15.929691, 0.000000, 0.000020, 0.000000, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1514, 1422.996704, 1514.778686, 16.743747, 0.000020, 0.000000, 89.999931, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1514, 1422.996704, 1511.978149, 16.743747, 0.000020, 0.000000, 89.999931, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19787, 1420.677856, 1514.633178, 17.493747, 0.000020, 0.000000, 89.999931, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(948, 1423.031250, 1515.578979, 15.533746, 0.000000, 0.000020, 0.000000, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19295, 1426.257812, 1511.860717, 16.733758, 0.000000, 0.000020, 0.000000, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19295, 1426.257812, 1517.001464, 16.733758, 0.000000, 0.000020, 0.000000, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1893, 1426.216308, 1511.225463, 18.923751, 0.000020, 0.000000, 89.999931, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1893, 1426.216308, 1516.856689, 18.923751, 0.000020, 0.000000, 89.999931, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2371, 1428.015869, 1487.531005, 15.570346, 0.000000, 0.000000, -89.999969, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2371, 1425.414428, 1487.531005, 15.570346, 0.000006, 0.000000, -89.999992, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2371, 1428.015869, 1504.761962, 15.570346, 0.000020, 0.000000, -90.000038, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2371, 1425.414428, 1504.761962, 15.570346, 0.000028, 0.000000, -90.000061, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2374, 1424.325195, 1501.968261, 16.800344, -0.000006, 0.000000, 89.999961, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2374, 1424.325195, 1501.228271, 16.800344, -0.000006, 0.000000, 89.999961, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2374, 1424.325195, 1502.709228, 16.800344, -0.000006, 0.000000, 89.999961, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2374, 1424.325195, 1500.438476, 16.800344, -0.000006, 0.000000, 89.999961, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2374, 1424.325195, 1491.096435, 16.800344, -0.000014, 0.000000, 89.999984, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2374, 1424.325195, 1490.356445, 16.800344, -0.000014, 0.000000, 89.999984, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2374, 1424.325195, 1491.837402, 16.800344, -0.000014, 0.000000, 89.999984, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2374, 1424.325195, 1489.566650, 16.800344, -0.000014, 0.000000, 89.999984, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2072, 1427.293945, 1496.688964, 20.320362, 0.000000, 90.000000, 89.999938, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2072, 1427.293945, 1492.718994, 20.320362, 0.000000, 90.000000, 89.999938, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2072, 1427.293945, 1500.489746, 20.320362, 0.000000, 90.000000, 89.999938, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2412, 1429.668823, 1497.918945, 15.570346, 0.000000, 0.000000, -89.999969, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2412, 1429.668823, 1493.557373, 15.570346, 0.000000, 0.000000, -89.999969, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1892, 1406.111572, 1509.644653, 15.515336, -0.000030, 0.000000, -89.999877, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1892, 1406.111572, 1513.145629, 15.515336, -0.000030, 0.000000, -89.999877, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1892, 1406.111572, 1516.436889, 15.515336, -0.000030, 0.000000, -89.999877, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3439, 1403.328979, 1507.158813, 16.755350, 0.000000, 0.000000, 179.999664, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3439, 1403.328979, 1517.979614, 16.755350, 0.000000, -0.000000, 179.999664, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1572, 1405.640502, 1522.824584, 16.065317, 0.000039, -0.000000, 139.899948, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 1443.045654, 1539.574829, 16.030338, 0.000000, 0.000000, 90.000000, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 1446.125488, 1539.574829, 16.030338, 0.000000, 0.000000, 90.000000, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 1443.045654, 1486.185180, 16.030338, 0.000007, 0.000000, 89.999977, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 1446.125488, 1486.185180, 16.030338, 0.000007, 0.000000, 89.999977, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 1429.067138, 1471.766601, 16.030338, 0.000014, 0.000007, -0.000014, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 1429.067138, 1468.686767, 16.030338, 0.000014, 0.000007, -0.000014, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 1428.857910, 1553.555175, 16.030338, 0.000014, 0.000014, 179.999877, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 1428.857910, 1556.635009, 16.030338, 0.000014, 0.000014, 179.999877, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 1437.089843, 1530.197143, 15.903493, -0.000065, -0.000003, 90.000160, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 1437.089843, 1525.586914, 15.903493, -0.000065, -0.000003, 90.000160, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 1436.160156, 1525.587036, 15.903493, -0.000063, -0.000003, -89.999809, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 1436.160156, 1530.197265, 15.903493, -0.000063, -0.000003, -89.999809, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 1437.089843, 1516.876831, 15.903493, -0.000057, -0.000003, 90.000137, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 1436.160156, 1516.876953, 15.903493, -0.000072, -0.000003, -89.999786, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 1437.089843, 1508.146606, 15.903493, -0.000048, -0.000003, 90.000114, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 1436.160156, 1508.146728, 15.903493, -0.000079, -0.000003, -89.999763, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 1437.089843, 1500.486816, 15.903493, -0.000048, -0.000003, 90.000114, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 1437.089843, 1495.876586, 15.903493, -0.000048, -0.000003, 90.000114, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 1436.160156, 1495.876708, 15.903493, -0.000079, -0.000003, -89.999763, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 1436.160156, 1500.486938, 15.903493, -0.000079, -0.000003, -89.999763, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19443, 1433.989379, 1473.053955, 17.020332, 0.000000, 0.000000, 90.000000, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19443, 1441.729736, 1490.469482, 17.020332, 0.000007, -0.000007, 179.999908, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19443, 1441.729736, 1534.889526, 17.020332, 0.000007, -0.000014, 179.999862, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19443, 1433.775146, 1552.531372, 17.020332, 0.000000, -0.000022, -90.000099, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1572, 1415.793579, 1532.460083, 16.065317, 0.000039, -0.000000, 139.899948, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1572, 1416.338012, 1486.025024, 16.065340, 0.000039, -0.000000, -163.700012, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 1407.358642, 1489.276733, 15.903492, -0.000040, -0.000003, 90.000091, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 1407.358642, 1484.666503, 15.903492, -0.000040, -0.000003, 90.000091, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 1406.428955, 1484.666625, 15.903492, -0.000087, -0.000001, -89.999740, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 1406.428955, 1489.276855, 15.903492, -0.000087, -0.000001, -89.999740, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 1407.358642, 1541.489501, 15.903492, -0.000033, -0.000003, 90.000068, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 1407.358642, 1536.879272, 15.903492, -0.000033, -0.000003, 90.000068, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 1406.428955, 1536.879394, 15.903492, -0.000095, -0.000001, -89.999717, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 1406.428955, 1541.489624, 15.903492, -0.000095, -0.000001, -89.999717, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3439, 1403.328979, 1495.858764, 16.755350, 0.000000, 0.000000, 179.999664, 145, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3439, 1403.328979, 1529.889038, 16.755350, 0.000000, 0.000000, 179.999664, 145, 0, -1, 200.00, 200.00);

    //pelabuhan
    LSAINTXT = CreateDynamicObject(18981, 2750.929443, -2421.191650, 27.697509, 0.000000, 90.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 16639, "a51_labs", "dam_terazzo", 0x00000000);
    LSAINTXT = CreateDynamicObject(18981, 2750.929443, -2396.221191, 27.697509, 0.000000, 90.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 16639, "a51_labs", "dam_terazzo", 0x00000000);
    LSAINTXT = CreateDynamicObject(18981, 2769.414306, -2408.221679, 22.824951, 0.000029, 180.000015, 89.999908, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(18981, 2750.931152, -2383.230468, 23.702758, 0.000022, 180.000015, 89.999931, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 8395, "pyramid", "luxorwindow01_128", 0x00000000);
    LSAINTXT = CreateDynamicObject(18981, 2751.892089, -2434.199951, 35.695434, 0.000022, 180.000015, 89.999931, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    LSAINTXT = CreateDynamicObject(18981, 2775.909179, -2421.191650, 27.697509, 0.000000, 90.000022, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 16639, "a51_labs", "dam_terazzo", 0x00000000);
    LSAINTXT = CreateDynamicObject(18981, 2763.927978, -2396.221191, 22.824951, 0.000000, 180.000015, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(18981, 2776.890380, -2434.199951, 35.695434, 0.000022, 180.000015, 89.999931, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(18981, 2788.891601, -2421.236572, 23.702758, 0.000000, 180.000015, 179.999862, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 8395, "pyramid", "luxorwindow01_128", 0x00000000);
    LSAINTXT = CreateDynamicObject(18981, 2737.942382, -2395.231689, 35.695434, 0.000000, 180.000015, 179.999908, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(18981, 2737.942382, -2420.212402, 35.695434, 0.000000, 180.000015, 179.999908, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    LSAINTXT = CreateDynamicObject(18981, 2744.727050, -2427.034912, 35.695434, 0.000018, 180.000015, 44.999958, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(18980, 2738.936035, -2383.239990, 24.690917, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(18980, 2762.936279, -2383.239990, 24.690917, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(18980, 2788.886474, -2409.228027, 24.690917, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(18980, 2788.886474, -2433.202636, 24.690917, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(18980, 2788.886474, -2421.241943, 28.172363, 0.000007, 90.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(18980, 2750.936035, -2383.239990, 28.173095, 0.000000, 90.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(18980, 2750.936035, -2383.239990, 36.684448, 0.000000, 90.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(18981, 2775.949218, -2396.144531, 35.718383, 0.000000, 90.000022, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 16639, "a51_labs", "dam_terazzo", 0x00000000);
    LSAINTXT = CreateDynamicObject(18980, 2788.886474, -2421.221923, 36.693969, 0.000007, 90.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(18981, 2765.949218, -2396.144531, 35.718383, 0.000000, 90.000022, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 16639, "a51_labs", "dam_terazzo", 0x00000000);
    LSAINTXT = CreateDynamicObject(18981, 2775.949218, -2406.144531, 35.718383, 0.000000, 90.000022, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 16639, "a51_labs", "dam_terazzo", 0x00000000);
    LSAINTXT = CreateDynamicObject(18981, 2765.949218, -2406.144531, 35.718383, 0.000000, 90.000022, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 16639, "a51_labs", "dam_terazzo", 0x00000000);
    LSAINTXT = CreateDynamicObject(18981, 2775.934082, -2383.230468, 48.667358, 0.000029, 180.000015, 89.999908, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 1975, "texttest", "kb_blue", 0x00000000);
    LSAINTXT = CreateDynamicObject(18981, 2788.891601, -2421.226562, 49.677856, 0.000000, 180.000015, 179.999816, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 9932, "nitelites", "sfnitewindows", 0x00000000);
    LSAINTXT = CreateDynamicObject(18981, 2750.981201, -2383.230468, 49.677856, 0.000029, 180.000015, 89.999908, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 9932, "nitelites", "sfnitewindows", 0x00000000);
    LSAINTXT = CreateDynamicObject(18981, 2788.928222, -2396.221435, 48.667358, 0.000022, 180.000015, 179.999893, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 1975, "texttest", "kb_blue", 0x00000000);
    LSAINTXT = CreateDynamicObject(18980, 2788.886474, -2396.229980, 36.693969, 0.000007, 90.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(18980, 2775.907470, -2383.239990, 36.684448, 0.000000, 90.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(18981, 2765.949218, -2396.144531, 34.717407, 0.000000, 90.000030, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14385, "trailerkb", "tr_wood1", 0x00000000);
    LSAINTXT = CreateDynamicObject(18981, 2775.949218, -2406.144531, 34.717407, 0.000000, 90.000030, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14385, "trailerkb", "tr_wood1", 0x00000000);
    LSAINTXT = CreateDynamicObject(18981, 2765.949218, -2406.144531, 34.717407, 0.000000, 90.000030, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14385, "trailerkb", "tr_wood1", 0x00000000);
    LSAINTXT = CreateDynamicObject(18981, 2750.929443, -2421.191650, 48.580078, 0.000000, 90.000015, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14385, "trailerkb", "tr_wood1", 0x00000000);
    LSAINTXT = CreateDynamicObject(18981, 2750.929443, -2396.221191, 48.580078, 0.000000, 90.000015, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14385, "trailerkb", "tr_wood1", 0x00000000);
    LSAINTXT = CreateDynamicObject(18981, 2775.909179, -2421.191650, 48.580078, 0.000000, 90.000030, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14385, "trailerkb", "tr_wood1", 0x00000000);
    LSAINTXT = CreateDynamicObject(18981, 2775.893066, -2396.221191, 48.580078, 0.000000, 90.000015, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14385, "trailerkb", "tr_wood1", 0x00000000);
    LSAINTXT = CreateDynamicObject(18980, 2788.886474, -2408.243652, 49.650634, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(18980, 2763.943115, -2383.242675, 49.650634, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(18980, 2775.937500, -2383.239990, 47.695434, 0.000000, 90.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(18980, 2788.886474, -2396.229980, 47.695556, 0.000007, 90.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(18980, 2752.947509, -2396.230468, 35.714233, 0.000014, 90.000000, 89.999954, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(18980, 2752.947509, -2406.175048, 35.714233, 0.000014, 90.000000, 89.999954, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(18980, 2764.947753, -2419.135986, 35.714233, 0.000000, 89.999984, 179.999908, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(18980, 2775.961669, -2419.135986, 35.714233, 0.000000, 89.999984, 179.999908, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(18980, 2752.947509, -2396.230468, 34.713256, 0.000022, 90.000000, 89.999931, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(18980, 2752.947509, -2406.175048, 34.713256, 0.000022, 90.000000, 89.999931, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(18980, 2764.947753, -2419.135986, 34.713256, 0.000000, 89.999977, 179.999862, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(18980, 2775.961669, -2419.135986, 34.713256, 0.000000, 89.999977, 179.999862, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(18981, 2775.963378, -2396.144531, 34.717407, 0.000000, 90.000030, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14385, "trailerkb", "tr_wood1", 0x00000000);
    LSAINTXT = CreateDynamicObject(18981, 2775.949218, -2396.194580, 27.698120, 0.000000, 90.000022, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 16639, "a51_labs", "dam_terazzo", 0x00000000);
    LSAINTXT = CreateDynamicObject(18981, 2812.922851, -2396.244628, 23.702758, 0.000000, 180.000015, 179.999862, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(18766, 2790.937988, -2394.739013, 27.690185, 89.999992, 180.000000, -89.999961, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 16639, "a51_labs", "dam_terazzo", 0x00000000);
    LSAINTXT = CreateDynamicObject(18981, 2776.929199, -2383.223388, 23.702758, -0.000007, 180.000015, -90.000068, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(18980, 2788.886474, -2384.228271, 24.690917, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(18766, 2759.423339, -2408.617187, 30.075683, 0.000000, 90.000015, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 1975, "texttest", "kb_blue", 0x00000000);
    LSAINTXT = CreateDynamicObject(18980, 2762.407714, -2408.618408, 20.602661, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 1975, "texttest", "kb_blue", 0x00000000);
    LSAINTXT = CreateDynamicObject(18980, 2763.398681, -2408.618408, 19.222656, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 1975, "texttest", "kb_blue", 0x00000000);
    LSAINTXT = CreateDynamicObject(18980, 2764.389648, -2408.618408, 18.021606, 0.000007, 0.000007, 89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 1975, "texttest", "kb_blue", 0x00000000);
    LSAINTXT = CreateDynamicObject(19482, 2768.832519, -2408.725341, 31.548217, -0.000022, 0.000000, -89.999931, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(LSAINTXT, 0, "PELA", 130, "Arial", 199, 1, 0xFF000000, 0x00000000, 1);
    LSAINTXT = CreateDynamicObject(19482, 2774.122314, -2408.725341, 31.548217, -0.000022, 0.000000, -89.999931, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(LSAINTXT, 0, "BUHA", 130, "Arial", 199, 1, 0xFF000000, 0x00000000, 1);
    LSAINTXT = CreateDynamicObject(19482, 2777.572265, -2408.725341, 31.548217, -0.000022, 0.000000, -89.999931, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(LSAINTXT, 0, "N", 130, "Arial", 199, 1, 0xFF000000, 0x00000000, 1);
    LSAINTXT = CreateDynamicObject(19482, 2772.379882, -2408.725341, 30.127319, -0.000022, 0.000000, -89.999931, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(LSAINTXT, 0, "BELAWAN", 130, "Arial", 120, 1, 0xFF000000, 0x00000000, 1);
    LSAINTXT = CreateDynamicObject(19482, 2768.970703, -2408.735351, 30.136352, -0.000037, 0.000000, -89.999885, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(LSAINTXT, 0, "o", 130, "Webdings", 120, 1, 0xFF000000, 0x00000000, 1);
    LSAINTXT = CreateDynamicObject(19482, 2775.791259, -2408.735351, 30.136352, -0.000022, -0.000000, 90.000068, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(LSAINTXT, 0, "o", 130, "Webdings", 120, 1, 0xFF000000, 0x00000000, 1);
    LSAINTXT = CreateDynamicObject(19482, 2759.980224, -2409.195800, 31.466430, -0.000045, 0.000000, -89.999862, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(LSAINTXT, 0, "o", 130, "Webdings", 199, 1, 0xFF000000, 0x00000000, 1);
    LSAINTXT = CreateDynamicObject(19482, 2759.980224, -2409.185791, 31.006591, -0.000045, 0.000000, -89.999862, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(LSAINTXT, 0, "m", 130, "Wingdings", 199, 1, 0xFFFFFFFF, 0x00000000, 1);
    LSAINTXT = CreateDynamicObject(10009, 2748.239501, -2388.650390, 32.996093, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 1975, "texttest", "kb_blue", 0x00000000);
    LSAINTXT = CreateDynamicObject(10009, 2783.490478, -2423.933105, 32.996093, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 1975, "texttest", "kb_blue", 0x00000000);
    LSAINTXT = CreateDynamicObject(970, 2780.130371, -2418.649169, 36.727172, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 6060, "shops2_law", "newall8-1blue", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(970, 2775.959228, -2418.649169, 36.727172, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 6060, "shops2_law", "newall8-1blue", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(970, 2771.789306, -2418.649169, 36.727172, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 6060, "shops2_law", "newall8-1blue", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(970, 2767.619628, -2418.649169, 36.727172, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 6060, "shops2_law", "newall8-1blue", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(970, 2763.459960, -2418.649169, 36.727172, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 6060, "shops2_law", "newall8-1blue", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(970, 2759.289062, -2418.649169, 36.727172, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 6060, "shops2_law", "newall8-1blue", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(970, 2755.117675, -2418.649169, 36.727172, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 6060, "shops2_law", "newall8-1blue", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(970, 2753.469726, -2392.011230, 36.727172, 0.000022, 0.000007, 89.999900, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 6060, "shops2_law", "newall8-1blue", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(970, 2753.469726, -2396.182373, 36.727172, 0.000022, 0.000007, 89.999900, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 6060, "shops2_law", "newall8-1blue", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(970, 2753.469726, -2400.352294, 36.727172, 0.000022, 0.000007, 89.999900, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 6060, "shops2_law", "newall8-1blue", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(970, 2753.469726, -2404.521972, 36.727172, 0.000022, 0.000007, 89.999900, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 6060, "shops2_law", "newall8-1blue", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(970, 2753.469726, -2408.681640, 36.727172, 0.000022, 0.000007, 89.999900, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 6060, "shops2_law", "newall8-1blue", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(970, 2753.469726, -2412.852539, 36.727172, 0.000022, 0.000007, 89.999900, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 6060, "shops2_law", "newall8-1blue", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(970, 2753.469726, -2417.023925, 36.727172, 0.000022, 0.000007, 89.999900, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 6060, "shops2_law", "newall8-1blue", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(18980, 2748.957031, -2422.694091, 45.816406, 25.000000, -24.999984, -0.000003, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14623, "mafcasmain", "ab_tileStar", 0x00000000);
    LSAINTXT = CreateDynamicObject(18980, 2747.801025, -2414.139648, 47.148071, 0.000005, -24.999958, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14623, "mafcasmain", "ab_tileStar", 0x00000000);
    LSAINTXT = CreateDynamicObject(18980, 2747.801025, -2412.639648, 47.148071, 0.000005, -24.999958, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14623, "mafcasmain", "ab_tileStar", 0x00000000);
    LSAINTXT = CreateDynamicObject(18980, 2767.945800, -2424.384765, 47.308227, 24.999998, 0.000044, -0.000014, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14623, "mafcasmain", "ab_tileStar", 0x00000000);
    LSAINTXT = CreateDynamicObject(18980, 2769.445800, -2424.384765, 47.308227, 24.999998, 0.000044, -0.000014, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14623, "mafcasmain", "ab_tileStar", 0x00000000);
    LSAINTXT = CreateDynamicObject(18980, 2777.945800, -2424.384765, 47.308227, 24.999998, 0.000044, -0.000014, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14623, "mafcasmain", "ab_tileStar", 0x00000000);
    LSAINTXT = CreateDynamicObject(18980, 2779.445800, -2424.384765, 47.308227, 24.999998, 0.000044, -0.000014, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14623, "mafcasmain", "ab_tileStar", 0x00000000);
    LSAINTXT = CreateDynamicObject(18980, 2747.801025, -2394.139648, 47.148071, 0.000005, -24.999958, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14623, "mafcasmain", "ab_tileStar", 0x00000000);
    LSAINTXT = CreateDynamicObject(18980, 2747.801025, -2392.639648, 47.148071, 0.000005, -24.999958, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14623, "mafcasmain", "ab_tileStar", 0x00000000);
    LSAINTXT = CreateDynamicObject(18980, 2757.945800, -2424.384765, 47.308227, 24.999998, 0.000044, -0.000014, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14623, "mafcasmain", "ab_tileStar", 0x00000000);
    LSAINTXT = CreateDynamicObject(18980, 2759.445800, -2424.384765, 47.308227, 24.999998, 0.000044, -0.000014, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14623, "mafcasmain", "ab_tileStar", 0x00000000);
    LSAINTXT = CreateDynamicObject(18980, 2747.801025, -2404.139648, 47.148071, 0.000005, -24.999958, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14623, "mafcasmain", "ab_tileStar", 0x00000000);
    LSAINTXT = CreateDynamicObject(18980, 2747.801025, -2402.639648, 47.148071, 0.000005, -24.999958, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14623, "mafcasmain", "ab_tileStar", 0x00000000);
    LSAINTXT = CreateDynamicObject(18981, 2782.889160, -2388.138671, 48.667358, 0.000027, 180.000015, 44.999931, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(18980, 2788.886474, -2394.831054, 49.650634, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(18980, 2788.886474, -2394.831054, 49.650634, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(18980, 2777.305908, -2383.242675, 49.650634, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(18980, 2750.936035, -2383.239990, 47.585571, 0.000000, 90.000015, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(18980, 2788.886474, -2421.221923, 47.595092, 0.000014, 90.000000, 89.999954, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(18980, 2738.936035, -2383.239990, 34.640014, 0.000000, 0.000022, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(18980, 2788.886474, -2433.202636, 34.640014, 0.000000, 0.000022, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(18980, 2788.886474, -2396.229736, 33.733642, 0.000007, 90.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(18763, 2751.452880, -2390.168212, 26.214477, 0.000015, 0.000015, 44.999984, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 1975, "texttest", "kb_blue", 0x00000000);
    LSAINTXT = CreateDynamicObject(18763, 2751.658203, -2389.962890, 26.084350, 0.000015, 0.000015, 44.999984, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 1975, "texttest", "kb_blue", 0x00000000);
    LSAINTXT = CreateDynamicObject(18763, 2751.898925, -2389.722167, 25.924194, 0.000015, 0.000015, 44.999984, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 1975, "texttest", "kb_blue", 0x00000000);
    LSAINTXT = CreateDynamicObject(18763, 2752.139648, -2389.481445, 25.804077, 0.000015, 0.000015, 44.999984, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 1975, "texttest", "kb_blue", 0x00000000);
    LSAINTXT = CreateDynamicObject(18763, 2784.967773, -2420.739013, 26.214477, 0.000034, 0.000009, 134.999954, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 1975, "texttest", "kb_blue", 0x00000000);
    LSAINTXT = CreateDynamicObject(18763, 2784.762451, -2420.534179, 26.084350, 0.000034, 0.000009, 134.999954, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 1975, "texttest", "kb_blue", 0x00000000);
    LSAINTXT = CreateDynamicObject(18763, 2784.521728, -2420.292968, 25.924194, 0.000034, 0.000009, 134.999954, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 1975, "texttest", "kb_blue", 0x00000000);
    LSAINTXT = CreateDynamicObject(18763, 2784.281250, -2420.052490, 25.804077, 0.000034, 0.000009, 134.999954, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 1975, "texttest", "kb_blue", 0x00000000);
    LSAINTXT = CreateDynamicObject(18762, 2785.247558, -2419.082519, 38.524658, 0.000000, 0.000022, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 1975, "texttest", "kb_blue", 0x00000000);
    LSAINTXT = CreateDynamicObject(18762, 2752.997314, -2386.901611, 38.524658, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 1975, "texttest", "kb_blue", 0x00000000);
    LSAINTXT = CreateDynamicObject(18762, 2752.237304, -2391.865234, 27.623046, 0.000010, 0.000010, 44.999984, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 1975, "texttest", "kb_blue", 0x00000000);
    LSAINTXT = CreateDynamicObject(18762, 2749.782714, -2389.410644, 27.623046, 0.000010, 0.000010, 44.999984, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 1975, "texttest", "kb_blue", 0x00000000);
    LSAINTXT = CreateDynamicObject(18762, 2786.694824, -2419.932373, 27.623046, 0.000026, 0.000004, 134.999954, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 1975, "texttest", "kb_blue", 0x00000000);
    LSAINTXT = CreateDynamicObject(18762, 2784.249267, -2422.377929, 27.623046, 0.000026, 0.000004, 134.999954, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 1975, "texttest", "kb_blue", 0x00000000);
    LSAINTXT = CreateDynamicObject(18762, 2752.997314, -2390.401367, 38.524658, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 1975, "texttest", "kb_blue", 0x00000000);
    LSAINTXT = CreateDynamicObject(18762, 2781.744628, -2419.082519, 38.524658, 0.000000, 0.000022, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 1975, "texttest", "kb_blue", 0x00000000);
    LSAINTXT = CreateDynamicObject(19482, 2770.464599, -2383.752685, 42.169433, -0.000075, 0.000000, -89.999771, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(LSAINTXT, 0, "o", 130, "Webdings", 199, 1, 0xFF000000, 0x00000000, 1);
    LSAINTXT = CreateDynamicObject(19482, 2770.464599, -2383.742675, 41.709594, -0.000075, 0.000000, -89.999771, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(LSAINTXT, 0, "m", 130, "Wingdings", 199, 1, 0xFFFFFFFF, 0x00000000, 1);
    LSAINTXT = CreateDynamicObject(19482, 2788.340820, -2401.649414, 42.169433, -0.000068, 0.000022, -179.999755, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(LSAINTXT, 0, "o", 130, "Webdings", 199, 1, 0xFF000000, 0x00000000, 1);
    LSAINTXT = CreateDynamicObject(19482, 2788.350830, -2401.649414, 41.709594, -0.000068, 0.000022, -179.999755, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(LSAINTXT, 0, "m", 130, "Wingdings", 199, 1, 0xFFFFFFFF, 0x00000000, 1);
    LSAINTXT = CreateDynamicObject(18766, 2783.341308, -2388.697021, 45.583740, -0.000005, 0.000005, -44.999988, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(1508, 2749.890625, -2431.465332, 29.824707, 0.000005, 0.000005, 44.999988, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14577, "casinovault01", "dts_elevator_door", 0x00000000);
    LSAINTXT = CreateDynamicObject(1508, 2740.693115, -2422.267822, 29.824707, 0.000005, 0.000005, 44.999988, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14577, "casinovault01", "dts_elevator_door", 0x00000000);
    LSAINTXT = CreateDynamicObject(1508, 2745.300537, -2426.875244, 29.824707, 0.000005, 0.000005, 44.999988, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14577, "casinovault01", "dts_elevator_door", 0x00000000);
    LSAINTXT = CreateDynamicObject(18766, 2748.426025, -2430.690673, 33.209472, 89.999992, 40.097309, -85.097282, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(18766, 2741.412109, -2423.676757, 33.209472, 89.999992, 40.097309, -85.097282, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(18766, 2748.426025, -2430.690673, 27.718383, 89.999992, 222.544189, -87.544143, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14735, "newcrak", "carpet-tile", 0x00000000);
    LSAINTXT = CreateDynamicObject(18766, 2741.368896, -2423.633544, 27.718383, 89.999992, 222.544189, -87.544143, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14735, "newcrak", "carpet-tile", 0x00000000);
    LSAINTXT = CreateDynamicObject(18766, 2755.491943, -2437.756591, 27.698364, 89.999992, 220.097305, -85.097282, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14735, "newcrak", "carpet-tile", 0x00000000);
    LSAINTXT = CreateDynamicObject(18766, 2734.302734, -2416.567382, 27.698364, 89.999992, 220.097305, -85.097282, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14735, "newcrak", "carpet-tile", 0x00000000);
    LSAINTXT = CreateDynamicObject(18766, 2755.490966, -2437.755615, 33.209472, 89.999992, 40.097309, -85.097282, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(18766, 2734.348876, -2416.613525, 33.209472, 89.999992, 40.097309, -85.097282, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(18766, 2749.966064, -2434.096191, 30.214233, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 13603, "stad_tag", "Was_swr_wall_blue", 0x00000000);
    LSAINTXT = CreateDynamicObject(18766, 2738.044189, -2422.166992, 30.214233, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 13603, "stad_tag", "Was_swr_wall_blue", 0x00000000);
    LSAINTXT = CreateDynamicObject(18980, 2752.018066, -2433.089843, 45.231811, -0.000010, 0.000010, -44.999984, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 13603, "stad_tag", "Was_swr_wall_blue", 0x00000000);
    LSAINTXT = CreateDynamicObject(18980, 2739.042968, -2420.114746, 45.231811, -0.000010, 0.000010, -44.999984, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 13603, "stad_tag", "Was_swr_wall_blue", 0x00000000);
    LSAINTXT = CreateDynamicObject(18980, 2748.681640, -2429.753417, 45.231811, -0.000010, 0.000010, -44.999984, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 13603, "stad_tag", "Was_swr_wall_blue", 0x00000000);
    LSAINTXT = CreateDynamicObject(18980, 2742.408203, -2423.479980, 45.231811, -0.000010, 0.000010, -44.999984, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 13603, "stad_tag", "Was_swr_wall_blue", 0x00000000);
    LSAINTXT = CreateDynamicObject(18980, 2745.562988, -2426.634765, 45.231811, -0.000010, 0.000010, -44.999984, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 13603, "stad_tag", "Was_swr_wall_blue", 0x00000000);
    LSAINTXT = CreateDynamicObject(18766, 2738.044189, -2388.744628, 30.214233, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 13603, "stad_tag", "Was_swr_wall_blue", 0x00000000);
    LSAINTXT = CreateDynamicObject(18766, 2738.044189, -2388.744628, 35.213867, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 13603, "stad_tag", "Was_swr_wall_blue", 0x00000000);
    LSAINTXT = CreateDynamicObject(18766, 2738.044189, -2388.744628, 40.203857, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 13603, "stad_tag", "Was_swr_wall_blue", 0x00000000);
    LSAINTXT = CreateDynamicObject(18766, 2738.044189, -2388.744628, 45.203369, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 13603, "stad_tag", "Was_swr_wall_blue", 0x00000000);
    LSAINTXT = CreateDynamicObject(18766, 2738.044189, -2388.744628, 50.203857, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 13603, "stad_tag", "Was_swr_wall_blue", 0x00000000);
    LSAINTXT = CreateDynamicObject(18766, 2783.399414, -2434.096191, 30.214233, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 13603, "stad_tag", "Was_swr_wall_blue", 0x00000000);
    LSAINTXT = CreateDynamicObject(18766, 2783.399414, -2434.096191, 35.214233, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 13603, "stad_tag", "Was_swr_wall_blue", 0x00000000);
    LSAINTXT = CreateDynamicObject(18766, 2783.399414, -2434.096191, 40.193969, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 13603, "stad_tag", "Was_swr_wall_blue", 0x00000000);
    LSAINTXT = CreateDynamicObject(18766, 2783.399414, -2434.096191, 45.194091, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 13603, "stad_tag", "Was_swr_wall_blue", 0x00000000);
    LSAINTXT = CreateDynamicObject(18766, 2783.399414, -2434.096191, 50.193725, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 13603, "stad_tag", "Was_swr_wall_blue", 0x00000000);
    LSAINTXT = CreateDynamicObject(2434, 2755.308837, -2406.678466, 28.020263, -0.000007, -0.000000, -89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 896, "underwater", "greyrockbig", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 6869, "vegastemp1", "casinowall1_128", 0x00000000);
    LSAINTXT = CreateDynamicObject(2435, 2756.440185, -2406.845703, 28.024047, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 896, "underwater", "greyrockbig", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 6869, "vegastemp1", "casinowall1_128", 0x00000000);
    LSAINTXT = CreateDynamicObject(2435, 2757.370361, -2406.845703, 28.024047, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 896, "underwater", "greyrockbig", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 6869, "vegastemp1", "casinowall1_128", 0x00000000);
    LSAINTXT = CreateDynamicObject(2435, 2758.300781, -2406.845703, 28.024047, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 896, "underwater", "greyrockbig", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 6869, "vegastemp1", "casinowall1_128", 0x00000000);
    LSAINTXT = CreateDynamicObject(2435, 2759.230712, -2406.845703, 28.024047, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 896, "underwater", "greyrockbig", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 6869, "vegastemp1", "casinowall1_128", 0x00000000);
    LSAINTXT = CreateDynamicObject(2434, 2755.482177, -2401.845703, 28.020263, -0.000006, -0.000007, 179.999847, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 896, "underwater", "greyrockbig", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 6869, "vegastemp1", "casinowall1_128", 0x00000000);
    LSAINTXT = CreateDynamicObject(2435, 2755.314941, -2402.977050, 28.024047, -0.000007, 0.000006, -90.000015, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 896, "underwater", "greyrockbig", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 6869, "vegastemp1", "casinowall1_128", 0x00000000);
    LSAINTXT = CreateDynamicObject(2435, 2755.314941, -2403.907226, 28.024047, -0.000007, 0.000006, -90.000015, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 896, "underwater", "greyrockbig", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 6869, "vegastemp1", "casinowall1_128", 0x00000000);
    LSAINTXT = CreateDynamicObject(2435, 2755.314941, -2404.837646, 28.024047, -0.000007, 0.000006, -90.000015, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 896, "underwater", "greyrockbig", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 6869, "vegastemp1", "casinowall1_128", 0x00000000);
    LSAINTXT = CreateDynamicObject(2435, 2755.314941, -2405.767578, 28.024047, -0.000007, 0.000006, -90.000015, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 896, "underwater", "greyrockbig", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 6869, "vegastemp1", "casinowall1_128", 0x00000000);
    LSAINTXT = CreateDynamicObject(2435, 2759.200683, -2401.843017, 28.024047, 0.000007, -0.000014, 179.999740, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 896, "underwater", "greyrockbig", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 6869, "vegastemp1", "casinowall1_128", 0x00000000);
    LSAINTXT = CreateDynamicObject(2435, 2758.270507, -2401.843017, 28.024047, 0.000007, -0.000014, 179.999740, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 896, "underwater", "greyrockbig", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 6869, "vegastemp1", "casinowall1_128", 0x00000000);
    LSAINTXT = CreateDynamicObject(2435, 2757.340087, -2401.843017, 28.024047, 0.000007, -0.000014, 179.999740, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 896, "underwater", "greyrockbig", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 6869, "vegastemp1", "casinowall1_128", 0x00000000);
    LSAINTXT = CreateDynamicObject(2435, 2756.410156, -2401.843017, 28.024047, 0.000007, -0.000014, 179.999740, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 896, "underwater", "greyrockbig", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 6869, "vegastemp1", "casinowall1_128", 0x00000000);
    LSAINTXT = CreateDynamicObject(2435, 2760.161621, -2406.845703, 28.024047, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 896, "underwater", "greyrockbig", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 6869, "vegastemp1", "casinowall1_128", 0x00000000);
    LSAINTXT = CreateDynamicObject(2435, 2761.091796, -2406.845703, 28.024047, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 896, "underwater", "greyrockbig", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 6869, "vegastemp1", "casinowall1_128", 0x00000000);
    LSAINTXT = CreateDynamicObject(2435, 2762.022216, -2406.845703, 28.024047, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 896, "underwater", "greyrockbig", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 6869, "vegastemp1", "casinowall1_128", 0x00000000);
    LSAINTXT = CreateDynamicObject(2435, 2762.952148, -2406.845703, 28.024047, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 896, "underwater", "greyrockbig", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 6869, "vegastemp1", "casinowall1_128", 0x00000000);
    LSAINTXT = CreateDynamicObject(2595, 2755.347412, -2402.872314, 29.317138, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 19894, "laptopsamp1", "laptopscreen2", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 3, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 4, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(2595, 2755.347412, -2405.913330, 29.317138, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 19894, "laptopsamp1", "laptopscreen2", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 3, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 4, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(2595, 2755.347412, -2404.402099, 29.317138, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 19894, "laptopsamp1", "laptopscreen2", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 3, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 4, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(19720, 2755.620361, -2404.360107, 33.474121, 0.000000, 83.499977, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 6869, "vegastemp1", "casinowall1_128", 0x00000000);
    LSAINTXT = CreateDynamicObject(19786, 2754.355468, -2404.361816, 31.409790, 6.199991, -0.000000, -89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 6985, "vgnfremnt2", "striplightspinky_256", 0x00000000);
    LSAINTXT = CreateDynamicObject(2662, 2754.184814, -2404.332519, 31.258789, 6.199989, 0.000007, -89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(LSAINTXT, 0, "ADA YANG BISA KAMI BANTU?", 130, "Times New Roman", 30, 1, 0xFFFFFFFF, 0x00000000, 1);
    LSAINTXT = CreateDynamicObject(2662, 2754.162597, -2404.332519, 31.467285, 6.199989, 0.000007, -89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(LSAINTXT, 0, "SELAMAT DATANG", 130, "Times New Roman", 60, 1, 0xFFFFFFFF, 0x00000000, 1);
    LSAINTXT = CreateDynamicObject(3533, 2753.472412, -2418.604980, 29.743652, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14623, "mafcasmain", "ab_tileStar", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(3533, 2753.472412, -2384.709472, 29.743652, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14623, "mafcasmain", "ab_tileStar", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(3533, 2787.422851, -2418.604980, 29.743652, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14623, "mafcasmain", "ab_tileStar", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(2611, 2759.036132, -2407.679199, 30.448730, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 3, 14608, "triad_big", "buddha_gold", 0x00000000);
    LSAINTXT = CreateDynamicObject(2611, 2761.586669, -2407.679199, 30.448730, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 3, 14608, "triad_big", "buddha_gold", 0x00000000);
    LSAINTXT = CreateDynamicObject(2484, 2760.337158, -2406.830566, 29.878784, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 18901, "matclothes", "bandanaflag", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 3, 14581, "ab_mafiasuitea", "cof_wood2", 0x00000000);
    LSAINTXT = CreateDynamicObject(2164, 2763.401123, -2404.447265, 28.177490, -0.000007, -0.000000, -89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 18882, "hugebowls", "woodpanel1", 0x00000000);
    LSAINTXT = CreateDynamicObject(2164, 2763.401123, -2402.686279, 28.177490, -0.000007, -0.000000, -89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 18882, "hugebowls", "woodpanel1", 0x00000000);
    LSAINTXT = CreateDynamicObject(2162, 2760.721435, -2407.681152, 31.358520, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 18882, "hugebowls", "woodpanel1", 0x00000000);
    LSAINTXT = CreateDynamicObject(948, 2763.171386, -2401.895263, 28.169677, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 11413, "fosterflowers", "starflower3", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 4, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(948, 2763.171386, -2406.058593, 28.169677, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 11413, "fosterflowers", "starflower3", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 4, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(2286, 2763.412353, -2403.944335, 31.880249, -0.000007, -0.000000, -89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(1806, 2757.544677, -2405.981201, 28.166503, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14808, "lee_strip2", "strip_carpet2", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 18646, "matcolours", "grey-90-percent", 0x00000000);
    LSAINTXT = CreateDynamicObject(1806, 2757.544677, -2402.729492, 28.166503, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14808, "lee_strip2", "strip_carpet2", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 18646, "matcolours", "grey-90-percent", 0x00000000);
    LSAINTXT = CreateDynamicObject(1806, 2757.544677, -2404.390380, 28.166503, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14808, "lee_strip2", "strip_carpet2", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 18646, "matcolours", "grey-90-percent", 0x00000000);
    LSAINTXT = CreateDynamicObject(2245, 2759.235839, -2401.867431, 29.298217, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(1723, 2772.396728, -2433.171386, 28.186889, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(1723, 2738.943847, -2401.771728, 28.186889, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(19482, 2781.539062, -2387.611328, 45.855102, -0.000010, -0.000010, -135.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(LSAINTXT, 0, "SELAMAT", 130, "Arial", 100, 1, 0xFFFFFFFF, 0x00000000, 1);
    LSAINTXT = CreateDynamicObject(19482, 2784.618896, -2390.691162, 45.855102, -0.000010, -0.000010, -135.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(LSAINTXT, 0, "DATANG", 130, "Arial", 100, 1, 0xFFFFFFFF, 0x00000000, 1);
    LSAINTXT = CreateDynamicObject(19482, 2782.020507, -2388.092773, 44.724121, -0.000010, -0.000010, -135.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(LSAINTXT, 0, "DI KOTA", 130, "Arial", 60, 1, 0xFFFFFFFF, 0x00000000, 1);
    LSAINTXT = CreateDynamicObject(19482, 2783.860839, -2389.933105, 44.724121, -0.000010, -0.000010, -135.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(LSAINTXT, 0, "ATHERLIFE", 130, "Arial", 60, 1, 0xFFFFFFFF, 0x00000000, 1);
    LSAINTXT = CreateDynamicObject(1508, 2783.142333, -2388.902099, 37.871337, 0.000005, 0.000005, 44.999988, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10023, "bigwhitesfe", "liftdoors_kb_256", 0x00000000);
    LSAINTXT = CreateDynamicObject(18766, 2782.650878, -2384.211425, 36.028808, 0.000005, 30.000005, 44.999988, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(18766, 2787.813476, -2389.374023, 36.028808, 0.000005, 30.000005, 44.999988, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(18762, 2782.002685, -2387.692138, 40.617309, 89.999992, 144.735610, -99.735618, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(18762, 2784.335937, -2390.025390, 40.617309, 89.999992, 144.735610, -99.735618, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(18766, 2782.961669, -2389.045166, 41.624633, 89.999992, -144.735610, -80.264381, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(18766, 2782.961669, -2389.045166, 35.723876, 89.999992, -144.735610, -80.264381, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14735, "newcrak", "carpet-tile", 0x00000000);
    LSAINTXT = CreateDynamicObject(18766, 2779.433105, -2392.573730, 35.723876, 89.999992, -144.735610, -80.264381, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14735, "newcrak", "carpet-tile", 0x00000000);
    LSAINTXT = CreateDynamicObject(18766, 2775.897949, -2396.108886, 35.723876, 89.999992, -144.735610, -80.264381, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14735, "newcrak", "carpet-tile", 0x00000000);
    LSAINTXT = CreateDynamicObject(18766, 2772.368896, -2399.637939, 35.723876, 89.999992, -144.735610, -80.264381, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14735, "newcrak", "carpet-tile", 0x00000000);
    LSAINTXT = CreateDynamicObject(19482, 2754.251708, -2404.421875, 32.892578, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(LSAINTXT, 0, "LOKET", 130, "Arial", 100, 1, 0xFF000000, 0x00000000, 1);
    LSAINTXT = CreateDynamicObject(18766, 2744.900390, -2420.102050, 27.718383, 89.999992, 222.544189, -87.544143, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14735, "newcrak", "carpet-tile", 0x00000000);
    LSAINTXT = CreateDynamicObject(18766, 2751.959716, -2427.161376, 27.718383, 89.999992, 222.544189, -87.544143, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14735, "newcrak", "carpet-tile", 0x00000000);
    LSAINTXT = CreateDynamicObject(19980, 2752.468017, -2414.146728, 35.747680, 0.000000, 179.999984, -90.000007, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 18646, "matcolours", "lightblue", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 3, 18901, "matclothes", "darkblue", 0x00000000);
    LSAINTXT = CreateDynamicObject(19980, 2752.478027, -2414.146728, 35.747680, 0.000000, -179.999984, 90.000007, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 18646, "matcolours", "lightblue", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 3, 18901, "matclothes", "darkblue", 0x00000000);
    LSAINTXT = CreateDynamicObject(19980, 2757.926757, -2419.617919, 35.747680, 0.000000, -179.999984, -0.000030, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 18646, "matcolours", "lightblue", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 3, 18901, "matclothes", "darkblue", 0x00000000);
    LSAINTXT = CreateDynamicObject(19980, 2757.926757, -2419.607910, 35.747680, 0.000000, 179.999984, 179.999984, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 18646, "matcolours", "lightblue", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 3, 18901, "matclothes", "darkblue", 0x00000000);
    LSAINTXT = CreateDynamicObject(18766, 2759.025878, -2434.227539, 27.698364, 89.999992, 220.097305, -85.097282, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14735, "newcrak", "carpet-tile", 0x00000000);
    LSAINTXT = CreateDynamicObject(18766, 2737.834228, -2413.035888, 27.698364, 89.999992, 220.097305, -85.097282, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14735, "newcrak", "carpet-tile", 0x00000000);
    LSAINTXT = CreateDynamicObject(2714, 2752.414306, -2414.193115, 33.193725, -0.000029, 0.000000, -89.999908, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(LSAINTXT, 0, "KEBERANGKATAN", 130, "Arial", 65, 1, 0xFFFFFFFF, 0x00000000, 1);
    LSAINTXT = CreateDynamicObject(2714, 2752.414306, -2414.012939, 33.083618, -0.000029, 0.000000, -89.999908, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(LSAINTXT, 0, "GATE DEPARTURE", 130, "Arial", 40, 1, 0xFFFFFFFF, 0x00000000, 1);
    LSAINTXT = CreateDynamicObject(2714, 2752.414306, -2413.622558, 33.163696, -0.000029, 0.000000, -89.999908, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(LSAINTXT, 0, "h", 130, "Wingdings 3", 150, 1, 0xFFFFFFFF, 0x00000000, 1);
    LSAINTXT = CreateDynamicObject(2714, 2757.964843, -2419.672851, 33.193725, -0.000029, 0.000045, 0.000061, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(LSAINTXT, 0, "KEBERANGKATAN", 130, "Arial", 65, 1, 0xFFFFFFFF, 0x00000000, 1);
    LSAINTXT = CreateDynamicObject(2714, 2757.784667, -2419.672851, 33.083618, -0.000029, 0.000045, 0.000061, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(LSAINTXT, 0, "GATE DEPARTURE", 130, "Arial", 40, 1, 0xFFFFFFFF, 0x00000000, 1);
    LSAINTXT = CreateDynamicObject(2714, 2757.394287, -2419.672851, 33.163696, -0.000029, 0.000045, 0.000061, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(LSAINTXT, 0, "h", 130, "Wingdings 3", 150, 1, 0xFFFFFFFF, 0x00000000, 1);
    LSAINTXT = CreateDynamicObject(18981, 2776.929199, -2383.223388, 23.702758, -0.000007, 180.000015, -90.000068, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(18981, 2776.929199, -2383.223388, 23.702758, -0.000007, 180.000015, -90.000068, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(18981, 2776.929199, -2391.442626, 23.702758, -0.000007, 180.000015, -90.000068, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(18980, 2788.886474, -2384.228271, 24.690917, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(18980, 2788.886474, -2392.428466, 24.690917, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(18981, 2778.138183, -2391.216308, 23.702758, -0.000000, 180.000015, -0.000091, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 17588, "lae2coast_alpha", "plainglass", 0x00000000);
    LSAINTXT = CreateDynamicObject(1499, 2778.263183, -2406.741699, 28.187988, 0.000029, 0.000000, 89.999908, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 19325, "lsmall_shops", "lsmall_window01", 0x00000000);
    LSAINTXT = CreateDynamicObject(1499, 2778.243164, -2403.729003, 28.187988, -0.000029, 0.000000, -89.999908, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 19325, "lsmall_shops", "lsmall_window01", 0x00000000);
    LSAINTXT = CreateDynamicObject(18766, 2778.153076, -2402.770507, 33.180664, 0.000014, 180.000000, 89.999954, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 19130, "matarrows", "green", 0x00000000);
    LSAINTXT = CreateDynamicObject(18762, 2778.141357, -2403.239501, 28.338256, 0.000000, 0.000029, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 19267, "mapmarkers", "red-2", 0x00000000);
    LSAINTXT = CreateDynamicObject(18762, 2778.141357, -2398.256347, 28.338256, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 19267, "mapmarkers", "red-2", 0x00000000);
    LSAINTXT = CreateDynamicObject(18762, 2778.141357, -2392.435302, 28.338256, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 19267, "mapmarkers", "red-2", 0x00000000);
    LSAINTXT = CreateDynamicObject(18762, 2778.141357, -2392.435302, 31.846923, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 19267, "mapmarkers", "red-2", 0x00000000);
    LSAINTXT = CreateDynamicObject(18766, 2778.093017, -2402.750488, 33.180664, 0.000014, 180.000000, 89.999954, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 19130, "matarrows", "green", 0x00000000);
    LSAINTXT = CreateDynamicObject(18762, 2778.101318, -2403.239501, 28.338256, 0.000000, 0.000037, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 19267, "mapmarkers", "red-2", 0x00000000);
    LSAINTXT = CreateDynamicObject(18762, 2778.101318, -2398.256347, 28.338256, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 19267, "mapmarkers", "red-2", 0x00000000);
    LSAINTXT = CreateDynamicObject(18762, 2778.101318, -2392.435302, 28.338256, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 19267, "mapmarkers", "red-2", 0x00000000);
    LSAINTXT = CreateDynamicObject(18762, 2778.101318, -2392.435302, 31.846923, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 19267, "mapmarkers", "red-2", 0x00000000);
    LSAINTXT = CreateDynamicObject(2626, 2772.501464, -2405.921875, 28.694458, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 19092, "pompoms", "wood-64x64", 0x00000000);
    LSAINTXT = CreateDynamicObject(19482, 2778.658691, -2402.793701, 32.413574, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(LSAINTXT, 0, "ATHERLIFEMART", 130, "Arial", 80, 1, 0xFF000000, 0x00000000, 1);
    LSAINTXT = CreateDynamicObject(18762, 2778.141357, -2407.223144, 28.338256, 0.000000, 0.000029, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 19267, "mapmarkers", "red-2", 0x00000000);
    LSAINTXT = CreateDynamicObject(19482, 2777.587646, -2402.793701, 32.413574, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(LSAINTXT, 0, "ARIVMART", 130, "Arial", 100, 1, 0xFF000000, 0x00000000, 1);
    LSAINTXT = CreateDynamicObject(2599, 2779.102050, -2403.241455, 28.614746, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14636, "mafcas_signs", "sign_careful", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(2257, 2764.443603, -2404.386474, 31.807250, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 7088, "casinoshops1", "247sign1", 0x00000000);
    LSAINTXT = CreateDynamicObject(2257, 2764.443603, -2399.805908, 31.807250, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 3467, "vegstreetsign", "streetsign2_256", 0x00000000);
    LSAINTXT = CreateDynamicObject(2257, 2768.572021, -2391.972656, 31.807250, -0.000000, 0.000007, -0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 3083, "billbox", "Sprunk_postersign1", 0x00000000);
    LSAINTXT = CreateDynamicObject(2257, 2772.922851, -2391.972656, 31.807250, -0.000000, 0.000007, -0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 3434, "skullsign", "vegasstripsign1_256", 0x00000000);
    LSAINTXT = CreateDynamicObject(2784, 2761.933105, -2388.310546, 29.495239, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14652, "ab_trukstpa", "wood01", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 2, 896, "underwater", "greyrockbig", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 3, 14652, "ab_trukstpa", "wood01", 0x00000000);
    LSAINTXT = CreateDynamicObject(2606, 2761.548095, -2388.284423, 30.621948, -0.000007, -0.000000, -89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 896, "underwater", "greyrockbig", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 16644, "a51_detailstuff", "a51_radardisp", 0x00000000);
    LSAINTXT = CreateDynamicObject(1806, 2763.137451, -2388.277099, 28.166503, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14808, "lee_strip2", "strip_carpet2", 0x00000000);
    SetDynamicObjectMaterial(LSAINTXT, 1, 18646, "matcolours", "grey-90-percent", 0x00000000);
    LSAINTXT = CreateDynamicObject(18766, 2790.937988, -2404.739257, 27.690185, 89.999992, 180.000000, -89.999961, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 16639, "a51_labs", "dam_terazzo", 0x00000000);
    LSAINTXT = CreateDynamicObject(18766, 2795.931640, -2394.739013, 27.690185, 89.999992, 180.000015, -89.999961, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 16639, "a51_labs", "dam_terazzo", 0x00000000);
    LSAINTXT = CreateDynamicObject(18766, 2795.931640, -2404.739257, 27.690185, 89.999992, 180.000015, -89.999961, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 16639, "a51_labs", "dam_terazzo", 0x00000000);
    LSAINTXT = CreateDynamicObject(18766, 2800.916015, -2394.739013, 27.690185, 89.999992, 180.000030, -89.999961, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 16639, "a51_labs", "dam_terazzo", 0x00000000);
    LSAINTXT = CreateDynamicObject(18766, 2800.916015, -2404.739257, 27.690185, 89.999992, 180.000030, -89.999961, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 16639, "a51_labs", "dam_terazzo", 0x00000000);
    LSAINTXT = CreateDynamicObject(18981, 2801.941894, -2391.444091, 23.702758, -0.000007, 180.000015, -90.000068, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(18981, 2801.891845, -2409.247558, 23.702758, -0.000007, 180.000015, -90.000068, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    LSAINTXT = CreateDynamicObject(18766, 2805.915283, -2394.739013, 27.690185, 89.999992, 180.000015, -89.999961, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 16639, "a51_labs", "dam_terazzo", 0x00000000);
    LSAINTXT = CreateDynamicObject(18766, 2805.915283, -2404.739257, 27.690185, 89.999992, 180.000015, -89.999961, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 16639, "a51_labs", "dam_terazzo", 0x00000000);
    LSAINTXT = CreateDynamicObject(18766, 2810.908935, -2394.739013, 27.690185, 89.999992, 180.000030, -89.999961, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 16639, "a51_labs", "dam_terazzo", 0x00000000);
    LSAINTXT = CreateDynamicObject(18766, 2810.908935, -2404.739257, 27.690185, 89.999992, 180.000030, -89.999961, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 16639, "a51_labs", "dam_terazzo", 0x00000000);
    LSAINTXT = CreateDynamicObject(18981, 2800.929443, -2397.265625, 34.720458, 0.000000, 270.000000, 179.999862, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 14385, "trailerkb", "tr_wood1", 0x00000000);
    LSAINTXT = CreateDynamicObject(18981, 2803.904052, -2407.741943, 23.702758, 0.000000, 180.000015, 179.999862, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 17588, "lae2coast_alpha", "plainglass", 0x00000000);
    LSAINTXT = CreateDynamicObject(18981, 2792.917236, -2412.746826, 23.702758, 0.000000, 180.000015, 179.999862, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 17588, "lae2coast_alpha", "plainglass", 0x00000000);
    LSAINTXT = CreateDynamicObject(2626, 2808.432128, -2406.812744, 28.694458, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 19092, "pompoms", "wood-64x64", 0x00000000);
    LSAINTXT = CreateDynamicObject(2626, 2770.501220, -2405.921875, 28.694458, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 19092, "pompoms", "wood-64x64", 0x00000000);
    LSAINTXT = CreateDynamicObject(1723, 2796.417968, -2392.447509, 28.168212, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(1723, 2807.383544, -2392.447509, 28.168212, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(1723, 2791.909179, -2403.248535, 28.168212, -0.000007, -0.000000, -89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(18980, 2792.909667, -2408.237060, 22.678955, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(18980, 2803.901123, -2408.237060, 22.678955, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(18980, 2803.901123, -2394.736083, 22.678955, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(18980, 2792.909667, -2399.744628, 22.678955, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(18766, 2788.868164, -2400.926757, 34.298583, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(19482, 2788.365966, -2400.732666, 33.049194, 0.000000, -0.000029, 179.999816, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(LSAINTXT, 0, "AVSTORE", 130, "Arial", 100, 1, 0xFFFFFFFF, 0x00000000, 1);
    LSAINTXT = CreateDynamicObject(19482, 2788.365966, -2400.732666, 32.528808, 0.000000, -0.000029, 179.999816, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(LSAINTXT, 0, "TERSEDIA BAJU DAN ELEKTRONIC HAPPY SHOPING", 130, "Arial", 20, 1, 0xFFFFFFFF, 0x00000000, 1);
    LSAINTXT = CreateDynamicObject(2257, 2797.455078, -2391.972656, 31.807250, -0.000000, 0.000007, -0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 5406, "jeffers5a_lae", "lasuburbansgn1", 0x00000000);
    LSAINTXT = CreateDynamicObject(2257, 2808.368408, -2391.972656, 31.807250, -0.000000, 0.000007, -0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 1, 1346, "roadside", "CJ_PHONESEXADD", 0x00000000);
    LSAINTXT = CreateDynamicObject(18766, 2798.143310, -2397.724853, 37.000244, 0.000000, 90.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(18766, 2808.396240, -2397.724853, 37.000244, 0.000000, 90.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(LSAINTXT, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    LSAINTXT = CreateDynamicObject(19482, 2798.124755, -2397.223632, 33.049194, 0.000007, -0.000021, 89.999855, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(LSAINTXT, 0, "ATHERLIFE CLOTHES", 130, "Arial", 50, 1, 0xFFFFFFFF, 0x00000000, 1);
    LSAINTXT = CreateDynamicObject(19482, 2808.378906, -2397.223632, 33.049194, 0.000007, -0.000021, 89.999855, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(LSAINTXT, 0, "ATHERLIFE ELECTRIC", 130, "Arial", 50, 1, 0xFFFFFFFF, 0x00000000, 1);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(11710, 2745.253906, -2426.910400, 31.748413, -0.000005, 0.000005, -44.999988, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(11710, 2740.645751, -2422.302246, 31.748413, -0.000005, 0.000005, -44.999988, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(11710, 2749.840576, -2431.497070, 31.748413, -0.000005, 0.000005, -44.999988, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(14629, 2743.527832, -2397.834960, 47.286743, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(14629, 2758.636474, -2428.600097, 47.286743, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 2749.199462, -2394.529052, 48.158691, 0.000000, 0.000029, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 2760.199462, -2394.529052, 48.158691, 0.000000, 0.000029, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 2760.199462, -2407.741210, 48.158691, 0.000000, 0.000029, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 2749.199462, -2407.741210, 48.158691, 0.000000, 0.000029, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 2770.696289, -2394.529052, 48.158691, 0.000000, 0.000037, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 2770.696289, -2407.741210, 48.158691, 0.000000, 0.000037, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 2760.199462, -2420.741210, 48.158691, 0.000000, 0.000029, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 2781.259521, -2394.529052, 48.158691, 0.000000, 0.000045, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 2781.259521, -2407.741210, 48.158691, 0.000000, 0.000045, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 2770.696289, -2420.741210, 48.158691, 0.000000, 0.000037, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 2781.259521, -2420.741210, 48.158691, 0.000000, 0.000045, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 2749.199462, -2420.792968, 48.158691, 0.000000, 0.000029, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19806, 2750.724609, -2430.728515, 32.595214, -0.000021, 0.000021, -44.999984, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19806, 2746.132080, -2426.135986, 32.595214, -0.000021, 0.000021, -44.999984, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19806, 2741.515136, -2421.519042, 32.595214, -0.000021, 0.000021, -44.999984, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19808, 2755.695800, -2402.892578, 29.094360, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19808, 2755.695800, -2404.424072, 29.094360, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19808, 2755.695800, -2405.935546, 29.094360, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1620, 2761.628906, -2393.399414, 34.173339, 0.000000, 270.000000, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1620, 2761.628906, -2403.381835, 34.173339, 0.000000, 270.000000, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1620, 2758.710693, -2411.727539, 34.173339, 0.000029, 270.000000, 89.999908, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1620, 2758.710693, -2410.727539, 34.173339, 0.000029, 270.000000, 89.999908, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1620, 2768.731933, -2410.727539, 34.173339, 0.000029, 270.000000, 89.999908, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1620, 2778.703613, -2410.727539, 34.173339, 0.000029, 270.000000, 89.999908, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1620, 2768.731933, -2411.727539, 34.173339, 0.000029, 270.000000, 89.999908, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1620, 2778.703613, -2411.727539, 34.173339, 0.000029, 270.000000, 89.999908, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1620, 2760.628906, -2393.399414, 34.173339, 0.000000, 270.000000, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1620, 2760.628906, -2403.381835, 34.173339, 0.000000, 270.000000, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19806, 2758.125000, -2393.383300, 33.949707, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19806, 2758.125000, -2404.264648, 33.949707, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19806, 2758.705566, -2414.406005, 33.949707, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19806, 2768.725830, -2414.406005, 33.949707, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19806, 2778.705810, -2414.406005, 33.949707, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2257, 2738.463378, -2401.738525, 34.020507, 0.000014, 0.000000, 89.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2257, 2738.463378, -2399.718505, 34.020507, 0.000014, 0.000000, 89.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2257, 2738.463378, -2399.718505, 36.120971, 0.000014, 0.000000, 89.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2257, 2738.463378, -2401.739746, 36.120971, 0.000014, 0.000000, 89.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2257, 2738.463378, -2401.738525, 38.221923, 0.000022, 0.000000, 89.999931, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2257, 2738.463378, -2399.718505, 38.221923, 0.000022, 0.000000, 89.999931, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2257, 2738.463378, -2399.718505, 40.322387, 0.000022, 0.000000, 89.999931, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2257, 2738.463378, -2401.739746, 40.322387, 0.000022, 0.000000, 89.999931, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2257, 2772.429687, -2433.671386, 34.020507, 0.000030, -0.000022, 179.999740, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2257, 2770.409667, -2433.671386, 34.020507, 0.000030, -0.000022, 179.999740, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2257, 2770.409667, -2433.671386, 36.120971, 0.000030, -0.000022, 179.999740, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2257, 2772.430908, -2433.671386, 36.120971, 0.000030, -0.000022, 179.999740, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2257, 2772.429687, -2433.671386, 38.221923, 0.000038, -0.000022, 179.999725, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2257, 2770.409667, -2433.671386, 38.221923, 0.000038, -0.000022, 179.999725, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2257, 2770.409667, -2433.671386, 40.322387, 0.000038, -0.000022, 179.999725, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2257, 2772.430908, -2433.671386, 40.322387, 0.000038, -0.000022, 179.999725, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(14805, 2773.925781, -2415.182373, 37.027343, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(14805, 2763.515869, -2415.182373, 37.027343, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(14805, 2756.903076, -2407.652587, 37.027343, -0.000007, -0.000000, -89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(14805, 2756.903076, -2407.652587, 37.027343, -0.000007, -0.000000, -89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(14805, 2756.903076, -2398.310546, 37.027343, -0.000007, -0.000000, -89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 2776.617675, -2410.673339, 36.732788, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 2762.625732, -2410.673339, 36.732788, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 2764.964599, -2410.673339, 36.732788, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 2767.294189, -2410.673339, 36.732788, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 2769.623291, -2410.673339, 36.732788, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 2771.953613, -2410.673339, 36.732788, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 2774.283203, -2410.673339, 36.732788, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 2761.444580, -2409.333496, 36.732788, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 2761.444580, -2406.994140, 36.732788, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 2761.444580, -2404.664550, 36.732788, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 2761.444580, -2402.334228, 36.732788, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 2761.444580, -2400.004882, 36.732788, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 2761.444580, -2397.675292, 36.732788, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 2761.444580, -2395.295898, 36.732788, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 2766.105957, -2384.113037, 36.901611, -0.000007, -0.000000, -89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 2775.077636, -2384.113037, 36.901611, -0.000007, -0.000000, -89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 2788.019042, -2397.013671, 36.901611, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 2788.019042, -2405.935058, 36.901611, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 2738.765380, -2414.502197, 28.861572, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 2738.765380, -2409.021728, 28.861572, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 2757.674804, -2433.327148, 28.861572, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 2763.156250, -2433.327148, 28.861572, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 2783.429931, -2433.327148, 28.861572, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 2738.765380, -2388.750000, 28.861572, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 2767.578857, -2409.103027, 28.861572, -0.000007, -0.000000, -89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 2772.948242, -2409.103027, 28.861572, -0.000007, -0.000000, -89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 2778.319091, -2409.103027, 28.861572, -0.000007, -0.000000, -89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(628, 2770.619628, -2384.366943, 38.182128, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(628, 2787.751464, -2401.447509, 38.182128, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(628, 2753.501464, -2432.968261, 30.176391, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(628, 2739.178466, -2418.617431, 30.176391, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2752.090820, -2397.571533, 28.682128, 0.000022, 0.000000, 89.999931, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2752.090820, -2402.571533, 28.682128, 0.000022, 0.000000, 89.999931, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2752.090820, -2407.571533, 28.682128, 0.000022, 0.000000, 89.999931, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2751.271972, -2407.571533, 28.682128, -0.000007, 0.000000, -89.999946, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2751.271972, -2402.571533, 28.682128, -0.000007, 0.000000, -89.999946, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2751.271972, -2397.571533, 28.682128, -0.000007, 0.000000, -89.999946, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2748.090820, -2397.571533, 28.682128, 0.000022, 0.000000, 89.999931, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2748.090820, -2402.571533, 28.682128, 0.000022, 0.000000, 89.999931, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2748.090820, -2407.571533, 28.682128, 0.000022, 0.000000, 89.999931, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2747.271972, -2407.571533, 28.682128, -0.000007, 0.000000, -89.999946, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2747.271972, -2402.571533, 28.682128, -0.000007, 0.000000, -89.999946, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2747.271972, -2397.571533, 28.682128, -0.000007, 0.000000, -89.999946, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2744.090820, -2397.571533, 28.682128, 0.000022, 0.000000, 89.999931, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2744.090820, -2402.571533, 28.682128, 0.000022, 0.000000, 89.999931, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2744.090820, -2407.571533, 28.682128, 0.000022, 0.000000, 89.999931, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2743.271972, -2407.571533, 28.682128, -0.000007, 0.000000, -89.999946, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2743.271972, -2402.571533, 28.682128, -0.000007, 0.000000, -89.999946, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2743.271972, -2397.571533, 28.682128, -0.000007, 0.000000, -89.999946, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2765.253173, -2420.023681, 28.682128, 0.000022, -0.000037, 179.999679, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2770.253173, -2420.023681, 28.682128, 0.000022, -0.000037, 179.999679, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2775.253173, -2420.023681, 28.682128, 0.000022, -0.000037, 179.999679, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2775.253173, -2420.842529, 28.682128, -0.000006, 0.000037, 0.000021, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2770.253173, -2420.842529, 28.682128, -0.000006, 0.000037, 0.000021, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2765.253173, -2420.842529, 28.682128, -0.000006, 0.000037, 0.000021, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2765.253173, -2424.023681, 28.682128, 0.000022, -0.000037, 179.999679, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2770.253173, -2424.023681, 28.682128, 0.000022, -0.000037, 179.999679, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2775.253173, -2424.023681, 28.682128, 0.000022, -0.000037, 179.999679, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2775.253173, -2424.842529, 28.682128, -0.000006, 0.000037, 0.000021, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2770.253173, -2424.842529, 28.682128, -0.000006, 0.000037, 0.000021, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2765.253173, -2424.842529, 28.682128, -0.000006, 0.000037, 0.000021, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2765.253173, -2428.023681, 28.682128, 0.000022, -0.000037, 179.999679, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2770.253173, -2428.023681, 28.682128, 0.000022, -0.000037, 179.999679, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2775.253173, -2428.023681, 28.682128, 0.000022, -0.000037, 179.999679, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2775.253173, -2428.842529, 28.682128, -0.000006, 0.000037, 0.000021, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2770.253173, -2428.842529, 28.682128, -0.000006, 0.000037, 0.000021, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2765.253173, -2428.842529, 28.682128, -0.000006, 0.000037, 0.000021, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(11710, 2783.069091, -2388.966308, 39.838378, -0.000005, 0.000005, -44.999988, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19806, 2781.955810, -2390.079101, 40.820922, 0.000005, 0.000005, 44.999988, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19806, 2781.127929, -2389.251220, 40.820922, 0.000005, 0.000005, 44.999988, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19806, 2782.789550, -2390.912841, 40.820922, 0.000005, 0.000005, 44.999988, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2375, 2801.471923, -2408.713867, 28.119873, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2375, 2801.471923, -2408.713867, 30.579956, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2447, 2769.011718, -2405.771484, 28.169555, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1890, 2769.338867, -2401.985351, 28.160034, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1890, 2769.338867, -2398.025634, 28.160034, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1890, 2769.338867, -2393.973388, 28.160034, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1890, 2772.360351, -2401.985351, 28.160034, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1890, 2772.360351, -2398.025634, 28.160034, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1890, 2772.360351, -2393.973388, 28.160034, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2578, 2773.488525, -2407.617919, 30.943237, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2578, 2772.448242, -2407.617919, 30.943237, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2578, 2771.408447, -2407.617919, 30.943237, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2578, 2770.368408, -2407.617919, 30.943237, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2578, 2769.328613, -2407.617919, 30.943237, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2387, 2800.145019, -2404.387939, 28.184692, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1885, 2774.223876, -2405.802001, 28.179931, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2779, 2765.852783, -2407.204345, 28.176391, 0.000000, -0.000014, 179.999908, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2779, 2766.703613, -2407.204345, 28.176391, 0.000000, -0.000014, 179.999908, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2452, 2764.810791, -2393.073730, 28.097412, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2452, 2764.810791, -2394.655029, 28.097412, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 2770.983886, -2399.899658, 34.275390, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(11745, 2761.952880, -2384.823974, 29.159545, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(11745, 2761.952880, -2385.323974, 29.159545, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(11745, 2761.952880, -2385.823974, 29.159545, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(11745, 2761.952880, -2386.323974, 29.159545, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(11745, 2761.952880, -2386.823974, 29.159545, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(11745, 2761.952880, -2387.323974, 29.159545, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(11745, 2761.952880, -2387.823974, 29.159545, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(11745, 2761.952880, -2388.474121, 29.159545, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(11745, 2761.952880, -2389.474121, 29.159545, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(11745, 2761.952880, -2390.474121, 29.159545, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(11745, 2761.952880, -2391.474121, 29.159545, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 2760.728759, -2391.884033, 28.705322, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 2760.728759, -2384.893310, 28.705322, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 2760.728759, -2387.223388, 28.705322, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 2760.728759, -2389.553955, 28.705322, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(922, 2762.958496, -2396.892578, 29.051025, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(922, 2762.038330, -2396.892578, 29.051025, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(922, 2783.482177, -2392.420654, 29.051025, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 2783.614257, -2399.899658, 34.275390, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 2778.839111, -2400.053955, 28.872802, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 2778.839111, -2394.693847, 28.872802, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2626, 2794.953125, -2406.082031, 28.694458, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2387, 2800.145019, -2406.239257, 28.184692, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2622, 2796.351806, -2406.139160, 28.947875, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2625, 2796.030517, -2408.315185, 29.700561, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19786, 2808.422851, -2408.757568, 32.581787, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19787, 2808.411865, -2408.762207, 31.217285, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2229, 2809.800048, -2407.199707, 28.170898, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2229, 2806.919189, -2407.199707, 28.170898, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2229, 2806.419189, -2407.199707, 28.170898, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2229, 2809.300048, -2407.199707, 28.170898, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19422, 2807.853027, -2406.830078, 29.228271, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19422, 2807.853027, -2406.619873, 29.228271, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(18868, 2807.533447, -2406.675781, 29.203979, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(18868, 2807.533447, -2406.865966, 29.203979, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(18868, 2807.623535, -2406.865966, 29.203979, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(18868, 2807.623535, -2406.675781, 29.203979, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19893, 2808.411865, -2406.745849, 29.219116, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1850, 2805.280273, -2403.833496, 28.120239, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1850, 2805.280273, -2400.833007, 28.120239, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1850, 2805.280273, -2397.842285, 28.120239, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1850, 2811.526123, -2398.342773, 28.120239, -0.000014, 0.000000, -89.999923, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1850, 2811.526123, -2401.343261, 28.120239, -0.000014, 0.000000, -89.999923, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1850, 2811.526123, -2404.333984, 28.120239, -0.000014, 0.000000, -89.999923, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1884, 2808.459716, -2402.217285, 28.140258, -0.000007, -0.000000, -89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1884, 2808.459716, -2398.125488, 28.140258, -0.000007, -0.000000, -89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2652, 2794.172851, -2406.086669, 29.711059, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2371, 2796.117919, -2402.338623, 28.159667, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2371, 2800.930908, -2402.338623, 28.159667, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2394, 2795.470458, -2401.960449, 28.891845, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2394, 2795.470458, -2402.621093, 28.891845, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2394, 2800.312011, -2402.621093, 28.891845, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(626, 2802.866455, -2395.749267, 30.181518, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(626, 2791.872802, -2400.759765, 30.181518, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 2783.614257, -2399.899658, 34.275390, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 2798.289550, -2399.899658, 34.275390, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 2808.300292, -2399.899658, 34.275390, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00);
}