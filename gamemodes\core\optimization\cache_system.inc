// ========================================
// CACHE SYSTEM - ANTI-LAG OPTIMIZATION
// ========================================
// Sistem cache untuk mengurangi database queries dan meningkatkan performance

#include <YSI_Coding\y_hooks>

// Cache variables
static playerDataCache[MAX_PLAYERS][32]; // Cache untuk data player yang sering diakses
static vehicleDataCache[MAX_VEHICLES][16]; // Cache untuk data vehicle
static lastCacheUpdate[MAX_PLAYERS]; // Timestamp terakhir cache diupdate
static lastVehicleCacheUpdate[MAX_VEHICLES];

// Cache intervals (dalam detik)
#define PLAYER_CACHE_INTERVAL 30 // Update cache player setiap 30 detik
#define VEHICLE_CACHE_INTERVAL 60 // Update cache vehicle setiap 60 detik

// Optimized player data caching
UpdatePlayerCache(playerid)
{
    if(!IsPlayerConnected(playerid) || !AccountData[playerid][IsLoggedIn])
        return 0;
    
    new currentTime = gettime();
    if(currentTime - lastCacheUpdate[playerid] < PLAYER_CACHE_INTERVAL)
        return 0; // Skip if cache is still fresh
    
    // Cache frequently accessed data
    playerDataCache[playerid][0] = AccountData[playerid][pLevel];
    playerDataCache[playerid][1] = AccountData[playerid][pMoney];
    playerDataCache[playerid][2] = AccountData[playerid][pBankMoney];
    playerDataCache[playerid][3] = AccountData[playerid][pAdmin];
    playerDataCache[playerid][4] = AccountData[playerid][pFaction];
    
    lastCacheUpdate[playerid] = currentTime;
    return 1;
}

// Optimized vehicle data caching
// DISABLED: Function not used - causes warning
// UpdateVehicleCache(vehicleid)
// {
//     if(!IsValidVehicle(vehicleid))
//         return 0;
//
//     new currentTime = gettime();
//     if(currentTime - lastVehicleCacheUpdate[vehicleid] < VEHICLE_CACHE_INTERVAL)
//         return 0;
//
//     // Cache vehicle data
//     vehicleDataCache[vehicleid][0] = GetVehicleModel(vehicleid);
//     GetVehicleHealth(vehicleid, Float:vehicleDataCache[vehicleid][1]);
//     vehicleDataCache[vehicleid][2] = GetVehicleFuel(vehicleid);
//
//     lastVehicleCacheUpdate[vehicleid] = currentTime;
//     return 1;
// }

// Batch database operations
static batchQueryQueue[50][512]; // Queue untuk batch queries
static batchQueryCount = 0;

// Add query to batch
AddToBatchQueue(const query[])
{
    if(batchQueryCount >= 50)
    {
        FlushBatchQueue();
    }
    
    strcopy(batchQueryQueue[batchQueryCount], query);
    batchQueryCount++;
    return 1;
}

// Execute all queued queries at once
FlushBatchQueue()
{
    if(batchQueryCount == 0) return 0;
    
    new bigQuery[25600] = ""; // Large buffer for combined queries
    
    for(new i = 0; i < batchQueryCount; i++)
    {
        strcat(bigQuery, batchQueryQueue[i]);
        if(i < batchQueryCount - 1) strcat(bigQuery, "; ");
    }
    
    mysql_pquery(g_SQL, bigQuery);
    batchQueryCount = 0;
    
    printf("[OPTIMIZATION] Flushed %d batched queries", batchQueryCount);
    return 1;
}

// Timer untuk flush batch queries secara berkala
task BatchFlushTimer[10000]() // Setiap 10 detik
{
    if(batchQueryCount > 0)
    {
        FlushBatchQueue();
    }
    return 1;
}

// Optimized player data saving
OptimizedSavePlayerData(playerid)
{
    if(!AccountData[playerid][IsLoggedIn]) return 0;
    
    new query[512];
    mysql_format(g_SQL, query, sizeof(query),
        "UPDATE `player_ucp` SET `Money`=%d, `BankMoney`=%d, `Level`=%d WHERE `ID`=%d",
        AccountData[playerid][pMoney],
        AccountData[playerid][pBankMoney], 
        AccountData[playerid][pLevel],
        AccountData[playerid][pID]
    );
    
    AddToBatchQueue(query);
    return 1;
}

// Memory optimization - clear unused data
ClearPlayerCache(playerid)
{
    for(new i = 0; i < 32; i++)
    {
        playerDataCache[playerid][i] = 0;
    }
    lastCacheUpdate[playerid] = 0;
    return 1;
}

ClearVehicleCache(vehicleid)
{
    for(new i = 0; i < 16; i++)
    {
        vehicleDataCache[vehicleid][i] = 0;
    }
    lastVehicleCacheUpdate[vehicleid] = 0;
    return 1;
}

// Hooks
hook OnPlayerConnect(playerid)
{
    ClearPlayerCache(playerid);
    return 1;
}

hook OnPlayerDisconnect(playerid, reason)
{
    // Save any pending data before clearing cache
    OptimizedSavePlayerData(playerid);
    ClearPlayerCache(playerid);
    return 1;
}

hook OnVehicleSpawn(vehicleid)
{
    ClearVehicleCache(vehicleid);
    return 1;
}

hook OnVehicleDeath(vehicleid, killerid)
{
    ClearVehicleCache(vehicleid);
    return 1;
}

// Optimized string operations
stock FastStringCopy(dest[], const source[], maxlength = sizeof(dest))
{
    // Optimized string copy using direct memory operations
    new len = strlen(source);
    if(len >= maxlength) len = maxlength - 1;
    
    for(new i = 0; i < len; i++)
    {
        dest[i] = source[i];
    }
    dest[len] = EOS;
    return len;
}

// Memory pool for temporary strings
static stringPool[10][256];
static stringPoolIndex = 0;

stock GetTempString()
{
    stringPoolIndex = (stringPoolIndex + 1) % 10;
    stringPool[stringPoolIndex][0] = EOS;
    return stringPool[stringPoolIndex];
}

// Performance monitoring
static performanceStats[5];
#define PERF_QUERIES_PER_SEC 0
#define PERF_PLAYERS_ONLINE 1
#define PERF_VEHICLES_ACTIVE 2
#define PERF_MEMORY_USAGE 3
#define PERF_CPU_USAGE 4

UpdatePerformanceStats()
{
    performanceStats[PERF_PLAYERS_ONLINE] = Iter_Count(Player);
    performanceStats[PERF_VEHICLES_ACTIVE] = Iter_Count(Vehicle);
    
    // Log performance stats setiap 5 menit
    static lastPerfLog = 0;
    if(gettime() - lastPerfLog > 300)
    {
        printf("[PERFORMANCE] Players: %d | Vehicles: %d | Batch Queue: %d", 
            performanceStats[PERF_PLAYERS_ONLINE],
            performanceStats[PERF_VEHICLES_ACTIVE],
            batchQueryCount
        );
        lastPerfLog = gettime();
    }
    return 1;
}

// Task untuk update performance stats
task PerformanceMonitor[30000]() // Setiap 30 detik
{
    UpdatePerformanceStats();
    return 1;
}

// Optimized distance calculation (tanpa sqrt untuk perbandingan)
stock Float:GetDistanceSquared(Float:x1, Float:y1, Float:z1, Float:x2, Float:y2, Float:z2)
{
    new Float:dx = x1 - x2;
    new Float:dy = y1 - y2;
    new Float:dz = z1 - z2;
    return (dx * dx) + (dy * dy) + (dz * dz);
}

// Fast player distance check (tanpa sqrt)
stock bool:IsPlayerInRangeSquared(playerid, Float:x, Float:y, Float:z, Float:rangeSquared)
{
    new Float:px, Float:py, Float:pz;
    GetPlayerPos(playerid, px, py, pz);
    return GetDistanceSquared(px, py, pz, x, y, z) <= rangeSquared;
}

// Optimized player iteration dengan limit
stock OptimizedPlayerLoop(maxPlayers = 20)
{
    new count = 0;
    foreach(new playerid : Player)
    {
        if(count >= maxPlayers) break;
        
        // Update cache untuk player ini
        UpdatePlayerCache(playerid);
        count++;
    }
    return count;
}

// Task untuk update cache secara berkala
task CacheUpdateTimer[15000]() // Setiap 15 detik
{
    OptimizedPlayerLoop(10); // Maksimal 10 player per cycle
    return 1;
}
