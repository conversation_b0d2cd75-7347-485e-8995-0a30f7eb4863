// ========================================
// TIMER OPTIMIZATION SYSTEM
// ========================================
// Sistem optimasi untuk timer dan task agar tidak lag

#include <YSI_Coding\y_hooks>

// Timer load balancing
static timerLoadBalance = 0;
static playerTaskBalance[MAX_PLAYERS];

// Optimized timer intervals
#define FAST_TIMER_INTERVAL 1000    // 1 detik - untuk checks penting
#define MEDIUM_TIMER_INTERVAL 3000  // 3 detik - untuk checks normal  
#define SLOW_TIMER_INTERVAL 10000   // 10 detik - untuk checks tidak penting

// Load balancing untuk player tasks
GetNextPlayerForTask()
{
    new playerCount = Iter_Count(Player);
    if(playerCount == 0) return INVALID_PLAYER_ID;
    
    new targetPlayer = INVALID_PLAYER_ID;
    new currentIndex = 0;
    
    foreach(new playerid : Player)
    {
        if(currentIndex == (timerLoadBalance % playerCount))
        {
            targetPlayer = playerid;
            break;
        }
        currentIndex++;
    }
    
    timerLoadBalance++;
    if(timerLoadBalance >= playerCount) timerLoadBalance = 0;
    
    return targetPlayer;
}

// Optimized player update task - hanya 1 player per cycle
task OptimizedPlayerUpdate[1000]()
{
    new playerid = GetNextPlayerForTask();
    if(playerid == INVALID_PLAYER_ID) return 1;
    
    if(!IsPlayerConnected(playerid) || !AccountData[playerid][pSpawned])
        return 1;
    
    // Update hanya data penting
    playerTaskBalance[playerid]++;
    
    // Rotasi checks berdasarkan balance
    switch(playerTaskBalance[playerid] % 5)
    {
        case 0: // Check health dan armor
        {
            new Float:health, Float:armour;
            GetPlayerHealth(playerid, health);
            GetPlayerArmour(playerid, armour);
            
            if(health > 100.0 && !AccountData[playerid][pAdminDuty])
            {
                SetPlayerHealthEx(playerid, 100.0);
            }
        }
        case 1: // Check money
        {
            if(GetPlayerMoney(playerid) != AccountData[playerid][pMoney])
            {
                ResetPlayerMoney(playerid);
                GivePlayerMoneyEx(playerid, AccountData[playerid][pMoney]);
            }
        }
        case 2: // Check weapons
        {
            if(GetPlayerWeapon(playerid) == 40) // Detonator
            {
                KickEx(playerid);
                return 1;
            }
        }
        case 3: // Check vehicle
        {
            if(IsPlayerInAnyVehicle(playerid))
            {
                new vehicleid = GetPlayerVehicleID(playerid);
                if(!IsAPlane(vehicleid))
                {
                    new Float:speed = GetPlayerSpeed(playerid);
                    if(speed > 400.0)
                    {
                        SendClientMessage(playerid, Y_RED, "[AntiCheat] Speed hack detected!");
                        KickEx(playerid);
                        return 1;
                    }
                }
            }
        }
        case 4: // Check ping (setiap 5 detik)
        {
            new ping = GetPlayerPing(playerid);
            if(ping > 800)
            {
                SendClientMessage(playerid, Y_RED, "[Anti-Lag] High ping detected!");
                KickEx(playerid);
                return 1;
            }
        }
    }
    
    return 1;
}

// Optimized vehicle check - hanya beberapa vehicle per cycle
static vehicleCheckIndex = 0;
task OptimizedVehicleCheck[2000]()
{
    new vehicleCount = Iter_Count(Vehicle);
    if(vehicleCount == 0) return 1;

    new checked = 0;

    foreach(new vehicleid : Vehicle)
    {
        if(checked >= 10) break; // Maksimal 10 vehicle per cycle
        
        if(vehicleCheckIndex > 0)
        {
            vehicleCheckIndex--;
            continue;
        }
        
        // Check vehicle health
        new Float:health;
        GetVehicleHealth(vehicleid, health);
        
        if(health > VehicleCore[vehicleid][vMaxHealth])
        {
            SetValidVehicleHealth(vehicleid, VehicleCore[vehicleid][vMaxHealth]);
        }
        
        // Check velocity untuk anti speed hack
        new Float:vel[3];
        GetVehicleVelocity(vehicleid, vel[0], vel[1], vel[2]);
        new Float:totalVel = floatsqroot((vel[0] * vel[0]) + (vel[1] * vel[1]) + (vel[2] * vel[2]));
        
        if(totalVel > 3.0 && !IsAPlane(vehicleid))
        {
            // Check if someone is driving
            foreach(new playerid : Player)
            {
                if(GetPlayerVehicleID(playerid) == vehicleid && GetPlayerState(playerid) == PLAYER_STATE_DRIVER)
                {
                    SendClientMessage(playerid, Y_RED, "[AntiCheat] Vehicle speed hack detected!");
                    break;
                }
            }
        }
        
        checked++;
        vehicleCheckIndex++;
    }
    
    // Reset index jika sudah mencapai akhir
    if(vehicleCheckIndex >= vehicleCount)
    {
        vehicleCheckIndex = 0;
    }
    
    return 1;
}

// Optimized database save - batch processing
static saveQueue[MAX_PLAYERS];
static saveQueueCount = 0;

AddPlayerToSaveQueue(playerid)
{
    if(saveQueueCount >= MAX_PLAYERS) return 0;
    
    saveQueue[saveQueueCount] = playerid;
    saveQueueCount++;
    return 1;
}

// Process save queue setiap 30 detik
task ProcessSaveQueue[30000]()
{
    if(saveQueueCount == 0) return 1;
    
    new query[2048] = "INSERT INTO `player_stats_temp` (`PlayerID`, `Money`, `Level`, `LastUpdate`) VALUES ";
    new bool:hasData = false;
    
    for(new i = 0; i < saveQueueCount; i++)
    {
        new playerid = saveQueue[i];
        if(!IsPlayerConnected(playerid)) continue;
        
        if(hasData) strcat(query, ", ");
        
        new tempStr[128];
        format(tempStr, sizeof(tempStr), "(%d, %d, %d, NOW())", 
            AccountData[playerid][pID],
            AccountData[playerid][pMoney],
            AccountData[playerid][pLevel]
        );
        strcat(query, tempStr);
        hasData = true;
    }
    
    if(hasData)
    {
        strcat(query, " ON DUPLICATE KEY UPDATE `Money`=VALUES(`Money`), `Level`=VALUES(`Level`), `LastUpdate`=NOW()");
        mysql_pquery(g_SQL, query);
        printf("[OPTIMIZATION] Batch saved %d players", saveQueueCount);
    }
    
    saveQueueCount = 0;
    return 1;
}

// Memory optimization - clear unused arrays
task MemoryCleanup[300000]() // Setiap 5 menit
{
    // Clear disconnected player data
    for(new i = 0; i < MAX_PLAYERS; i++)
    {
        if(!IsPlayerConnected(i))
        {
            // Reset player arrays
            playerTaskBalance[i] = 0;
            
            // Clear inventory data jika player disconnect
            for(new x = 0; x < MAX_INVENTORY; x++)
            {
                if(!InventoryData[i][x][invExists])
                {
                    InventoryData[i][x][invItem][0] = EOS;
                    InventoryData[i][x][invModel] = 0;
                    InventoryData[i][x][invQuantity] = 0;
                }
            }
        }
    }
    
    printf("[OPTIMIZATION] Memory cleanup completed");
    return 1;
}

// Optimized string operations
stock OptimizedFormat(dest[], size, const format[], {Float,_}:...)
{
    // Use faster formatting for simple cases
    if(numargs() == 3) // No additional arguments
    {
        return strcopy(dest, format, size);
    }
    else
    {
        return format(dest, size, format, ___(3));
    }
}

// Fast player name caching
static playerNameCache[MAX_PLAYERS][MAX_PLAYER_NAME];
static playerNameCacheTime[MAX_PLAYERS];

stock GetPlayerNameFast(playerid, dest[], len = sizeof(dest))
{
    if(!IsPlayerConnected(playerid))
    {
        strcopy(dest, "Unknown", len);
        return 0;
    }

    new currentTime = gettime();
    if(currentTime - playerNameCacheTime[playerid] > 60) // Cache for 1 minute
    {
        GetPlayerName(playerid, playerNameCache[playerid], MAX_PLAYER_NAME);
        playerNameCacheTime[playerid] = currentTime;
    }

    strcopy(dest, playerNameCache[playerid], len);
    return 1;
}

// Optimized distance checks - DISABLED (conflict with existing function)
// Function IsPlayerNearPlayer already exists in codebase

// DISABLED: Performance monitoring - not used
// static performanceMetrics[10];
// #define PERF_TIMER_CALLS 0
// #define PERF_DB_QUERIES 1
// #define PERF_PLAYER_UPDATES 2

// DISABLED: Function not used - causes warning
// UpdatePerformanceMetrics()
// {
//     performanceMetrics[PERF_TIMER_CALLS]++;
//
//     // Log metrics setiap 10 menit
//     static lastMetricLog = 0;
//     if(gettime() - lastMetricLog > 600)
//     {
//         printf("[PERFORMANCE] Timer calls: %d | DB queries: %d | Player updates: %d",
//             performanceMetrics[PERF_TIMER_CALLS],
//             performanceMetrics[PERF_DB_QUERIES],
//             performanceMetrics[PERF_PLAYER_UPDATES]
//         );
//
//         // Reset counters
//         for(new i = 0; i < 10; i++)
//         {
//             performanceMetrics[i] = 0;
//         }
//
//         lastMetricLog = gettime();
//     }
//     return 1;
// }

// Hook untuk monitoring
hook OnPlayerConnect(playerid)
{
    playerTaskBalance[playerid] = 0;
    playerNameCacheTime[playerid] = 0;
    return 1;
}

hook OnPlayerDisconnect(playerid, reason)
{
    // Add to save queue before disconnect
    AddPlayerToSaveQueue(playerid);
    return 1;
}
