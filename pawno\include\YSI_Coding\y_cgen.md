# y_cgen

Provide spare space in *COD* for writing new instructions to.

## YSI

For general YSI information, see the following links:

* [Installation](../installation.md)
* [Troubleshooting](../troubleshooting.md)

## Documentation

* [Quick Start](y_cgen/quick-start.md) - One very simple example of getting started with this library.
* [Features](y_cgen/features.md) - More features and examples.
* [FAQs](y_cgen/faqs.md) - Frequently Asked Questions, including errors and solutions.
* [API](y_cgen/api.md) - Full list of all functions and their meaning.
* [Internal](y_cgen/internal.md) - Internal developer documentation for the system.

## External Links

These are links to external documentation and tutorials; both first- and third-party.  Note that these may be incomplete, obsolete, or otherwise inaccurate.

