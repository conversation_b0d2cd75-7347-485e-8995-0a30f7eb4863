# y_ebc

Entity Based Callbacks (EBCs) are tiny wrappers around inlines (and other callback pointers) to assign owners to them and subsequently kill them when not needed, i.e. when the owner is destroyed/disconnected.

## YSI

For general YSI information, see the following links:

* [Installation](../installation.md)
* [Troubleshooting](../troubleshooting.md)

## Documentation

* [Quick Start](y_ebc/quick-start.md) - One very simple example of getting started with this library.
* [Features](y_ebc/features.md) - More features and examples.
* [FAQs](y_ebc/faqs.md) - Frequently Asked Questions, including errors and solutions.
* [API](y_ebc/api.md) - Full list of all functions and their meaning.
* [Internal](y_ebc/internal.md) - Internal developer documentation for the system.

## External Links

These are links to external documentation and tutorials; both first- and third-party.  Note that these may be incomplete, obsolete, or otherwise inaccurate.

