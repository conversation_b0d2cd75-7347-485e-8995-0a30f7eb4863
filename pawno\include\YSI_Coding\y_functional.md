# y_functional

Provides support for "lambda" functions - small functions entirely contained within an expression such as a function call.  Also provides functions for operating on arrays and data at a high level.

## YSI

For general YSI information, see the following links:

* [Installation](../installation.md)
* [Troubleshooting](../troubleshooting.md)

## Documentation

* [Quick Start](y_functional/quick-start.md) - One very simple example of getting started with this library.
* [Features](y_functional/features.md) - More features and examples.
* [FAQs](y_functional/faqs.md) - Frequently Asked Questions, including errors and solutions.
* [API](y_functional/api.md) - Full list of all functions and their meaning.
* [Internal](y_functional/internal.md) - Internal developer documentation for the system.

## External Links

These are links to external documentation and tutorials; both first- and third-party.  Note that these may be incomplete, obsolete, or otherwise inaccurate.

