# y_hooks

Provides language syntax and support for "hooking" functions.  Allowing you to intercept them, or use the same callback in multiple files at once.

## YSI

For general YSI information, see the following links:

* [Installation](../installation.md)
* [Troubleshooting](../troubleshooting.md)

## Documentation

* [Quick Start](y_hooks/quick-start.md) - One very simple example of getting started with this library.
* [Features](y_hooks/features.md) - More features and examples.
* [FAQs](y_hooks/faqs.md) - Frequently Asked Questions, including errors and solutions.
* [API](y_hooks/api.md) - Full list of all functions and their meaning.
* [Internal](y_hooks/internal.md) - Internal developer documentation for the system.

## External Links

These are links to external documentation and tutorials; both first- and third-party.  Note that these may be incomplete, obsolete, or otherwise inaccurate.

