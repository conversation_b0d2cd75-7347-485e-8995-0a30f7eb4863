# y_inline

Provides "inline" functions - functions that can be written inside other functions, and can use the outer function's data as a "closure".

## YSI

For general YSI information, see the following links:

* [Installation](../installation.md)
* [Troubleshooting](../troubleshooting.md)

## Documentation

* [Quick Start](y_inline/quick-start.md) - One very simple example of getting started with this library.
* [Features](y_inline/features.md) - More features and examples.
* [FAQs](y_inline/faqs.md) - Frequently Asked Questions, including errors and solutions.
* [API](y_inline/api.md) - Full list of all functions and their meaning.
* [Internal](y_inline/internal.md) - Internal developer documentation for the system.

## External Links

These are links to external documentation and tutorials; both first- and third-party.  Note that these may be incomplete, obsolete, or otherwise inaccurate.

