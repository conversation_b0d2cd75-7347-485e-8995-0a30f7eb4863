# y_remote

Wraps `CallRemoteFunction` (and `CallLocalFunction`) to give compile-time parameter type checks.

## YSI

For general YSI information, see the following links:

* [Installation](../installation.md)
* [Troubleshooting](../troubleshooting.md)

## Documentation

* [Quick Start](y_remote/quick-start.md) - One very simple example of getting started with this library.
* [Features](y_remote/features.md) - More features and examples.
* [FAQs](y_remote/faqs.md) - Frequently Asked Questions, including errors and solutions.
* [API](y_remote/api.md) - Full list of all functions and their meaning.
* [Internal](y_remote/internal.md) - Internal developer documentation for the system.

## External Links

These are links to external documentation and tutorials; both first- and third-party.  Note that these may be incomplete, obsolete, or otherwise inaccurate.

