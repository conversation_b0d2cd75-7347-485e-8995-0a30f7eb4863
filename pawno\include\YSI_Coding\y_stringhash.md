# y_stringhash

Performs compile-time hashing of strings, for use as array indexes, `case` values, and more.

## YSI

For general YSI information, see the following links:

* [Installation](../installation.md)
* [Troubleshooting](../troubleshooting.md)

## Documentation

* [Quick Start](y_stringhash/quick-start.md) - One very simple example of getting started with this library.
* [Features](y_stringhash/features.md) - More features and examples.
* [FAQs](y_stringhash/faqs.md) - Frequently Asked Questions, including errors and solutions.
* [API](y_stringhash/api.md) - Full list of all functions and their meaning.
* [Internal](y_stringhash/internal.md) - Internal developer documentation for the system.

## External Links

These are links to external documentation and tutorials; both first- and third-party.  Note that these may be incomplete, obsolete, or otherwise inaccurate.

