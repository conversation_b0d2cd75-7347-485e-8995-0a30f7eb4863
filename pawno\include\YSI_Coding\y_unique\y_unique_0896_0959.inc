static stock const Y_UNIQUE_0896_0959_CALLED = UNIQUE_SYMBOL;

#if defined _inc_y_unique_0896_0959
	#undef _inc_y_unique_0896_0959
#endif

#if UNIQUE_SYMBOL < (903)
	#if UNIQUE_SYMBOL == (895)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (896)
		#define UNIQUE_FUNCTION<%0...%1> %0D0%1
		#endinput
	#elseif UNIQUE_SYMBOL == (896)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (897)
		#define UNIQUE_FUNCTION<%0...%1> %0D1%1
		#endinput
	#elseif UNIQUE_SYMBOL == (897)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (898)
		#define UNIQUE_FUNCTION<%0...%1> %0D2%1
		#endinput
	#elseif UNIQUE_SYMBOL == (898)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (899)
		#define UNIQUE_FUNCTION<%0...%1> %0D3%1
		#endinput
	#elseif UNIQUE_SYMBOL == (899)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (900)
		#define UNIQUE_FUNCTION<%0...%1> %0D4%1
		#endinput
	#elseif UNIQUE_SYMBOL == (900)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (901)
		#define UNIQUE_FUNCTION<%0...%1> %0D5%1
		#endinput
	#elseif UNIQUE_SYMBOL == (901)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (902)
		#define UNIQUE_FUNCTION<%0...%1> %0D6%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (903)
		#define UNIQUE_FUNCTION<%0...%1> %0D7%1
		#endinput
	#endif
#elseif UNIQUE_SYMBOL < (911)
	#if UNIQUE_SYMBOL == (903)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (904)
		#define UNIQUE_FUNCTION<%0...%1> %0D8%1
		#endinput
	#elseif UNIQUE_SYMBOL == (904)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (905)
		#define UNIQUE_FUNCTION<%0...%1> %0D9%1
		#endinput
	#elseif UNIQUE_SYMBOL == (905)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (906)
		#define UNIQUE_FUNCTION<%0...%1> %0D@%1
		#endinput
	#elseif UNIQUE_SYMBOL == (906)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (907)
		#define UNIQUE_FUNCTION<%0...%1> %0DA%1
		#endinput
	#elseif UNIQUE_SYMBOL == (907)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (908)
		#define UNIQUE_FUNCTION<%0...%1> %0DB%1
		#endinput
	#elseif UNIQUE_SYMBOL == (908)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (909)
		#define UNIQUE_FUNCTION<%0...%1> %0DC%1
		#endinput
	#elseif UNIQUE_SYMBOL == (909)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (910)
		#define UNIQUE_FUNCTION<%0...%1> %0DD%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (911)
		#define UNIQUE_FUNCTION<%0...%1> %0DE%1
		#endinput
	#endif
#elseif UNIQUE_SYMBOL < (919)
	#if UNIQUE_SYMBOL == (911)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (912)
		#define UNIQUE_FUNCTION<%0...%1> %0DF%1
		#endinput
	#elseif UNIQUE_SYMBOL == (912)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (913)
		#define UNIQUE_FUNCTION<%0...%1> %0DG%1
		#endinput
	#elseif UNIQUE_SYMBOL == (913)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (914)
		#define UNIQUE_FUNCTION<%0...%1> %0DH%1
		#endinput
	#elseif UNIQUE_SYMBOL == (914)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (915)
		#define UNIQUE_FUNCTION<%0...%1> %0DI%1
		#endinput
	#elseif UNIQUE_SYMBOL == (915)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (916)
		#define UNIQUE_FUNCTION<%0...%1> %0DJ%1
		#endinput
	#elseif UNIQUE_SYMBOL == (916)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (917)
		#define UNIQUE_FUNCTION<%0...%1> %0DK%1
		#endinput
	#elseif UNIQUE_SYMBOL == (917)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (918)
		#define UNIQUE_FUNCTION<%0...%1> %0DL%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (919)
		#define UNIQUE_FUNCTION<%0...%1> %0DM%1
		#endinput
	#endif
#elseif UNIQUE_SYMBOL < (927)
	#if UNIQUE_SYMBOL == (919)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (920)
		#define UNIQUE_FUNCTION<%0...%1> %0DN%1
		#endinput
	#elseif UNIQUE_SYMBOL == (920)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (921)
		#define UNIQUE_FUNCTION<%0...%1> %0DO%1
		#endinput
	#elseif UNIQUE_SYMBOL == (921)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (922)
		#define UNIQUE_FUNCTION<%0...%1> %0DP%1
		#endinput
	#elseif UNIQUE_SYMBOL == (922)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (923)
		#define UNIQUE_FUNCTION<%0...%1> %0DQ%1
		#endinput
	#elseif UNIQUE_SYMBOL == (923)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (924)
		#define UNIQUE_FUNCTION<%0...%1> %0DR%1
		#endinput
	#elseif UNIQUE_SYMBOL == (924)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (925)
		#define UNIQUE_FUNCTION<%0...%1> %0DS%1
		#endinput
	#elseif UNIQUE_SYMBOL == (925)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (926)
		#define UNIQUE_FUNCTION<%0...%1> %0DT%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (927)
		#define UNIQUE_FUNCTION<%0...%1> %0DU%1
		#endinput
	#endif
#elseif UNIQUE_SYMBOL < (935)
	#if UNIQUE_SYMBOL == (927)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (928)
		#define UNIQUE_FUNCTION<%0...%1> %0DV%1
		#endinput
	#elseif UNIQUE_SYMBOL == (928)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (929)
		#define UNIQUE_FUNCTION<%0...%1> %0DW%1
		#endinput
	#elseif UNIQUE_SYMBOL == (929)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (930)
		#define UNIQUE_FUNCTION<%0...%1> %0DX%1
		#endinput
	#elseif UNIQUE_SYMBOL == (930)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (931)
		#define UNIQUE_FUNCTION<%0...%1> %0DY%1
		#endinput
	#elseif UNIQUE_SYMBOL == (931)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (932)
		#define UNIQUE_FUNCTION<%0...%1> %0DZ%1
		#endinput
	#elseif UNIQUE_SYMBOL == (932)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (933)
		#define UNIQUE_FUNCTION<%0...%1> %0D_%1
		#endinput
	#elseif UNIQUE_SYMBOL == (933)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (934)
		#define UNIQUE_FUNCTION<%0...%1> %0Da%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (935)
		#define UNIQUE_FUNCTION<%0...%1> %0Db%1
		#endinput
	#endif
#elseif UNIQUE_SYMBOL < (943)
	#if UNIQUE_SYMBOL == (935)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (936)
		#define UNIQUE_FUNCTION<%0...%1> %0Dc%1
		#endinput
	#elseif UNIQUE_SYMBOL == (936)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (937)
		#define UNIQUE_FUNCTION<%0...%1> %0Dd%1
		#endinput
	#elseif UNIQUE_SYMBOL == (937)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (938)
		#define UNIQUE_FUNCTION<%0...%1> %0De%1
		#endinput
	#elseif UNIQUE_SYMBOL == (938)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (939)
		#define UNIQUE_FUNCTION<%0...%1> %0Df%1
		#endinput
	#elseif UNIQUE_SYMBOL == (939)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (940)
		#define UNIQUE_FUNCTION<%0...%1> %0Dg%1
		#endinput
	#elseif UNIQUE_SYMBOL == (940)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (941)
		#define UNIQUE_FUNCTION<%0...%1> %0Dh%1
		#endinput
	#elseif UNIQUE_SYMBOL == (941)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (942)
		#define UNIQUE_FUNCTION<%0...%1> %0Di%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (943)
		#define UNIQUE_FUNCTION<%0...%1> %0Dj%1
		#endinput
	#endif
#elseif UNIQUE_SYMBOL < (951)
	#if UNIQUE_SYMBOL == (943)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (944)
		#define UNIQUE_FUNCTION<%0...%1> %0Dk%1
		#endinput
	#elseif UNIQUE_SYMBOL == (944)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (945)
		#define UNIQUE_FUNCTION<%0...%1> %0Dl%1
		#endinput
	#elseif UNIQUE_SYMBOL == (945)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (946)
		#define UNIQUE_FUNCTION<%0...%1> %0Dm%1
		#endinput
	#elseif UNIQUE_SYMBOL == (946)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (947)
		#define UNIQUE_FUNCTION<%0...%1> %0Dn%1
		#endinput
	#elseif UNIQUE_SYMBOL == (947)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (948)
		#define UNIQUE_FUNCTION<%0...%1> %0Do%1
		#endinput
	#elseif UNIQUE_SYMBOL == (948)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (949)
		#define UNIQUE_FUNCTION<%0...%1> %0Dp%1
		#endinput
	#elseif UNIQUE_SYMBOL == (949)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (950)
		#define UNIQUE_FUNCTION<%0...%1> %0Dq%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (951)
		#define UNIQUE_FUNCTION<%0...%1> %0Dr%1
		#endinput
	#endif
#else
	#if UNIQUE_SYMBOL == (951)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (952)
		#define UNIQUE_FUNCTION<%0...%1> %0Ds%1
		#endinput
	#elseif UNIQUE_SYMBOL == (952)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (953)
		#define UNIQUE_FUNCTION<%0...%1> %0Dt%1
		#endinput
	#elseif UNIQUE_SYMBOL == (953)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (954)
		#define UNIQUE_FUNCTION<%0...%1> %0Du%1
		#endinput
	#elseif UNIQUE_SYMBOL == (954)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (955)
		#define UNIQUE_FUNCTION<%0...%1> %0Dv%1
		#endinput
	#elseif UNIQUE_SYMBOL == (955)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (956)
		#define UNIQUE_FUNCTION<%0...%1> %0Dw%1
		#endinput
	#elseif UNIQUE_SYMBOL == (956)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (957)
		#define UNIQUE_FUNCTION<%0...%1> %0Dx%1
		#endinput
	#elseif UNIQUE_SYMBOL == (957)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (958)
		#define UNIQUE_FUNCTION<%0...%1> %0Dy%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (959)
		#define UNIQUE_FUNCTION<%0...%1> %0Dz%1
		#endinput
	#endif
#endif

