static stock const Y_UNIQUE_0960_1023_CALLED = UNIQUE_SYMBOL;

#if defined _inc_y_unique_0960_1023
	#undef _inc_y_unique_0960_1023
#endif

#if UNIQUE_SYMBOL < (967)
	#if UNIQUE_SYMBOL == (959)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (960)
		#define UNIQUE_FUNCTION<%0...%1> %0E0%1
		#endinput
	#elseif UNIQUE_SYMBOL == (960)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (961)
		#define UNIQUE_FUNCTION<%0...%1> %0E1%1
		#endinput
	#elseif UNIQUE_SYMBOL == (961)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (962)
		#define UNIQUE_FUNCTION<%0...%1> %0E2%1
		#endinput
	#elseif UNIQUE_SYMBOL == (962)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (963)
		#define UNIQUE_FUNCTION<%0...%1> %0E3%1
		#endinput
	#elseif UNIQUE_SYMBOL == (963)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (964)
		#define UNIQUE_FUNCTION<%0...%1> %0E4%1
		#endinput
	#elseif UNIQUE_SYMBOL == (964)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (965)
		#define UNIQUE_FUNCTION<%0...%1> %0E5%1
		#endinput
	#elseif UNIQUE_SYMBOL == (965)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (966)
		#define UNIQUE_FUNCTION<%0...%1> %0E6%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (967)
		#define UNIQUE_FUNCTION<%0...%1> %0E7%1
		#endinput
	#endif
#elseif UNIQUE_SYMBOL < (975)
	#if UNIQUE_SYMBOL == (967)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (968)
		#define UNIQUE_FUNCTION<%0...%1> %0E8%1
		#endinput
	#elseif UNIQUE_SYMBOL == (968)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (969)
		#define UNIQUE_FUNCTION<%0...%1> %0E9%1
		#endinput
	#elseif UNIQUE_SYMBOL == (969)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (970)
		#define UNIQUE_FUNCTION<%0...%1> %0E@%1
		#endinput
	#elseif UNIQUE_SYMBOL == (970)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (971)
		#define UNIQUE_FUNCTION<%0...%1> %0EA%1
		#endinput
	#elseif UNIQUE_SYMBOL == (971)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (972)
		#define UNIQUE_FUNCTION<%0...%1> %0EB%1
		#endinput
	#elseif UNIQUE_SYMBOL == (972)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (973)
		#define UNIQUE_FUNCTION<%0...%1> %0EC%1
		#endinput
	#elseif UNIQUE_SYMBOL == (973)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (974)
		#define UNIQUE_FUNCTION<%0...%1> %0ED%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (975)
		#define UNIQUE_FUNCTION<%0...%1> %0EE%1
		#endinput
	#endif
#elseif UNIQUE_SYMBOL < (983)
	#if UNIQUE_SYMBOL == (975)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (976)
		#define UNIQUE_FUNCTION<%0...%1> %0EF%1
		#endinput
	#elseif UNIQUE_SYMBOL == (976)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (977)
		#define UNIQUE_FUNCTION<%0...%1> %0EG%1
		#endinput
	#elseif UNIQUE_SYMBOL == (977)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (978)
		#define UNIQUE_FUNCTION<%0...%1> %0EH%1
		#endinput
	#elseif UNIQUE_SYMBOL == (978)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (979)
		#define UNIQUE_FUNCTION<%0...%1> %0EI%1
		#endinput
	#elseif UNIQUE_SYMBOL == (979)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (980)
		#define UNIQUE_FUNCTION<%0...%1> %0EJ%1
		#endinput
	#elseif UNIQUE_SYMBOL == (980)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (981)
		#define UNIQUE_FUNCTION<%0...%1> %0EK%1
		#endinput
	#elseif UNIQUE_SYMBOL == (981)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (982)
		#define UNIQUE_FUNCTION<%0...%1> %0EL%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (983)
		#define UNIQUE_FUNCTION<%0...%1> %0EM%1
		#endinput
	#endif
#elseif UNIQUE_SYMBOL < (991)
	#if UNIQUE_SYMBOL == (983)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (984)
		#define UNIQUE_FUNCTION<%0...%1> %0EN%1
		#endinput
	#elseif UNIQUE_SYMBOL == (984)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (985)
		#define UNIQUE_FUNCTION<%0...%1> %0EO%1
		#endinput
	#elseif UNIQUE_SYMBOL == (985)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (986)
		#define UNIQUE_FUNCTION<%0...%1> %0EP%1
		#endinput
	#elseif UNIQUE_SYMBOL == (986)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (987)
		#define UNIQUE_FUNCTION<%0...%1> %0EQ%1
		#endinput
	#elseif UNIQUE_SYMBOL == (987)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (988)
		#define UNIQUE_FUNCTION<%0...%1> %0ER%1
		#endinput
	#elseif UNIQUE_SYMBOL == (988)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (989)
		#define UNIQUE_FUNCTION<%0...%1> %0ES%1
		#endinput
	#elseif UNIQUE_SYMBOL == (989)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (990)
		#define UNIQUE_FUNCTION<%0...%1> %0ET%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (991)
		#define UNIQUE_FUNCTION<%0...%1> %0EU%1
		#endinput
	#endif
#elseif UNIQUE_SYMBOL < (999)
	#if UNIQUE_SYMBOL == (991)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (992)
		#define UNIQUE_FUNCTION<%0...%1> %0EV%1
		#endinput
	#elseif UNIQUE_SYMBOL == (992)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (993)
		#define UNIQUE_FUNCTION<%0...%1> %0EW%1
		#endinput
	#elseif UNIQUE_SYMBOL == (993)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (994)
		#define UNIQUE_FUNCTION<%0...%1> %0EX%1
		#endinput
	#elseif UNIQUE_SYMBOL == (994)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (995)
		#define UNIQUE_FUNCTION<%0...%1> %0EY%1
		#endinput
	#elseif UNIQUE_SYMBOL == (995)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (996)
		#define UNIQUE_FUNCTION<%0...%1> %0EZ%1
		#endinput
	#elseif UNIQUE_SYMBOL == (996)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (997)
		#define UNIQUE_FUNCTION<%0...%1> %0E_%1
		#endinput
	#elseif UNIQUE_SYMBOL == (997)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (998)
		#define UNIQUE_FUNCTION<%0...%1> %0Ea%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (999)
		#define UNIQUE_FUNCTION<%0...%1> %0Eb%1
		#endinput
	#endif
#elseif UNIQUE_SYMBOL < (1007)
	#if UNIQUE_SYMBOL == (999)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1000)
		#define UNIQUE_FUNCTION<%0...%1> %0Ec%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1000)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1001)
		#define UNIQUE_FUNCTION<%0...%1> %0Ed%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1001)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1002)
		#define UNIQUE_FUNCTION<%0...%1> %0Ee%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1002)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1003)
		#define UNIQUE_FUNCTION<%0...%1> %0Ef%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1003)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1004)
		#define UNIQUE_FUNCTION<%0...%1> %0Eg%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1004)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1005)
		#define UNIQUE_FUNCTION<%0...%1> %0Eh%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1005)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1006)
		#define UNIQUE_FUNCTION<%0...%1> %0Ei%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1007)
		#define UNIQUE_FUNCTION<%0...%1> %0Ej%1
		#endinput
	#endif
#elseif UNIQUE_SYMBOL < (1015)
	#if UNIQUE_SYMBOL == (1007)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1008)
		#define UNIQUE_FUNCTION<%0...%1> %0Ek%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1008)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1009)
		#define UNIQUE_FUNCTION<%0...%1> %0El%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1009)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1010)
		#define UNIQUE_FUNCTION<%0...%1> %0Em%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1010)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1011)
		#define UNIQUE_FUNCTION<%0...%1> %0En%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1011)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1012)
		#define UNIQUE_FUNCTION<%0...%1> %0Eo%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1012)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1013)
		#define UNIQUE_FUNCTION<%0...%1> %0Ep%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1013)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1014)
		#define UNIQUE_FUNCTION<%0...%1> %0Eq%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1015)
		#define UNIQUE_FUNCTION<%0...%1> %0Er%1
		#endinput
	#endif
#else
	#if UNIQUE_SYMBOL == (1015)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1016)
		#define UNIQUE_FUNCTION<%0...%1> %0Es%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1016)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1017)
		#define UNIQUE_FUNCTION<%0...%1> %0Et%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1017)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1018)
		#define UNIQUE_FUNCTION<%0...%1> %0Eu%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1018)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1019)
		#define UNIQUE_FUNCTION<%0...%1> %0Ev%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1019)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1020)
		#define UNIQUE_FUNCTION<%0...%1> %0Ew%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1020)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1021)
		#define UNIQUE_FUNCTION<%0...%1> %0Ex%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1021)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1022)
		#define UNIQUE_FUNCTION<%0...%1> %0Ey%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1023)
		#define UNIQUE_FUNCTION<%0...%1> %0Ez%1
		#endinput
	#endif
#endif

