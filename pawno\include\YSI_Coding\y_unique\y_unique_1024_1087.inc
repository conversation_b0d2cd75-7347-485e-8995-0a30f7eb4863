static stock const Y_UNIQUE_1024_1087_CALLED = UNIQUE_SYMBOL;

#if defined _inc_y_unique_1024_1087
	#undef _inc_y_unique_1024_1087
#endif

#if UNIQUE_SYMBOL < (1031)
	#if UNIQUE_SYMBOL == (1023)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1024)
		#define UNIQUE_FUNCTION<%0...%1> %0F0%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1024)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1025)
		#define UNIQUE_FUNCTION<%0...%1> %0F1%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1025)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1026)
		#define UNIQUE_FUNCTION<%0...%1> %0F2%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1026)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1027)
		#define UNIQUE_FUNCTION<%0...%1> %0F3%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1027)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1028)
		#define UNIQUE_FUNCTION<%0...%1> %0F4%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1028)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1029)
		#define UNIQUE_FUNCTION<%0...%1> %0F5%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1029)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1030)
		#define UNIQUE_FUNCTION<%0...%1> %0F6%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1031)
		#define UNIQUE_FUNCTION<%0...%1> %0F7%1
		#endinput
	#endif
#elseif UNIQUE_SYMBOL < (1039)
	#if UNIQUE_SYMBOL == (1031)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1032)
		#define UNIQUE_FUNCTION<%0...%1> %0F8%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1032)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1033)
		#define UNIQUE_FUNCTION<%0...%1> %0F9%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1033)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1034)
		#define UNIQUE_FUNCTION<%0...%1> %0F@%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1034)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1035)
		#define UNIQUE_FUNCTION<%0...%1> %0FA%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1035)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1036)
		#define UNIQUE_FUNCTION<%0...%1> %0FB%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1036)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1037)
		#define UNIQUE_FUNCTION<%0...%1> %0FC%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1037)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1038)
		#define UNIQUE_FUNCTION<%0...%1> %0FD%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1039)
		#define UNIQUE_FUNCTION<%0...%1> %0FE%1
		#endinput
	#endif
#elseif UNIQUE_SYMBOL < (1047)
	#if UNIQUE_SYMBOL == (1039)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1040)
		#define UNIQUE_FUNCTION<%0...%1> %0FF%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1040)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1041)
		#define UNIQUE_FUNCTION<%0...%1> %0FG%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1041)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1042)
		#define UNIQUE_FUNCTION<%0...%1> %0FH%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1042)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1043)
		#define UNIQUE_FUNCTION<%0...%1> %0FI%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1043)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1044)
		#define UNIQUE_FUNCTION<%0...%1> %0FJ%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1044)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1045)
		#define UNIQUE_FUNCTION<%0...%1> %0FK%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1045)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1046)
		#define UNIQUE_FUNCTION<%0...%1> %0FL%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1047)
		#define UNIQUE_FUNCTION<%0...%1> %0FM%1
		#endinput
	#endif
#elseif UNIQUE_SYMBOL < (1055)
	#if UNIQUE_SYMBOL == (1047)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1048)
		#define UNIQUE_FUNCTION<%0...%1> %0FN%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1048)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1049)
		#define UNIQUE_FUNCTION<%0...%1> %0FO%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1049)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1050)
		#define UNIQUE_FUNCTION<%0...%1> %0FP%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1050)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1051)
		#define UNIQUE_FUNCTION<%0...%1> %0FQ%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1051)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1052)
		#define UNIQUE_FUNCTION<%0...%1> %0FR%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1052)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1053)
		#define UNIQUE_FUNCTION<%0...%1> %0FS%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1053)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1054)
		#define UNIQUE_FUNCTION<%0...%1> %0FT%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1055)
		#define UNIQUE_FUNCTION<%0...%1> %0FU%1
		#endinput
	#endif
#elseif UNIQUE_SYMBOL < (1063)
	#if UNIQUE_SYMBOL == (1055)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1056)
		#define UNIQUE_FUNCTION<%0...%1> %0FV%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1056)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1057)
		#define UNIQUE_FUNCTION<%0...%1> %0FW%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1057)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1058)
		#define UNIQUE_FUNCTION<%0...%1> %0FX%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1058)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1059)
		#define UNIQUE_FUNCTION<%0...%1> %0FY%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1059)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1060)
		#define UNIQUE_FUNCTION<%0...%1> %0FZ%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1060)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1061)
		#define UNIQUE_FUNCTION<%0...%1> %0F_%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1061)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1062)
		#define UNIQUE_FUNCTION<%0...%1> %0Fa%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1063)
		#define UNIQUE_FUNCTION<%0...%1> %0Fb%1
		#endinput
	#endif
#elseif UNIQUE_SYMBOL < (1071)
	#if UNIQUE_SYMBOL == (1063)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1064)
		#define UNIQUE_FUNCTION<%0...%1> %0Fc%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1064)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1065)
		#define UNIQUE_FUNCTION<%0...%1> %0Fd%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1065)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1066)
		#define UNIQUE_FUNCTION<%0...%1> %0Fe%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1066)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1067)
		#define UNIQUE_FUNCTION<%0...%1> %0Ff%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1067)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1068)
		#define UNIQUE_FUNCTION<%0...%1> %0Fg%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1068)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1069)
		#define UNIQUE_FUNCTION<%0...%1> %0Fh%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1069)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1070)
		#define UNIQUE_FUNCTION<%0...%1> %0Fi%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1071)
		#define UNIQUE_FUNCTION<%0...%1> %0Fj%1
		#endinput
	#endif
#elseif UNIQUE_SYMBOL < (1079)
	#if UNIQUE_SYMBOL == (1071)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1072)
		#define UNIQUE_FUNCTION<%0...%1> %0Fk%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1072)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1073)
		#define UNIQUE_FUNCTION<%0...%1> %0Fl%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1073)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1074)
		#define UNIQUE_FUNCTION<%0...%1> %0Fm%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1074)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1075)
		#define UNIQUE_FUNCTION<%0...%1> %0Fn%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1075)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1076)
		#define UNIQUE_FUNCTION<%0...%1> %0Fo%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1076)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1077)
		#define UNIQUE_FUNCTION<%0...%1> %0Fp%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1077)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1078)
		#define UNIQUE_FUNCTION<%0...%1> %0Fq%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1079)
		#define UNIQUE_FUNCTION<%0...%1> %0Fr%1
		#endinput
	#endif
#else
	#if UNIQUE_SYMBOL == (1079)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1080)
		#define UNIQUE_FUNCTION<%0...%1> %0Fs%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1080)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1081)
		#define UNIQUE_FUNCTION<%0...%1> %0Ft%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1081)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1082)
		#define UNIQUE_FUNCTION<%0...%1> %0Fu%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1082)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1083)
		#define UNIQUE_FUNCTION<%0...%1> %0Fv%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1083)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1084)
		#define UNIQUE_FUNCTION<%0...%1> %0Fw%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1084)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1085)
		#define UNIQUE_FUNCTION<%0...%1> %0Fx%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1085)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1086)
		#define UNIQUE_FUNCTION<%0...%1> %0Fy%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1087)
		#define UNIQUE_FUNCTION<%0...%1> %0Fz%1
		#endinput
	#endif
#endif

