static stock const Y_UNIQUE_1088_1151_CALLED = UNIQUE_SYMBOL;

#if defined _inc_y_unique_1088_1151
	#undef _inc_y_unique_1088_1151
#endif

#if UNIQUE_SYMBOL < (1095)
	#if UNIQUE_SYMBOL == (1087)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1088)
		#define UNIQUE_FUNCTION<%0...%1> %0G0%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1088)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1089)
		#define UNIQUE_FUNCTION<%0...%1> %0G1%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1089)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1090)
		#define UNIQUE_FUNCTION<%0...%1> %0G2%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1090)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1091)
		#define UNIQUE_FUNCTION<%0...%1> %0G3%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1091)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1092)
		#define UNIQUE_FUNCTION<%0...%1> %0G4%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1092)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1093)
		#define UNIQUE_FUNCTION<%0...%1> %0G5%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1093)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1094)
		#define UNIQUE_FUNCTION<%0...%1> %0G6%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1095)
		#define UNIQUE_FUNCTION<%0...%1> %0G7%1
		#endinput
	#endif
#elseif UNIQUE_SYMBOL < (1103)
	#if UNIQUE_SYMBOL == (1095)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1096)
		#define UNIQUE_FUNCTION<%0...%1> %0G8%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1096)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1097)
		#define UNIQUE_FUNCTION<%0...%1> %0G9%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1097)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1098)
		#define UNIQUE_FUNCTION<%0...%1> %0G@%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1098)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1099)
		#define UNIQUE_FUNCTION<%0...%1> %0GA%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1099)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1100)
		#define UNIQUE_FUNCTION<%0...%1> %0GB%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1100)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1101)
		#define UNIQUE_FUNCTION<%0...%1> %0GC%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1101)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1102)
		#define UNIQUE_FUNCTION<%0...%1> %0GD%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1103)
		#define UNIQUE_FUNCTION<%0...%1> %0GE%1
		#endinput
	#endif
#elseif UNIQUE_SYMBOL < (1111)
	#if UNIQUE_SYMBOL == (1103)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1104)
		#define UNIQUE_FUNCTION<%0...%1> %0GF%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1104)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1105)
		#define UNIQUE_FUNCTION<%0...%1> %0GG%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1105)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1106)
		#define UNIQUE_FUNCTION<%0...%1> %0GH%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1106)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1107)
		#define UNIQUE_FUNCTION<%0...%1> %0GI%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1107)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1108)
		#define UNIQUE_FUNCTION<%0...%1> %0GJ%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1108)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1109)
		#define UNIQUE_FUNCTION<%0...%1> %0GK%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1109)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1110)
		#define UNIQUE_FUNCTION<%0...%1> %0GL%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1111)
		#define UNIQUE_FUNCTION<%0...%1> %0GM%1
		#endinput
	#endif
#elseif UNIQUE_SYMBOL < (1119)
	#if UNIQUE_SYMBOL == (1111)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1112)
		#define UNIQUE_FUNCTION<%0...%1> %0GN%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1112)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1113)
		#define UNIQUE_FUNCTION<%0...%1> %0GO%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1113)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1114)
		#define UNIQUE_FUNCTION<%0...%1> %0GP%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1114)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1115)
		#define UNIQUE_FUNCTION<%0...%1> %0GQ%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1115)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1116)
		#define UNIQUE_FUNCTION<%0...%1> %0GR%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1116)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1117)
		#define UNIQUE_FUNCTION<%0...%1> %0GS%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1117)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1118)
		#define UNIQUE_FUNCTION<%0...%1> %0GT%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1119)
		#define UNIQUE_FUNCTION<%0...%1> %0GU%1
		#endinput
	#endif
#elseif UNIQUE_SYMBOL < (1127)
	#if UNIQUE_SYMBOL == (1119)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1120)
		#define UNIQUE_FUNCTION<%0...%1> %0GV%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1120)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1121)
		#define UNIQUE_FUNCTION<%0...%1> %0GW%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1121)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1122)
		#define UNIQUE_FUNCTION<%0...%1> %0GX%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1122)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1123)
		#define UNIQUE_FUNCTION<%0...%1> %0GY%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1123)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1124)
		#define UNIQUE_FUNCTION<%0...%1> %0GZ%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1124)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1125)
		#define UNIQUE_FUNCTION<%0...%1> %0G_%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1125)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1126)
		#define UNIQUE_FUNCTION<%0...%1> %0Ga%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1127)
		#define UNIQUE_FUNCTION<%0...%1> %0Gb%1
		#endinput
	#endif
#elseif UNIQUE_SYMBOL < (1135)
	#if UNIQUE_SYMBOL == (1127)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1128)
		#define UNIQUE_FUNCTION<%0...%1> %0Gc%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1128)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1129)
		#define UNIQUE_FUNCTION<%0...%1> %0Gd%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1129)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1130)
		#define UNIQUE_FUNCTION<%0...%1> %0Ge%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1130)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1131)
		#define UNIQUE_FUNCTION<%0...%1> %0Gf%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1131)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1132)
		#define UNIQUE_FUNCTION<%0...%1> %0Gg%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1132)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1133)
		#define UNIQUE_FUNCTION<%0...%1> %0Gh%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1133)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1134)
		#define UNIQUE_FUNCTION<%0...%1> %0Gi%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1135)
		#define UNIQUE_FUNCTION<%0...%1> %0Gj%1
		#endinput
	#endif
#elseif UNIQUE_SYMBOL < (1143)
	#if UNIQUE_SYMBOL == (1135)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1136)
		#define UNIQUE_FUNCTION<%0...%1> %0Gk%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1136)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1137)
		#define UNIQUE_FUNCTION<%0...%1> %0Gl%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1137)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1138)
		#define UNIQUE_FUNCTION<%0...%1> %0Gm%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1138)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1139)
		#define UNIQUE_FUNCTION<%0...%1> %0Gn%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1139)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1140)
		#define UNIQUE_FUNCTION<%0...%1> %0Go%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1140)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1141)
		#define UNIQUE_FUNCTION<%0...%1> %0Gp%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1141)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1142)
		#define UNIQUE_FUNCTION<%0...%1> %0Gq%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1143)
		#define UNIQUE_FUNCTION<%0...%1> %0Gr%1
		#endinput
	#endif
#else
	#if UNIQUE_SYMBOL == (1143)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1144)
		#define UNIQUE_FUNCTION<%0...%1> %0Gs%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1144)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1145)
		#define UNIQUE_FUNCTION<%0...%1> %0Gt%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1145)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1146)
		#define UNIQUE_FUNCTION<%0...%1> %0Gu%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1146)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1147)
		#define UNIQUE_FUNCTION<%0...%1> %0Gv%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1147)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1148)
		#define UNIQUE_FUNCTION<%0...%1> %0Gw%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1148)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1149)
		#define UNIQUE_FUNCTION<%0...%1> %0Gx%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1149)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1150)
		#define UNIQUE_FUNCTION<%0...%1> %0Gy%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1151)
		#define UNIQUE_FUNCTION<%0...%1> %0Gz%1
		#endinput
	#endif
#endif

