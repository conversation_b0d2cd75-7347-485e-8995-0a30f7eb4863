static stock const Y_UNIQUE_1152_1215_CALLED = UNIQUE_SYMBOL;

#if defined _inc_y_unique_1152_1215
	#undef _inc_y_unique_1152_1215
#endif

#if UNIQUE_SYMBOL < (1159)
	#if UNIQUE_SYMBOL == (1151)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1152)
		#define UNIQUE_FUNCTION<%0...%1> %0H0%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1152)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1153)
		#define UNIQUE_FUNCTION<%0...%1> %0H1%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1153)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1154)
		#define UNIQUE_FUNCTION<%0...%1> %0H2%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1154)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1155)
		#define UNIQUE_FUNCTION<%0...%1> %0H3%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1155)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1156)
		#define UNIQUE_FUNCTION<%0...%1> %0H4%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1156)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1157)
		#define UNIQUE_FUNCTION<%0...%1> %0H5%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1157)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1158)
		#define UNIQUE_FUNCTION<%0...%1> %0H6%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1159)
		#define UNIQUE_FUNCTION<%0...%1> %0H7%1
		#endinput
	#endif
#elseif UNIQUE_SYMBOL < (1167)
	#if UNIQUE_SYMBOL == (1159)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1160)
		#define UNIQUE_FUNCTION<%0...%1> %0H8%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1160)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1161)
		#define UNIQUE_FUNCTION<%0...%1> %0H9%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1161)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1162)
		#define UNIQUE_FUNCTION<%0...%1> %0H@%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1162)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1163)
		#define UNIQUE_FUNCTION<%0...%1> %0HA%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1163)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1164)
		#define UNIQUE_FUNCTION<%0...%1> %0HB%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1164)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1165)
		#define UNIQUE_FUNCTION<%0...%1> %0HC%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1165)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1166)
		#define UNIQUE_FUNCTION<%0...%1> %0HD%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1167)
		#define UNIQUE_FUNCTION<%0...%1> %0HE%1
		#endinput
	#endif
#elseif UNIQUE_SYMBOL < (1175)
	#if UNIQUE_SYMBOL == (1167)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1168)
		#define UNIQUE_FUNCTION<%0...%1> %0HF%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1168)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1169)
		#define UNIQUE_FUNCTION<%0...%1> %0HG%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1169)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1170)
		#define UNIQUE_FUNCTION<%0...%1> %0HH%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1170)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1171)
		#define UNIQUE_FUNCTION<%0...%1> %0HI%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1171)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1172)
		#define UNIQUE_FUNCTION<%0...%1> %0HJ%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1172)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1173)
		#define UNIQUE_FUNCTION<%0...%1> %0HK%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1173)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1174)
		#define UNIQUE_FUNCTION<%0...%1> %0HL%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1175)
		#define UNIQUE_FUNCTION<%0...%1> %0HM%1
		#endinput
	#endif
#elseif UNIQUE_SYMBOL < (1183)
	#if UNIQUE_SYMBOL == (1175)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1176)
		#define UNIQUE_FUNCTION<%0...%1> %0HN%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1176)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1177)
		#define UNIQUE_FUNCTION<%0...%1> %0HO%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1177)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1178)
		#define UNIQUE_FUNCTION<%0...%1> %0HP%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1178)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1179)
		#define UNIQUE_FUNCTION<%0...%1> %0HQ%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1179)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1180)
		#define UNIQUE_FUNCTION<%0...%1> %0HR%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1180)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1181)
		#define UNIQUE_FUNCTION<%0...%1> %0HS%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1181)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1182)
		#define UNIQUE_FUNCTION<%0...%1> %0HT%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1183)
		#define UNIQUE_FUNCTION<%0...%1> %0HU%1
		#endinput
	#endif
#elseif UNIQUE_SYMBOL < (1191)
	#if UNIQUE_SYMBOL == (1183)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1184)
		#define UNIQUE_FUNCTION<%0...%1> %0HV%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1184)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1185)
		#define UNIQUE_FUNCTION<%0...%1> %0HW%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1185)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1186)
		#define UNIQUE_FUNCTION<%0...%1> %0HX%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1186)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1187)
		#define UNIQUE_FUNCTION<%0...%1> %0HY%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1187)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1188)
		#define UNIQUE_FUNCTION<%0...%1> %0HZ%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1188)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1189)
		#define UNIQUE_FUNCTION<%0...%1> %0H_%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1189)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1190)
		#define UNIQUE_FUNCTION<%0...%1> %0Ha%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1191)
		#define UNIQUE_FUNCTION<%0...%1> %0Hb%1
		#endinput
	#endif
#elseif UNIQUE_SYMBOL < (1199)
	#if UNIQUE_SYMBOL == (1191)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1192)
		#define UNIQUE_FUNCTION<%0...%1> %0Hc%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1192)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1193)
		#define UNIQUE_FUNCTION<%0...%1> %0Hd%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1193)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1194)
		#define UNIQUE_FUNCTION<%0...%1> %0He%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1194)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1195)
		#define UNIQUE_FUNCTION<%0...%1> %0Hf%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1195)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1196)
		#define UNIQUE_FUNCTION<%0...%1> %0Hg%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1196)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1197)
		#define UNIQUE_FUNCTION<%0...%1> %0Hh%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1197)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1198)
		#define UNIQUE_FUNCTION<%0...%1> %0Hi%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1199)
		#define UNIQUE_FUNCTION<%0...%1> %0Hj%1
		#endinput
	#endif
#elseif UNIQUE_SYMBOL < (1207)
	#if UNIQUE_SYMBOL == (1199)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1200)
		#define UNIQUE_FUNCTION<%0...%1> %0Hk%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1200)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1201)
		#define UNIQUE_FUNCTION<%0...%1> %0Hl%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1201)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1202)
		#define UNIQUE_FUNCTION<%0...%1> %0Hm%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1202)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1203)
		#define UNIQUE_FUNCTION<%0...%1> %0Hn%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1203)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1204)
		#define UNIQUE_FUNCTION<%0...%1> %0Ho%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1204)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1205)
		#define UNIQUE_FUNCTION<%0...%1> %0Hp%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1205)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1206)
		#define UNIQUE_FUNCTION<%0...%1> %0Hq%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1207)
		#define UNIQUE_FUNCTION<%0...%1> %0Hr%1
		#endinput
	#endif
#else
	#if UNIQUE_SYMBOL == (1207)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1208)
		#define UNIQUE_FUNCTION<%0...%1> %0Hs%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1208)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1209)
		#define UNIQUE_FUNCTION<%0...%1> %0Ht%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1209)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1210)
		#define UNIQUE_FUNCTION<%0...%1> %0Hu%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1210)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1211)
		#define UNIQUE_FUNCTION<%0...%1> %0Hv%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1211)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1212)
		#define UNIQUE_FUNCTION<%0...%1> %0Hw%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1212)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1213)
		#define UNIQUE_FUNCTION<%0...%1> %0Hx%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1213)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1214)
		#define UNIQUE_FUNCTION<%0...%1> %0Hy%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1215)
		#define UNIQUE_FUNCTION<%0...%1> %0Hz%1
		#endinput
	#endif
#endif

