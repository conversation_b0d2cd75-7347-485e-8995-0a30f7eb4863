static stock const Y_UNIQUE_1216_1279_CALLED = UNIQUE_SYMBOL;

#if defined _inc_y_unique_1216_1279
	#undef _inc_y_unique_1216_1279
#endif

#if UNIQUE_SYMBOL < (1223)
	#if UNIQUE_SYMBOL == (1215)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1216)
		#define UNIQUE_FUNCTION<%0...%1> %0I0%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1216)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1217)
		#define UNIQUE_FUNCTION<%0...%1> %0I1%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1217)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1218)
		#define UNIQUE_FUNCTION<%0...%1> %0I2%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1218)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1219)
		#define UNIQUE_FUNCTION<%0...%1> %0I3%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1219)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1220)
		#define UNIQUE_FUNCTION<%0...%1> %0I4%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1220)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1221)
		#define UNIQUE_FUNCTION<%0...%1> %0I5%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1221)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1222)
		#define UNIQUE_FUNCTION<%0...%1> %0I6%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1223)
		#define UNIQUE_FUNCTION<%0...%1> %0I7%1
		#endinput
	#endif
#elseif UNIQUE_SYMBOL < (1231)
	#if UNIQUE_SYMBOL == (1223)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1224)
		#define UNIQUE_FUNCTION<%0...%1> %0I8%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1224)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1225)
		#define UNIQUE_FUNCTION<%0...%1> %0I9%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1225)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1226)
		#define UNIQUE_FUNCTION<%0...%1> %0I@%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1226)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1227)
		#define UNIQUE_FUNCTION<%0...%1> %0IA%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1227)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1228)
		#define UNIQUE_FUNCTION<%0...%1> %0IB%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1228)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1229)
		#define UNIQUE_FUNCTION<%0...%1> %0IC%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1229)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1230)
		#define UNIQUE_FUNCTION<%0...%1> %0ID%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1231)
		#define UNIQUE_FUNCTION<%0...%1> %0IE%1
		#endinput
	#endif
#elseif UNIQUE_SYMBOL < (1239)
	#if UNIQUE_SYMBOL == (1231)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1232)
		#define UNIQUE_FUNCTION<%0...%1> %0IF%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1232)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1233)
		#define UNIQUE_FUNCTION<%0...%1> %0IG%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1233)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1234)
		#define UNIQUE_FUNCTION<%0...%1> %0IH%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1234)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1235)
		#define UNIQUE_FUNCTION<%0...%1> %0II%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1235)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1236)
		#define UNIQUE_FUNCTION<%0...%1> %0IJ%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1236)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1237)
		#define UNIQUE_FUNCTION<%0...%1> %0IK%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1237)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1238)
		#define UNIQUE_FUNCTION<%0...%1> %0IL%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1239)
		#define UNIQUE_FUNCTION<%0...%1> %0IM%1
		#endinput
	#endif
#elseif UNIQUE_SYMBOL < (1247)
	#if UNIQUE_SYMBOL == (1239)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1240)
		#define UNIQUE_FUNCTION<%0...%1> %0IN%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1240)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1241)
		#define UNIQUE_FUNCTION<%0...%1> %0IO%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1241)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1242)
		#define UNIQUE_FUNCTION<%0...%1> %0IP%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1242)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1243)
		#define UNIQUE_FUNCTION<%0...%1> %0IQ%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1243)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1244)
		#define UNIQUE_FUNCTION<%0...%1> %0IR%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1244)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1245)
		#define UNIQUE_FUNCTION<%0...%1> %0IS%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1245)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1246)
		#define UNIQUE_FUNCTION<%0...%1> %0IT%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1247)
		#define UNIQUE_FUNCTION<%0...%1> %0IU%1
		#endinput
	#endif
#elseif UNIQUE_SYMBOL < (1255)
	#if UNIQUE_SYMBOL == (1247)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1248)
		#define UNIQUE_FUNCTION<%0...%1> %0IV%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1248)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1249)
		#define UNIQUE_FUNCTION<%0...%1> %0IW%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1249)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1250)
		#define UNIQUE_FUNCTION<%0...%1> %0IX%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1250)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1251)
		#define UNIQUE_FUNCTION<%0...%1> %0IY%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1251)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1252)
		#define UNIQUE_FUNCTION<%0...%1> %0IZ%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1252)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1253)
		#define UNIQUE_FUNCTION<%0...%1> %0I_%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1253)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1254)
		#define UNIQUE_FUNCTION<%0...%1> %0Ia%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1255)
		#define UNIQUE_FUNCTION<%0...%1> %0Ib%1
		#endinput
	#endif
#elseif UNIQUE_SYMBOL < (1263)
	#if UNIQUE_SYMBOL == (1255)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1256)
		#define UNIQUE_FUNCTION<%0...%1> %0Ic%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1256)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1257)
		#define UNIQUE_FUNCTION<%0...%1> %0Id%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1257)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1258)
		#define UNIQUE_FUNCTION<%0...%1> %0Ie%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1258)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1259)
		#define UNIQUE_FUNCTION<%0...%1> %0If%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1259)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1260)
		#define UNIQUE_FUNCTION<%0...%1> %0Ig%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1260)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1261)
		#define UNIQUE_FUNCTION<%0...%1> %0Ih%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1261)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1262)
		#define UNIQUE_FUNCTION<%0...%1> %0Ii%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1263)
		#define UNIQUE_FUNCTION<%0...%1> %0Ij%1
		#endinput
	#endif
#elseif UNIQUE_SYMBOL < (1271)
	#if UNIQUE_SYMBOL == (1263)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1264)
		#define UNIQUE_FUNCTION<%0...%1> %0Ik%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1264)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1265)
		#define UNIQUE_FUNCTION<%0...%1> %0Il%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1265)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1266)
		#define UNIQUE_FUNCTION<%0...%1> %0Im%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1266)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1267)
		#define UNIQUE_FUNCTION<%0...%1> %0In%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1267)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1268)
		#define UNIQUE_FUNCTION<%0...%1> %0Io%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1268)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1269)
		#define UNIQUE_FUNCTION<%0...%1> %0Ip%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1269)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1270)
		#define UNIQUE_FUNCTION<%0...%1> %0Iq%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1271)
		#define UNIQUE_FUNCTION<%0...%1> %0Ir%1
		#endinput
	#endif
#else
	#if UNIQUE_SYMBOL == (1271)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1272)
		#define UNIQUE_FUNCTION<%0...%1> %0Is%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1272)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1273)
		#define UNIQUE_FUNCTION<%0...%1> %0It%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1273)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1274)
		#define UNIQUE_FUNCTION<%0...%1> %0Iu%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1274)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1275)
		#define UNIQUE_FUNCTION<%0...%1> %0Iv%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1275)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1276)
		#define UNIQUE_FUNCTION<%0...%1> %0Iw%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1276)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1277)
		#define UNIQUE_FUNCTION<%0...%1> %0Ix%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1277)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1278)
		#define UNIQUE_FUNCTION<%0...%1> %0Iy%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1279)
		#define UNIQUE_FUNCTION<%0...%1> %0Iz%1
		#endinput
	#endif
#endif

