static stock const Y_UNIQUE_1280_1343_CALLED = UNIQUE_SYMBOL;

#if defined _inc_y_unique_1280_1343
	#undef _inc_y_unique_1280_1343
#endif

#if UNIQUE_SYMBOL < (1287)
	#if UNIQUE_SYMBOL == (1279)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1280)
		#define UNIQUE_FUNCTION<%0...%1> %0J0%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1280)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1281)
		#define UNIQUE_FUNCTION<%0...%1> %0J1%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1281)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1282)
		#define UNIQUE_FUNCTION<%0...%1> %0J2%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1282)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1283)
		#define UNIQUE_FUNCTION<%0...%1> %0J3%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1283)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1284)
		#define UNIQUE_FUNCTION<%0...%1> %0J4%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1284)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1285)
		#define UNIQUE_FUNCTION<%0...%1> %0J5%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1285)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1286)
		#define UNIQUE_FUNCTION<%0...%1> %0J6%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1287)
		#define UNIQUE_FUNCTION<%0...%1> %0J7%1
		#endinput
	#endif
#elseif UNIQUE_SYMBOL < (1295)
	#if UNIQUE_SYMBOL == (1287)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1288)
		#define UNIQUE_FUNCTION<%0...%1> %0J8%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1288)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1289)
		#define UNIQUE_FUNCTION<%0...%1> %0J9%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1289)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1290)
		#define UNIQUE_FUNCTION<%0...%1> %0J@%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1290)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1291)
		#define UNIQUE_FUNCTION<%0...%1> %0JA%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1291)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1292)
		#define UNIQUE_FUNCTION<%0...%1> %0JB%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1292)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1293)
		#define UNIQUE_FUNCTION<%0...%1> %0JC%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1293)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1294)
		#define UNIQUE_FUNCTION<%0...%1> %0JD%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1295)
		#define UNIQUE_FUNCTION<%0...%1> %0JE%1
		#endinput
	#endif
#elseif UNIQUE_SYMBOL < (1303)
	#if UNIQUE_SYMBOL == (1295)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1296)
		#define UNIQUE_FUNCTION<%0...%1> %0JF%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1296)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1297)
		#define UNIQUE_FUNCTION<%0...%1> %0JG%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1297)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1298)
		#define UNIQUE_FUNCTION<%0...%1> %0JH%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1298)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1299)
		#define UNIQUE_FUNCTION<%0...%1> %0JI%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1299)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1300)
		#define UNIQUE_FUNCTION<%0...%1> %0JJ%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1300)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1301)
		#define UNIQUE_FUNCTION<%0...%1> %0JK%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1301)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1302)
		#define UNIQUE_FUNCTION<%0...%1> %0JL%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1303)
		#define UNIQUE_FUNCTION<%0...%1> %0JM%1
		#endinput
	#endif
#elseif UNIQUE_SYMBOL < (1311)
	#if UNIQUE_SYMBOL == (1303)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1304)
		#define UNIQUE_FUNCTION<%0...%1> %0JN%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1304)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1305)
		#define UNIQUE_FUNCTION<%0...%1> %0JO%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1305)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1306)
		#define UNIQUE_FUNCTION<%0...%1> %0JP%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1306)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1307)
		#define UNIQUE_FUNCTION<%0...%1> %0JQ%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1307)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1308)
		#define UNIQUE_FUNCTION<%0...%1> %0JR%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1308)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1309)
		#define UNIQUE_FUNCTION<%0...%1> %0JS%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1309)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1310)
		#define UNIQUE_FUNCTION<%0...%1> %0JT%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1311)
		#define UNIQUE_FUNCTION<%0...%1> %0JU%1
		#endinput
	#endif
#elseif UNIQUE_SYMBOL < (1319)
	#if UNIQUE_SYMBOL == (1311)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1312)
		#define UNIQUE_FUNCTION<%0...%1> %0JV%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1312)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1313)
		#define UNIQUE_FUNCTION<%0...%1> %0JW%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1313)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1314)
		#define UNIQUE_FUNCTION<%0...%1> %0JX%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1314)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1315)
		#define UNIQUE_FUNCTION<%0...%1> %0JY%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1315)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1316)
		#define UNIQUE_FUNCTION<%0...%1> %0JZ%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1316)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1317)
		#define UNIQUE_FUNCTION<%0...%1> %0J_%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1317)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1318)
		#define UNIQUE_FUNCTION<%0...%1> %0Ja%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1319)
		#define UNIQUE_FUNCTION<%0...%1> %0Jb%1
		#endinput
	#endif
#elseif UNIQUE_SYMBOL < (1327)
	#if UNIQUE_SYMBOL == (1319)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1320)
		#define UNIQUE_FUNCTION<%0...%1> %0Jc%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1320)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1321)
		#define UNIQUE_FUNCTION<%0...%1> %0Jd%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1321)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1322)
		#define UNIQUE_FUNCTION<%0...%1> %0Je%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1322)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1323)
		#define UNIQUE_FUNCTION<%0...%1> %0Jf%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1323)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1324)
		#define UNIQUE_FUNCTION<%0...%1> %0Jg%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1324)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1325)
		#define UNIQUE_FUNCTION<%0...%1> %0Jh%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1325)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1326)
		#define UNIQUE_FUNCTION<%0...%1> %0Ji%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1327)
		#define UNIQUE_FUNCTION<%0...%1> %0Jj%1
		#endinput
	#endif
#elseif UNIQUE_SYMBOL < (1335)
	#if UNIQUE_SYMBOL == (1327)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1328)
		#define UNIQUE_FUNCTION<%0...%1> %0Jk%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1328)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1329)
		#define UNIQUE_FUNCTION<%0...%1> %0Jl%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1329)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1330)
		#define UNIQUE_FUNCTION<%0...%1> %0Jm%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1330)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1331)
		#define UNIQUE_FUNCTION<%0...%1> %0Jn%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1331)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1332)
		#define UNIQUE_FUNCTION<%0...%1> %0Jo%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1332)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1333)
		#define UNIQUE_FUNCTION<%0...%1> %0Jp%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1333)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1334)
		#define UNIQUE_FUNCTION<%0...%1> %0Jq%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1335)
		#define UNIQUE_FUNCTION<%0...%1> %0Jr%1
		#endinput
	#endif
#else
	#if UNIQUE_SYMBOL == (1335)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1336)
		#define UNIQUE_FUNCTION<%0...%1> %0Js%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1336)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1337)
		#define UNIQUE_FUNCTION<%0...%1> %0Jt%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1337)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1338)
		#define UNIQUE_FUNCTION<%0...%1> %0Ju%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1338)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1339)
		#define UNIQUE_FUNCTION<%0...%1> %0Jv%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1339)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1340)
		#define UNIQUE_FUNCTION<%0...%1> %0Jw%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1340)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1341)
		#define UNIQUE_FUNCTION<%0...%1> %0Jx%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1341)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1342)
		#define UNIQUE_FUNCTION<%0...%1> %0Jy%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1343)
		#define UNIQUE_FUNCTION<%0...%1> %0Jz%1
		#endinput
	#endif
#endif

