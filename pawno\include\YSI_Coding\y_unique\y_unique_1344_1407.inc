static stock const Y_UNIQUE_1344_1407_CALLED = UNIQUE_SYMBOL;

#if defined _inc_y_unique_1344_1407
	#undef _inc_y_unique_1344_1407
#endif

#if UNIQUE_SYMBOL < (1351)
	#if UNIQUE_SYMBOL == (1343)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1344)
		#define UNIQUE_FUNCTION<%0...%1> %0K0%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1344)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1345)
		#define UNIQUE_FUNCTION<%0...%1> %0K1%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1345)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1346)
		#define UNIQUE_FUNCTION<%0...%1> %0K2%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1346)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1347)
		#define UNIQUE_FUNCTION<%0...%1> %0K3%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1347)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1348)
		#define UNIQUE_FUNCTION<%0...%1> %0K4%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1348)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1349)
		#define UNIQUE_FUNCTION<%0...%1> %0K5%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1349)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1350)
		#define UNIQUE_FUNCTION<%0...%1> %0K6%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1351)
		#define UNIQUE_FUNCTION<%0...%1> %0K7%1
		#endinput
	#endif
#elseif UNIQUE_SYMBOL < (1359)
	#if UNIQUE_SYMBOL == (1351)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1352)
		#define UNIQUE_FUNCTION<%0...%1> %0K8%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1352)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1353)
		#define UNIQUE_FUNCTION<%0...%1> %0K9%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1353)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1354)
		#define UNIQUE_FUNCTION<%0...%1> %0K@%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1354)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1355)
		#define UNIQUE_FUNCTION<%0...%1> %0KA%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1355)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1356)
		#define UNIQUE_FUNCTION<%0...%1> %0KB%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1356)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1357)
		#define UNIQUE_FUNCTION<%0...%1> %0KC%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1357)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1358)
		#define UNIQUE_FUNCTION<%0...%1> %0KD%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1359)
		#define UNIQUE_FUNCTION<%0...%1> %0KE%1
		#endinput
	#endif
#elseif UNIQUE_SYMBOL < (1367)
	#if UNIQUE_SYMBOL == (1359)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1360)
		#define UNIQUE_FUNCTION<%0...%1> %0KF%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1360)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1361)
		#define UNIQUE_FUNCTION<%0...%1> %0KG%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1361)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1362)
		#define UNIQUE_FUNCTION<%0...%1> %0KH%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1362)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1363)
		#define UNIQUE_FUNCTION<%0...%1> %0KI%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1363)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1364)
		#define UNIQUE_FUNCTION<%0...%1> %0KJ%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1364)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1365)
		#define UNIQUE_FUNCTION<%0...%1> %0KK%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1365)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1366)
		#define UNIQUE_FUNCTION<%0...%1> %0KL%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1367)
		#define UNIQUE_FUNCTION<%0...%1> %0KM%1
		#endinput
	#endif
#elseif UNIQUE_SYMBOL < (1375)
	#if UNIQUE_SYMBOL == (1367)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1368)
		#define UNIQUE_FUNCTION<%0...%1> %0KN%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1368)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1369)
		#define UNIQUE_FUNCTION<%0...%1> %0KO%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1369)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1370)
		#define UNIQUE_FUNCTION<%0...%1> %0KP%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1370)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1371)
		#define UNIQUE_FUNCTION<%0...%1> %0KQ%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1371)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1372)
		#define UNIQUE_FUNCTION<%0...%1> %0KR%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1372)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1373)
		#define UNIQUE_FUNCTION<%0...%1> %0KS%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1373)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1374)
		#define UNIQUE_FUNCTION<%0...%1> %0KT%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1375)
		#define UNIQUE_FUNCTION<%0...%1> %0KU%1
		#endinput
	#endif
#elseif UNIQUE_SYMBOL < (1383)
	#if UNIQUE_SYMBOL == (1375)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1376)
		#define UNIQUE_FUNCTION<%0...%1> %0KV%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1376)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1377)
		#define UNIQUE_FUNCTION<%0...%1> %0KW%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1377)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1378)
		#define UNIQUE_FUNCTION<%0...%1> %0KX%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1378)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1379)
		#define UNIQUE_FUNCTION<%0...%1> %0KY%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1379)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1380)
		#define UNIQUE_FUNCTION<%0...%1> %0KZ%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1380)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1381)
		#define UNIQUE_FUNCTION<%0...%1> %0K_%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1381)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1382)
		#define UNIQUE_FUNCTION<%0...%1> %0Ka%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1383)
		#define UNIQUE_FUNCTION<%0...%1> %0Kb%1
		#endinput
	#endif
#elseif UNIQUE_SYMBOL < (1391)
	#if UNIQUE_SYMBOL == (1383)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1384)
		#define UNIQUE_FUNCTION<%0...%1> %0Kc%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1384)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1385)
		#define UNIQUE_FUNCTION<%0...%1> %0Kd%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1385)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1386)
		#define UNIQUE_FUNCTION<%0...%1> %0Ke%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1386)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1387)
		#define UNIQUE_FUNCTION<%0...%1> %0Kf%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1387)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1388)
		#define UNIQUE_FUNCTION<%0...%1> %0Kg%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1388)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1389)
		#define UNIQUE_FUNCTION<%0...%1> %0Kh%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1389)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1390)
		#define UNIQUE_FUNCTION<%0...%1> %0Ki%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1391)
		#define UNIQUE_FUNCTION<%0...%1> %0Kj%1
		#endinput
	#endif
#elseif UNIQUE_SYMBOL < (1399)
	#if UNIQUE_SYMBOL == (1391)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1392)
		#define UNIQUE_FUNCTION<%0...%1> %0Kk%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1392)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1393)
		#define UNIQUE_FUNCTION<%0...%1> %0Kl%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1393)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1394)
		#define UNIQUE_FUNCTION<%0...%1> %0Km%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1394)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1395)
		#define UNIQUE_FUNCTION<%0...%1> %0Kn%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1395)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1396)
		#define UNIQUE_FUNCTION<%0...%1> %0Ko%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1396)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1397)
		#define UNIQUE_FUNCTION<%0...%1> %0Kp%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1397)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1398)
		#define UNIQUE_FUNCTION<%0...%1> %0Kq%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1399)
		#define UNIQUE_FUNCTION<%0...%1> %0Kr%1
		#endinput
	#endif
#else
	#if UNIQUE_SYMBOL == (1399)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1400)
		#define UNIQUE_FUNCTION<%0...%1> %0Ks%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1400)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1401)
		#define UNIQUE_FUNCTION<%0...%1> %0Kt%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1401)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1402)
		#define UNIQUE_FUNCTION<%0...%1> %0Ku%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1402)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1403)
		#define UNIQUE_FUNCTION<%0...%1> %0Kv%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1403)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1404)
		#define UNIQUE_FUNCTION<%0...%1> %0Kw%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1404)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1405)
		#define UNIQUE_FUNCTION<%0...%1> %0Kx%1
		#endinput
	#elseif UNIQUE_SYMBOL == (1405)
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1406)
		#define UNIQUE_FUNCTION<%0...%1> %0Ky%1
		#endinput
	#else
		#undef UNIQUE_SYMBOL
		#define UNIQUE_SYMBOL (1407)
		#define UNIQUE_FUNCTION<%0...%1> %0Kz%1
		#endinput
	#endif
#endif

